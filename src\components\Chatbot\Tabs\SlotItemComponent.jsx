// SlotItemComponent.jsx
import DragIcon from '../../Global/Icons/DragIcon';
import EditIcon from '../../Global/Icons/EditIcon';
import DeleteIcon from '../../Global/Icons/DeleteIcon';
import DButton from '@/components/Global/DButton';

const SlotItemComponent = ({ item, dragHandleProps, handleEdit, handleDelete }) => {
  return (
  <div className="flex items-center justify-between p-2">
    <div className="flex items-center gap-2">
      <div {...dragHandleProps}>
        <DragIcon />
      </div>
      <p>{item.title}</p>
      {item.disclaimer && <span className="text-xs tracking-tight text-grey-50">({item.disclaimer})</span>}
    </div>
    <div className="flex items-center gap-size2">
      <DButton variant='outlined' onClick={() => handleEdit(item)}>
        <EditIcon />
      </DButton>
      <DButton variant="grey" onClick={() => handleDelete(item)}>
        <DeleteIcon />
      </DButton>
    </div>
  </div>
  );
};

export default SlotItemComponent;
