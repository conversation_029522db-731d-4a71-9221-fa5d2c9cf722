/**
 * Converts a hex color code to an RGB array.
 *
 * @param {string} hex - The hex color code (e.g., '#RRGGBB' or 'RRGGBB').
 * @returns {number[] | null} An array with RGB values [r, g, b], or null if the input is invalid.
 */
const hexToRgb = (hex) => {
  if (!hex) return null;
  const matches = hex.match(/\w\w/g);
  if (!matches || matches.length !== 3) return null;
  return matches.map((s) => parseInt(s, 16));
};

/**
 * Calculates the brightness of a color based on its hex value.
 *
 * Uses a formula that weights RGB values according to their perceived brightness.
 *
 * @param {string} color - The hex color code (e.g., '#RRGGBB' or 'RRGGBB').
 * @returns {number | null} The brightness value, or null if the color is invalid.
 */
const getBrightnessColor = (color) => {
  if (!color) return null;
  const rgb = hexToRgb(color);
  if (!rgb) return null;
  const [r, g, b] = rgb;
  return (r * 299 + g * 587 + b * 114) / 1000;
}

export default getBrightnessColor;
