import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import useModalStore from '@/stores/modal/modalStore';

/**
 * RouteChangeHandler component
 * 
 * This component monitors route changes and performs cleanup actions when the route changes
 * - Closes any open inline plans modals
 * - Can be extended to handle other route change cleanup tasks
 */
const RouteChangeHandler = () => {
  const location = useLocation();
  const closeInlinePlansModal = useModalStore(state => state.closeInlinePlansModal);
  const closePlansModal = useModalStore(state => state.closePlansModal);

  useEffect(() => {
    // Reset modal states when route changes
    closeInlinePlansModal();
    closePlansModal();
    
    // This effect runs on every location change
  }, [location.pathname, closeInlinePlansModal, closePlansModal]);

  // This component doesn't render anything
  return null;
};

export default RouteChangeHandler; 