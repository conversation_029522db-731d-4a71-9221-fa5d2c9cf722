import path from 'path';
import { fileURLToPath } from 'url';
import { defineConfig } from 'vite';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

export default defineConfig({
  plugins: [cssInjectedByJsPlugin({ styleId: 'dante-embed-styles' })],
  build: {
    emptyOutDir: false,
    rollupOptions: {
      input: {
        index: path.resolve(dirname, 'avatar-bubble-embed/src/index.js')
      },
      output: [
        {
          manualChunks: undefined,
          entryFileNames: 'avatar-bubble-embed.js'
        },
        {
          manualChunks: undefined,
          entryFileNames: 'avatar-bubble-embed.min.js'
        }
      ]
    },
    outDir: './public'
  },
  publicDir: 'assets'
});
