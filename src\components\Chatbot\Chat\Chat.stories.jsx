import { DANTE_THEME_COLOR_CHAT } from '@/constants';
import {
  fakerInitialMessages,
  fakerLoadingConversation,
  fakerLongConversation,
  fakerShortConversation,
  fakerSuggestionsPrompts
} from '@/helpers/stories/generateChatMessages';
import { fn } from '@storybook/test';

import Chat from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Chatbot/Chat',
  component: Chat,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'fullscreen'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {
    isDanteFaq: false,
    hiddenConversation: false,
    showBookMeeting: false,
    showLiveAgent: false,
    hiddenLlmSelector: false,
    hiddenPoweredByDante: false,
    messages: fakerInitialMessages,
    suggestionPrompts: [],
    customization: DANTE_THEME_COLOR_CHAT,
    handleCloseButton: fn(),
    handleOpenDanteConversations: fn(),
    showInAppHeader: false
  },
  decorators: [
    (Story) => (
      <div className="h-screen">
        <Story />
      </div>
    )
  ]
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    customization: DANTE_THEME_COLOR_CHAT,
    messages: fakerInitialMessages,
    suggestionPrompts: fakerSuggestionsPrompts,
    hiddenConversation: false
  }
};
export const SmallConversation = {
  args: {
    customization: DANTE_THEME_COLOR_CHAT,
    messages: fakerShortConversation,
    hiddenConversation: false
  }
};
export const SmallConversationRemoveLlmSelector = {
  args: {
    customization: DANTE_THEME_COLOR_CHAT,
    messages: fakerShortConversation,
    hiddenConversation: false,
    hiddenLlmSelector: true
  }
};
export const SmallConversationRemovePoweredByDante = {
  args: {
    customization: DANTE_THEME_COLOR_CHAT,
    messages: fakerShortConversation,
    hiddenConversation: false,
    hiddenPoweredByDante: true
  }
};
export const SmallConversationBookMeeting = {
  args: {
    customization: DANTE_THEME_COLOR_CHAT,
    messages: fakerShortConversation,
    suggestionPrompts: fakerSuggestionsPrompts,
    hiddenConversation: false,
    showBookMeeting: true
  }
};
export const SmallConversationBookMeetingAndLiveAgent = {
  args: {
    customization: DANTE_THEME_COLOR_CHAT,
    messages: fakerShortConversation,
    suggestionPrompts: fakerSuggestionsPrompts,
    hiddenConversation: false,
    showBookMeeting: true,
    showLiveAgent: true
  }
};
export const LoadingMessage = {
  args: {
    customization: DANTE_THEME_COLOR_CHAT,
    messages: fakerLoadingConversation,
    hiddenConversation: false
  }
};
export const LongConversation = {
  decorators: [],
  args: {
    customization: DANTE_THEME_COLOR_CHAT,
    messages: fakerLongConversation,
    suggestionPrompts: fakerSuggestionsPrompts,
    hiddenConversation: false
  }
};

export const ChatDanteFAQInitial = {
  decorators: [],
  args: {
    messages: [],
    hiddenConversation: true,
    showInAppHeader: true
  }
};

export const ChatDanteFAQActive = {
  args: {
    ...ChatDanteFAQInitial.args,
    hiddenConversation: false,
    showInAppHeader: true
  }
};
