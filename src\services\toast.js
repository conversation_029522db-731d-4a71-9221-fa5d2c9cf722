import toast from 'react-hot-toast';
import { STATUS } from '@/constants';

const toastService = {
  addToast: ({ message, type }) => {
    const options = {
      duration: 5000,
      position: 'top-center',
    };

    switch (type) {
      case STATUS.SUCCESS:
        return toast.success(message, options);
      case STATUS.FAILURE:
      case STATUS.ERROR:
        return toast.error(message, options);
      case STATUS.WARNING:
        return toast(message, {
          ...options,
          icon: '⚠️',
        });
      default:
        return toast(message, options);
    }
  },

  removeToast: (toastId) => {
    toast.dismiss(toastId);
  },

  removeAllToasts: () => {
    toast.dismiss();
  },
};

export default toastService; 