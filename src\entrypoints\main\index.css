@import '../../styles/common.css';
@tailwind base;
@tailwind components;
@tailwind utilities;


/* https://tailwindcss.com/docs/customizing-colors#using-css-variables */

[data-mode='dark'],
.dark {
  --color-purple-300: 112, 30, 245;
  --color-purple-opacity: 130, 117, 247;

  --color-purple-200: 82, 11, 240;

  --color-green-400: 128, 212, 89;
  --color-green-300: 14, 217, 9;
  --color-green-200: 225, 246, 225;
  --color-green-5: 14, 217, 9;

  --color-negative-100: 255, 53, 40;

  --color-orange-300: 255, 156, 40;
  --color-orange-200: 249, 239, 227;
  --color-orange-5: 255, 156, 40;

  --color-grey-100: 13, 11, 43;
  --color-grey-opacity: 255, 255, 255;

  --color-yellow-300: 251, 255, 47;

  --color-black: 255, 255, 255;
  --color-white: 9, 8, 31;
  --color-black-opacity: 0.85;
  --color-oklch-100: 0.176 0.0601 286.4;
}

/* Override autofill styles for dark mode */
[data-mode='dark'] input:-webkit-autofill,
[data-mode='dark'] input:-webkit-autofill:hover,
[data-mode='dark'] input:-webkit-autofill:focus,
[data-mode='dark'] input:-webkit-autofill:active,
.dark input:-webkit-autofill,
.dark input:-webkit-autofill:hover,
.dark input:-webkit-autofill:focus,
.dark input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px rgba(9, 8, 31, 1) inset !important;
  box-shadow: 0 0 0 30px rgba(9, 8, 31, 1) inset !important;
  -webkit-text-fill-color: rgba(255, 255, 255, 1) !important;
  color: rgba(255, 255, 255, 1) !important;
}

/* dashboards */
.dashboard_wrapper .dashboard-grid_content {
  flex-grow: 1;
}

.dashboard-grid_content {
  grid-template-columns: repeat(auto-fit, minmax(200px, 300px));
  grid-auto-rows: max-content;
}

.dashboard-grid_content.grid-voice {
  grid-template-columns: repeat(auto-fit, minmax(200px, 270px));
  grid-auto-rows: max-content;
}

.dashboard_wrapper::-webkit-scrollbar {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  width: 8px;
}

.dashboard_wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.dashboard-create_btn {
  max-width: 300px;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  /* border-radius: 24px; */
}

@media (max-width: 640px) {
  .dashboard-grid_content {
    grid-template-columns: 1fr;
    grid-auto-columns: max-content;
  }
  .dashboard-create_btn {
    max-width: 100%;
  }
}
.range-input::-webkit-slider-thumb {
  background-color: var(--dt-color-element-100);
}


#onetrust-consent-sdk{
  display: none !important;
}
