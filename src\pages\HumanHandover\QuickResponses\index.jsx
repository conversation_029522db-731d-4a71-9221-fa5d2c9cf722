import DButton from '@/components/Global/DButton';
import AddIcon from '@/components/Global/Icons/AddIcon';
import { useEffect, useState } from 'react';
import AddQuickResponse from './AddQuickResponse';
import QuickResponse from '@/components/QuickResponse';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import EditQuickResponse from './EditQuickResponse';
import * as humanHandoverService from '@/services/humanHandover';
import { useParams } from 'react-router-dom';
import useToast from '@/hooks/useToast';
import useDanteApi from '@/hooks/useDanteApi';
import DLoading from '@/components/DLoading';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';

const HumanHandoverQuickResponses = () => {
  const { organization_id } = useParams();
  const { updateQuickResponses } = useHumanHandoverStore((state) => state);
  const {
    data: dataQuickResponses,
    isLoading: isLoadingQuickResponses,
    refetch: refetchQuickResponses,
  } = useDanteApi(
    humanHandoverService.getQuickResponses,
    [],
    {},
    organization_id
  );

  const { addSuccessToast } = useToast();

  const [quickResponses, setQuickResponses] = useState([]);
  const [openAdd, setOpenAdd] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [editResponse, setEditResponse] = useState(null);
  const [deleteResponse, setDeleteResponse] = useState(null);
  const [isLoadingCreateQuickResponse, setIsLoadingCreateQuickResponse] = useState(false);
  const [isLoadingEditQuickResponse, setIsLoadingEditQuickResponse] = useState(false);
  const handleCreateQuickResponse = async (responseTitle, responseContent) => {
    setIsLoadingCreateQuickResponse(true);
    try {
      const response = await humanHandoverService.createQuickResponse({
        organization_id,
        title: responseTitle,
        content: responseContent,
      });
      if (response.status === 200) {
        addSuccessToast({
          message: 'Quick response created successfully',
        });
        refetchQuickResponses();
        setOpenAdd(false);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoadingCreateQuickResponse(false);
    }
  };

  const handleEditQuickResponse = async (responseTitle, responseContent) => {
    setIsLoadingEditQuickResponse(true);
    try {
      const response = await humanHandoverService.updateQuickResponse(
        editResponse.id,
        { title: responseTitle, content: responseContent }
      );
      if (response.status === 200) {
        addSuccessToast({
          message: 'Quick response updated successfully',
        });
        refetchQuickResponses();
        setOpenEdit(false);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoadingEditQuickResponse(false);
    }
  };

  const handleDeleteQuickResponse = async () => {
    try {
      const response = await humanHandoverService.deleteQuickResponse(
        deleteResponse.id
      );
      if (response.status === 200) {
        addSuccessToast({
          message: 'Quick response deleted successfully',
        });
        refetchQuickResponses();
        setOpenDelete(false);
        setDeleteResponse(null);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (dataQuickResponses?.results?.length > 0) {
      setQuickResponses(dataQuickResponses?.results);
      updateQuickResponses(dataQuickResponses?.results);
    }
  }, [dataQuickResponses]);

  if (isLoadingQuickResponses) {
    return <DLoading show={true} />;
  }

  return (
    <div className="bg-white h-[1px] grow p-size3 md:p-size5 flex flex-col gap-size4 md:gap-size5 overflow-y-auto">
      <p className="text-lg md:text-2xl font-medium">Your quick responses</p>
      <div className="flex border border-grey-5 rounded-size1 p-size3 h-full">
        <div className="flex flex-col gap-size3 w-full">
          {quickResponses.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-size3">
              {quickResponses.map((response) => (
                <QuickResponse
                  key={response.title}
                  title={response.title}
                  description={response.content}
                  onDelete={() => {
                    setOpenDelete(true);
                    setDeleteResponse(response);
                  }}
                  onEdit={() => {
                    setOpenEdit(true);
                    setEditResponse(response);
                  }}
                />
              ))}
            </div>
          ) : (
            <div className="flex flex-col gap-size1 items-center justify-center h-full w-full">
              <p className="text-sm font-medium text-grey-50">
                There are no responses created yet
              </p>
              <DButton variant="grey" onClick={() => setOpenAdd(true)}>
                <AddIcon className="w-4 h-4" />
                Add Response
              </DButton>
            </div>
          )}
          {quickResponses.length > 0 && (
            <DButton variant="dark" onClick={() => setOpenAdd(true)}>
              <AddIcon className="w-4 h-4" />
              Add Response
            </DButton>
          )}
        </div>
      </div>
      <AddQuickResponse
        open={openAdd}
        onClose={() => setOpenAdd(false)}
        handleCreateQuickResponse={handleCreateQuickResponse}
        isLoading={isLoadingCreateQuickResponse}
      />
      <EditQuickResponse
        open={openEdit}
        onClose={() => setOpenEdit(false)}
        handleEditQuickResponse={handleEditQuickResponse}
        response={editResponse}
        isLoading={isLoadingEditQuickResponse}
      />
      <DConfirmationModal
        title="Delete quick response"
        description="Are you sure you want to delete this quick response? This action cannot be undone."
        open={openDelete}
        onClose={() => setOpenDelete(false)}
        onConfirm={handleDeleteQuickResponse}
        confirmText="Delete"
        cancelText="Cancel"
        variantConfirm="danger"
      />
    </div>
  );
};

export default HumanHandoverQuickResponses;
