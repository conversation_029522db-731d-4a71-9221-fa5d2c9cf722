function deepCompare(obj1, obj2) {
  // Check if both are objects
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 === null || obj2 === null) {
    return obj1 === obj2; // Compare primitive values or null
  }

  // Get keys from both objects
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  // Check if the number of keys is different
  if (keys1.length !== keys2.length) return false;

  // Compare keys and values
  for (const key of keys1) {
    if (!keys2.includes(key)) return false; // Check if the key exists in obj2

    const value1 = obj1[key];
    const value2 = obj2[key];

    // If it's an array, compare as arrays
    if (Array.isArray(value1) && Array.isArray(value2)) {
      if (!compareArrays(value1, value2)) return false;
    } else if (!deepCompare(value1, value2)) {
      // Otherwise, compare recursively
      return false;
    }
  }

  return true;
}

function compareArrays(array1, array2) {
  if (array1.length !== array2.length) return false;

  // Normalize array objects into sorted JSON strings for deep comparison
  const normalized1 = array1.map((item) => JSON.stringify(item)).sort();
  const normalized2 = array2.map((item) => JSON.stringify(item)).sort();

  return normalized1.every((value, index) => value === normalized2[index]);
}

export default deepCompare;