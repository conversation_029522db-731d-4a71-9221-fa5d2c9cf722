import React, { useEffect, useRef, useState } from 'react';
import DButton from '../Global/DButton';
import DSelect from '../Global/DSelect';
import LayoutOnBoarding from './LayoutOnBoarding';
import { SignUpStepsEnum } from './signUpSteps';
import ChevronDownIcon from '../Global/Icons/ChevronDownIcon';
import DInputBlock from '../Global/DInput/DInputBlock';
import DInput from '../Global/DInput/DInput';
import AddImageIcon from '../Global/Icons/AddImageIcon';
import AddIcon from '../Global/Icons/AddIcon';
import DeleteIcon from '../Global/Icons/DeleteIcon';
import DTransition from '../Global/DTransition';
import { useUserStore } from '@/stores/user/userStore';
import useDanteApi from '@/hooks/useDanteApi';
import {
  getDefaultImages,
  createTeamOnboarding,
  getTeamRoles,
} from '@/services/teamManagement.service';
import * as userService from '@/services/user.service';
import validateEmail from '@/helpers/validateEmail';
import clsx from 'clsx';
import DProfileImage from '../DProfileImage';
import { useNavigate } from 'react-router-dom';

const RegisterTeam = ({
  handleChange,
  setCanSubmit,
  setForm,
  setCurrentStep,
  teamData,
  setTeamData,
}) => {
  const user = useUserStore((state) => state.user);
  const navigate = useNavigate();

  // Refs and state
  const teamImageRef = useRef(null);
  const { data: defaultTeamImages } = useDanteApi(getDefaultImages);
  const [teamImageUrl, setTeamImageUrl] = useState(
    defaultTeamImages?.results[0]?.big_image_url
  );
  const [teamImage, setTeamImage] = useState(null);
  const [teamName, setTeamName] = useState('');
  // const [teammates, setTeammates] = useState([{ email: '', role_id: '' }]);
  const [errors, setErrors] = useState({});
  const [isCreateTeam, setIsCreateTeam] = useState(false);
  const formData = new FormData();
  const [isLoading, setIsLoading] = useState(false);
  const [buttonText, setButtonText] = useState('');
  const [teammateErrors, setTeammateErrors] = useState({});

  const { data: roles, isLoading: isLoadingRoles } = useDanteApi(getTeamRoles);

  const setTeammates = (teammates) => {
    setTeamData({ ...teamData, teammates });
  };

  const handleTeammateChange = (index, field, value) => {
    let error = '';
    const updatedTeammates = [...teamData?.teammates];
    updatedTeammates[index][field] = value;
    if (field === 'email' && value && !validateEmail(value)) {
      error = 'Invalid email';
    } else if (
      validateEmail(updatedTeammates[index].email) &&
      !updatedTeammates[index].role_id
    ) {
      error = 'Role is required';
    }
    setTeammateErrors({ ...teammateErrors, [index]: error });
    setTeammates(updatedTeammates);
  };

  const handleDeleteTeammate = (index) => {
    const updatedTeammates = teamData?.teammates?.filter((_, i) => i !== index);
    setTeammates(updatedTeammates);
    setTeammateErrors({ ...teammateErrors, [index]: '' });
    setErrors({ ...errors, teammates: '' });
  };

  const handleAddTeammate = () => {
    if (teamData?.teammates?.length < 3) {
      setTeammates([
        ...teamData?.teammates,
        {
          email: '',
          role_id: import.meta.env.VITE_APP_DANTE_SIGNUP_TEAM_ROLE_ID,
          max_credits_available: import.meta.env
            .VITE_APP_DANTE_SIGNUP_TEAM_MAX_CREDITS,
        },
      ]);
      setErrors({ ...errors, teammates: '' });
    } else {
      setErrors({ ...errors, teammates: 'You can invite up to 3 teammates' });
    }
  };

  const validateTeam = async () => {
    const newErrors = {};
    let hasErrors = false;

    if (!teamName) {
      newErrors.teamName = 'Team name is required';
      hasErrors = true;
    }

    if (!teamImageUrl && !teamImage) {
      newErrors.teamImageUrl = 'Team image is required';
      hasErrors = true;
    }

    return { hasErrors, newErrors };
  };

  const changeOnboarding = async () => {
    try {
      await userService.updateOnboardingStatus({
        onboarding_completed: true,
      });
    } catch (error) {
      console.error('Error changing onboarding:', error);
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    setErrors({});

    if (!isCreateTeam) {
      changeOnboarding();
      setIsLoading(false);
      navigate('/');
    }

    const { hasErrors, newErrors } = await validateTeam();
    setErrors(newErrors);

    if (hasErrors) {
      setIsLoading(false);
      return;
    }

    try {
      let teamIcon = teamImageUrl;

      if (teamImage instanceof File) {
        formData.append('file', teamImage);
        const response = await userService.uploadFile(formData);
        if (response.status === 200) {
          teamIcon = response?.data?.url;
        }
        formData.delete('file');
      }

      const response = await createTeamOnboarding({
        name: teamName,
        team_icon: teamIcon,
        members: teamData?.teammates?.filter((teammate) => teammate.email),
      });

      if (response.status === 200) {
        setTeamData({ ...teamData, ...response.data });
        changeOnboarding();
        navigate('/');
      }
    } catch (error) {
      console.error('Error creating team:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageChange = (file, ...rest) => {
    if (file instanceof File) {
      setTeamImage(file);
      setTeamImageUrl(URL.createObjectURL(file));
    } else {
      setTeamImage(null);
      setTeamImageUrl(file);
    }
    handleChange({ team_icon: file }, 'teamData');
  };

  useEffect(() => {
    const isTeamValid =
      (teamData?.teammates?.length > 0 && teamData?.teammates[0]?.email) ||
      teamName ||
      teamImageUrl;

    const buttonLabel = isTeamValid
      ? teamData?.teammates?.length > 0 && teamData?.teammates[0]?.email
        ? 'Send invites and continue'
        : 'Continue'
      : 'Continue and invite later';
    setButtonText(buttonLabel);
    setIsCreateTeam(isTeamValid);
  }, [teamData?.teammates, teamName, teamImageUrl]);

  return (
    <>
      <DTransition show={roles?.results?.length > 0}>
        <LayoutOnBoarding
          title="Build the future as a team"
          alert={'Share access and collaborate with team members to manage your account efficiently'}
        >
          <DProfileImage
            label="Team icon"
            name="teamImageUrl"
            required={isCreateTeam}
            defaultImages={defaultTeamImages?.results}
            imageUrl={teamImageUrl}
            imageFile={teamImage}
            handleImageChange={handleImageChange}
            error={errors.teamImageUrl}
            setImageError={(name, errorMessage) => {
              setErrors((prevErrors) => ({
                ...prevErrors,
                [name]: errorMessage,
              }));
            }}
          />

          {/* Team Name */}
          <DInputBlock label="Team name" required={isCreateTeam}>
            <DInput
              name="team_name"
              type="text"
              placeholder="Enter your team name"
              value={teamData?.name}
              onChange={(e) => setTeamName(e.target.value)}
              error={errors.teamName}
              required
            />
          </DInputBlock>

          {/* Teammates */}
          <p className="text-base font-medium tracking-tight">
            Invite teammates
          </p>
          <div className="flex flex-col w-full gap-size2">
            {teamData?.teammates?.map((teammate, index) => (
              <div
                className={clsx('flex items-start gap-size1 justify-between', {
                  '!items-end': index === 0,
                })}
                key={index}
              >
                <div className="flex gap-size1 w-full justify-between">
                  <DInputBlock
                    size="sm"
                    label="E-mail"
                    hiddenLabel={index > 0}
                    className={clsx({
                      'max-w-full': index === 0,
                      'max-w-[66%]': index > 0,
                    })}
                  >
                    <DInput
                      type="email"
                      placeholder="Enter teammate's email"
                      value={teammate.email}
                      error={teammateErrors[index]}
                      className="grow"
                      onChange={(e) =>
                        handleTeammateChange(index, 'email', e.target.value)
                      }
                      name={`teammate-email-${index}`}
                    />
                    <div className="absolute top-1 right-2"></div>
                  </DInputBlock>
                  <DInputBlock
                    size="sm"
                    label="Role"
                    hiddenLabel={index > 0}
                    shouldGrow={false}
                  >
                    <DSelect
                      options={roles?.results?.map((role) => ({
                        label: role.name,
                        value: role.id,
                      }))}
                      placeholder="Select role"
                      listButtonClass="!w-32"
                      value={
                        teammate.role_id ||
                        import.meta.env.VITE_APP_DANTE_SIGNUP_TEAM_ROLE_ID
                      }
                      onChange={(value) =>
                        handleTeammateChange(index, 'role_id', value)
                      }
                      name={`teammate-role-${index}`}
                    />
                  </DInputBlock>

                  <DInputBlock
                    label="Max. credits"
                    size="sm"
                    tooltipContent="Define maximum amount of credits member is allowed to spend in a month. Note credits will reset each month. (0 for unlimited)"
                    hiddenLabel={index > 0}
                    shouldGrow={false}
                    className="w-24"
                  >
                    <DInput
                      placeholder="Number of max. credits"
                      value={teammate.max_credits_available}
                      type="number"
                      min={0}
                      step={1}
                      className="grow-0 !w-20"
                      onChange={(e) => {
                        //don't allow negative numbers
                        if (e.target.value < 0) {
                          return;
                        }
                        handleTeammateChange(
                          index,
                          'max_credits_available',
                          e.target.value
                        );
                      }}
                      name={`teammate-max-credits-${index}`}
                    />
                  </DInputBlock>
                </div>

                <DButton
                  variant="outlined"
                  className={clsx('!size-11', {
                    'mb-size1': index === 0,
                  })}
                  onClick={() => handleDeleteTeammate(index)}
                >
                  <DeleteIcon />
                </DButton>
              </div>
            ))}
          </div>

          <DButton onClick={handleAddTeammate} className="pl-0">
            <AddIcon /> Add teammate
          </DButton>
          {errors.teammates && (
            <DTransition>
              <p className="text-error">{errors.teammates}</p>
            </DTransition>
          )}

          {/* Action Buttons */}
          <div className="flex gap-size3 w-full">
            <DButton
              variant="grey"
              size="lg"
              className="w-max whitespace-nowrap"
              onClick={() => setCurrentStep(SignUpStepsEnum.PROFILE)}
            >
              Go Back
            </DButton>
            <DButton
              variant="dark"
              size="lg"
              fullWidth
              onClick={handleSubmit}
              loading={isLoading}
            >
              {buttonText}
            </DButton>
          </div>
        </LayoutOnBoarding>
      </DTransition>
    </>
  );
};

export default RegisterTeam;
