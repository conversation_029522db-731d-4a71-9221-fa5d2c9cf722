import { Field, Label, Radio, RadioGroup } from '@headlessui/react';
import clsx from 'clsx';
import ValidationError from '../ValidationError';
import DInputBlock from '../DInput/DInputBlock';

const DRadioGroup = ({
  options = [],
  onChange,
  value,
  name = '',
  size = 'md',
  error,
  label,
  required = false,
  hideError = false,
  variant = 'stack',
  ...props
}) => {
  return (
    <DInputBlock
      label={label}
      required={required}
      error={error}
      hideError={hideError}
      labelClassName="font-regular"
    >
      <RadioGroup
        value={value}
        onChange={onChange}
        aria-label={label}
        className={clsx({
          'flex flex-col gap-size0': variant === 'stack',
          'flex flex-row gap-size2': variant === 'inline',
        })}
        data-testid={`d-radio-group-${name}`}
      >
        {options.map(({ value: optionValue, label: optionLabel }) => (
          <Field
            key={optionValue}
            className="flex items-baseline gap-size1"
            data-testid={`d-radio-group-${name}-${optionValue}`}
          >
            <Radio
              value={optionValue}
              className="group size-4 flex items-center justify-center rounded-full border bg-white data-[checked]:bg-black data-[disabled]:opacity-50 transition-all duration-300 cursor-pointer"
              role="radio"
              tabIndex={0}
            >
              <span className="invisible size-2 rounded-full bg-white group-data-[checked]:visible" />
            </Radio>
            <Label className="text-sm cursor-pointer font-regular">{optionLabel}</Label>
          </Field>
        ))}
      </RadioGroup>
    </DInputBlock>
  );
};

export default DRadioGroup;
