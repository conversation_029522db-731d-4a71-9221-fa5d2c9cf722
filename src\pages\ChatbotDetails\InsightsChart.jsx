import { useEffect, useRef } from 'react';
import { Line } from 'react-chartjs-2';
import mapApiResponseToDataset from '../../helpers/mapResponseToDataset';
import 'chart.js/auto';
import { Chart } from 'chart.js';
import 'chartjs-adapter-luxon';

const InsightsChart = ({
  apiResponse,
  showConversations,
  showMessages,
  showCredits,
  showUniqueUsers
}) => {
  const chartRef = useRef(null);

  const { credits = {}, users = {}, conversations = {}, messages = {} } = apiResponse;

  const labels = (conversations && Object.keys(conversations).sort()) || (users && Object.keys(users).sort()) || (messages && Object.keys(messages).sort()) || (credits && Object.keys(credits).sort());

  const datasetCredits =
    showCredits && credits ? mapApiResponseToDataset(credits, '255, 156, 40') : null;
  const datasetUsers =
    showUniqueUsers && users ? mapApiResponseToDataset(users, '14, 217, 9') : null;
  const datasetConversations =
    showConversations && conversations
      ? mapApiResponseToDataset(conversations, '130, 117, 247')
      : null;
  const datasetMessages =
    showMessages && messages ? mapApiResponseToDataset(messages, '255, 53, 40') : null;

  const chartData = {
    labels: labels,
    datasets: [datasetConversations, datasetMessages, datasetCredits, datasetUsers].filter(Boolean) // Filter out nulls
  };

  useEffect(() => {
    if (chartRef.current) {
      chartRef.current?.chart?.destroy();
    }
  }, [chartData]);

  return (
    <div className="w-[96%] h-[400px]">
      <Line
        ref={chartRef}
        options={{
          maintainAspectRatio: false,
          layout: {
            padding: {
              left: -8,
              right: -8
            }
          },
          plugins: {
            legend: false,
            title: false
          },
          scales: {
            x: {
              type: 'time',
              time: {
                unit: 'day',
                tooltipFormat: 'dd/MM/yyyy',
                displayFormats: {
                  day: 'dd/MM/yyyy'
                }
              },
              ticks: {
                color: '#6B7280',
                font: {
                  size: 12
                },
                maxRotation: 45,
                minRotation: 45
              },
              grid: {
                display: false
              },
              border: {
                display: false
              }
            },
            y: {
              display: false,
              grid: {
                display: false
              },
              ticks: {
                padding: 0
              }
            }
          }
        }}
        data={chartData}
      />
    </div>
  );
};

export default InsightsChart;
