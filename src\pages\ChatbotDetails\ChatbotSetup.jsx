import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import Bubble from '@/components/Bubble';
import Setup from '@/components/Chatbot/Setup';
import DButton from '@/components/Global/DButton';
import { COMMON_CLASSNAMES } from '@/constants';
import useCustomizationData from '@/hooks/useCustomization';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import useLayoutStore from '@/stores/layout/layoutStore';
import BlurredOverlay from '@/components/BlurredOverlay';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import DLoading from '@/components/DLoading';
import { useOptimistic } from '@/hooks/useOptimistic';
import ReactRouterPrompt from 'react-router-prompt';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import compareObjects from '@/helpers/compareObjects';
import { updateChatbotCoreSettings } from '@/services/customization.service';
import useToast from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { trackKlaviyoEvent } from '@/services/chatbot.service';
import StyleTag from '@/components/StyleTag';

const ChatbotSetup = () => {
  const params = useParams();
  const { customizationData, setChatbotCustomization } = useCustomizationData(
    true,
    params.id
  );
  const navigate = useNavigate();

  const [tempCustomization, updateTempCustomization, { rollback, pending }] =
    useOptimistic(customizationData, (currentState, newState) => ({
      ...currentState,
      ...newState,
    }));
  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );

  const [changedData, setChangedData] = useState({});
  
  const { addSuccessToast } = useToast();

  const updateCoreSettingData = async () => {
    setIsSaveLoading(true);
    try{
      const res = await updateChatbotCoreSettings(params.id, changedData);
      if(res.status === 200){
        // Track customized-settings event
        const user = useUserStore.getState().user;
        await trackKlaviyoEvent('customized-settings', { 
          chatbot_id: params.id
        });
        updateTempCustomization(res.data);
        setChatbotCustomization(res.data);
        addSuccessToast({
          message: 'Core settings updated successfully',
        });
        setIsSaveLoading(false);
      }
    }catch(e){
      setIsSaveLoading(false);
      console.log('error', e)
    }
  }

  useEffect(() => {
    setSidebarOpen(false);
    setIsInPreviewBubblePage(true);
  }, []);

  useEffect(() => {
    if (!tempCustomization || !customizationData) {
      return;
    }

    const hasUnsavedChanges = !compareObjects(
      customizationData,
      tempCustomization
    );

    setUnsavedChanges(hasUnsavedChanges);
  }, [tempCustomization]);

  if (!customizationData || !tempCustomization?.kb_id) {
    return <DLoading show={true} />;
  }

  return (
    <>
      <LayoutRightSidebar
        RightSidebar={() => (
          <div className={COMMON_CLASSNAMES.previewBubble}>
            <StyleTag tag=".bubble" tempCustomizationData={tempCustomization} />
            <Bubble
              config={{
                ...tempCustomization,
                public: true,
                home_tab_enabled: false,
                remove_watermark: true,
              }}
              isInApp={false}
              type="chatbot"
              isPreviewMode={true}
            />
          </div>
        )}
      >
        {() => (
            <LayoutWithButtons
              footer={
                <div className="flex items-center justify-between">
                  <DButton
                    variant="grey"
                    className="!h-12 w-full md:w-auto md:!min-w-52"
                    onClick={() => {
                      rollback();
                      navigate(`/chatbot/${params.id}`);
                    }}
                  >
                    Cancel
                  </DButton>
                  <DButton
                    variant="dark"
                    className="!h-12 w-full md:w-auto md:!min-w-52"
                    loading={isSaveLoading}
                    onClick={updateCoreSettingData}
                    disabled={compareObjects(
                      customizationData,
                      tempCustomization
                    )}
                  >
                    Save
                  </DButton>
                </div>
              }
            >
              <Setup
                customizationData={tempCustomization}
                updateCustomizationData={(key, value) =>{
                  updateTempCustomization((prev) => ({ ...prev, [key]: value }))
                  setChangedData((prev) => ({ ...prev, [key]: value }))
                }}
              />
              <ReactRouterPrompt when={unsavedChanges}>
                {({ isActive, onConfirm, onCancel }) => (
                  <DConfirmationModal
                    open={isActive}
                    onClose={onCancel}
                    onConfirm={onConfirm}
                    title="Are you sure you want to leave this page?"
                    description="You have unsaved changes. If you leave, you will lose your changes."
                    confirmText="Leave"
                    cancelText="Cancel"
                    variantConfirm="danger"
                  />
                )}
              </ReactRouterPrompt>
            </LayoutWithButtons>
        )}
      </LayoutRightSidebar>
    </>
  );
};

export default ChatbotSetup;
