import { useMemo } from 'react';
import Lottie from 'lottie-react'; // Direct import

import animationData from './DLoaderTraining.json'; // Ensure correct path
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';

const DLoaderTraining = ({ size }) => {
  const isAboveSm = useIsAboveBreakpoint('sm');

  const showSize = useMemo(() => {
    return size || (isAboveSm ? 500 : 280);
  }, [isAboveSm, size]);

  return (
    <Lottie
      animationData={animationData}
      loop={true}
      autoplay={true}
      style={{ height: showSize, width: showSize }}
    />
  );
};

export default DLoaderTraining;
