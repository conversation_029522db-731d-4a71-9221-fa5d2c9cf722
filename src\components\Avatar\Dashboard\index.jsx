import { useState } from 'react';

import { Select } from '@headlessui/react';

import DanteFAQ from '../../Chatbot/DanteFAQ';
import <PERSON><PERSON><PERSON>Logo from '../../Global/DLogo/DShapeLogo';
import AddIcon from '../../Global/Icons/AddIcon';
import OpenAiIcon from '../../Global/Icons/OpenAiLogoIcon';
import SearchIcon from '../../Global/Icons/SearchIcon';
import SendIcon from '../../Global/Icons/SendIcon';
import AvatarBlock from '../AvatarBlock';

import './index.css';
const AvatarDashboard = () => {
  //states
  const [showChat, setShowChat] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [firstMessage, setFirstMessage] = useState('');

  const toggleSearch = () => {
    setIsSearchActive(!isSearchActive);
  };

  return <div className="avatar_dashboard_wrapper h-full grow">
      <div className="avatar_dashboard-grid_content grid gap-size5 overflow-y-scroll">
        <div className="avatar_dashboard-create_btn  flex gap-size1 md:gap-0 md:block">
          <button
            className={`dbutton flex md:flex-col items-center rounded-size5 py-size2 px-size3 md:p-size3 justify-center text-grey-10 text-lg md:text-xl ${
              isSearchActive ? 'w-auto' : 'w-full'
            } h-full`}
          >
            <AddIcon className="w-[32px] h-[32px] md:w-[88px] md:h-[88px]" />
            {!isSearchActive && <span className="ml-2">New AI Avatar</span>}
          </button>
          <div
            className={`flex items-center border border-grey-5 rounded-size5 transition-transform duration-300 ease-in-out bg-white md:hidden ${
              isSearchActive ? 'w-full' : 'w-[64px]'
            }`}
          >
            <button
              className={`dbutton w-16 h-16 p-[20px] ${
                isSearchActive ? 'pr-3' : 'pr-[20px'
              } rounded-size5 bg-trasparent`}
              onClick={toggleSearch}
            >
              <SearchIcon className="w-[25px] h-[25px] text-grey-20" />
            </button>
            <input
              type="text"
              className={`bg-trasparent text-min-safe-input h-full rounded-size5 p-2 pl-0 transition-transform duration-300 ease-in-out ${
                isSearchActive ? 'w-full opacity-100' : 'w-0 opacity-0'
              }`}
            />
          </div>
        </div>
        {[1, 2, 3, 4, 5, 1, 2, 3, 4, 5].map((avatar, index) => (
          <div className="m-auto md:m-0">
            <AvatarBlock
              isDefault={false}
              image={
                'https://dante-chatbot-pictures.s3.amazonaws.com/764a6813-da65-45d3-a0e4-cc8ec47c0307/avatar-764a6813-da65-45d3-a0e4-cc8ec47c0307-1722278074914.png'
              }
              name="James"
              avatarId="asd-123"
            />
          </div>
        ))}
      </div>
      {/* <DanteFAQ
        showChat={showChat}
        setShowChat={setShowChat}
        firstMessage={firstMessage}
        setFirstMessage={setFirstMessage}
      /> */}
    </div>
};

export default AvatarDashboard;
