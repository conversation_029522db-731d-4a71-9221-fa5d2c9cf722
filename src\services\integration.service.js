import http from './http';

export const getAllIntegrations = () => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'integrations', {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const createWhatsappIntegration = (data) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'whatsapp/whatsapp-integration',
    {},
    {
      params: {
        kb_id: data.kb_id,
        graph_version: data.graph_version,
        business_account_id: data.business_account_id,
        business_phone_id: data.business_phone_id,
        phone_number: data.phone_number,
        test_meta_token: data.test_meta_token,
        meta_token: data.meta_token,
        model_type: data.model_type,
        active: data.active
      },
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const getWhatsappIntegration = (kb_id) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'whatsapp/whatsapp-integrations', {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const updateWhatsappIntegration = (data) => {
  return http.put(
    import.meta.env.VITE_APP_BASE_API + 'whatsapp/whatsapp-integrations',
    data,
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const createIntercomIntegration = (data) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'intercom/webhook/intercom',
    {},
    {
      params: data,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const updateIntercomState = (data) => {
  return http.patch(
    import.meta.env.VITE_APP_BASE_API + 'intercom/' + data.kb_id + '/intercom-enabled',
    {
      intercom_enabled: data.intercom_state
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const getIntercomIntegration = (kb_id) => {
  return http.get(
    import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/customization/' + kb_id + '/intercom',
    {},
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const updateIntercomIntegration = (kb_id, data) => {
  return http.patch(
    import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/customization/' + kb_id + '/intercom',
    data,
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const getGraphAPIVerions = () => {
  return http.get(
    import.meta.env.VITE_APP_BASE_API + 'integrations/whatsapp/graph-api-versions',
    {},
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};


export const getWordpressApiKey = () => {
  return http.get(
    import.meta.env.VITE_APP_BASE_API + 'user/wordpress_api_key',
    {},
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const generateWordpressApiKey = () => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'user/wordpress_api_key',
    {},
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const deleteWordpressApiKey = () => {
  return http.delete(
    import.meta.env.VITE_APP_BASE_API + 'user/wordpress_api_key',
    {},
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

