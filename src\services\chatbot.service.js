import generateApiEndpoint from '@/helpers/generateApiEndpoint';
import { DEFAULT_HEADERS } from './constants.service';
import http from './http';
import { useUserStore } from '@/stores/user/userStore';

export const getChatbots = async () => {
  return http.get(`${import.meta.env.VITE_APP_BASE_API}knowledge-bases`);
}

export const getChatbotsDashboard = async () => {
  return http.get(`${import.meta.env.VITE_APP_BASE_API}knowledge-bases/dashboard`);
};

export const getChatbotById = async (chatbotId) => {
  return http.get(`${import.meta.env.VITE_APP_BASE_API}knowledge-bases/${chatbotId}`);
};

export const getChatbotInfoById = async (chatbotId) => {
  return http.get(`${import.meta.env.VITE_APP_BASE_API}knowledge-bases/${chatbotId}/info`);
}

export const createChatbotAll = async (
  urls,
  max_links,
  exclude_urls,
  knowledge_base_name,
  files,
  personalities_template,
  base_system_prompt,
  open_to_internet_knowledge,
  creativity
) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/v2/all_files', files, {
    params: {
      urls: urls,
      max_links: max_links,
      exclude_urls: exclude_urls,
      knowledge_base_name: knowledge_base_name,
      personalities_template: personalities_template,
      base_system_prompt: base_system_prompt,
      open_to_internet_knowledge: open_to_internet_knowledge,
      creativity: creativity
    },
    paramsSerializer: {
      indexes: null // by default: false
    },
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 1000 * 60 * 60 * 6 // 6-hour timeout specifically for this endpoint
  });
};

export const createChatbotUrl = async (
  urls_val,
  exclude_urls_val,
  knowledge_base_name,
  user_id,
  personalities_template,
  base_system_prompt,
  open_to_internet_knowledge,
  creativity
) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/v2/url', urls_val, {
    params: {
      exclude_urls: exclude_urls_val,
      knowledge_base_name: knowledge_base_name,
      user_id: user_id,
      personalities_template: personalities_template,
      base_system_prompt: base_system_prompt,
      open_to_internet_knowledge: open_to_internet_knowledge,
      creativity: creativity
    },
    paramsSerializer: {
      indexes: null // by default: false
    },
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 1000 * 60 * 60 * 6 // 6-hour timeout specifically for this endpoint
  });
};

export const createChatbotFile = async (
  name,
  selectedFile,
  user_id,
  personalities_template,
  base_system_prompt,
  open_to_internet_knowledge,
  creativity,
  onUploadProgress
) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/files', selectedFile, {
    params: {
      knowledge_base_name: name,
      user_id: user_id,
      personalities_template: personalities_template,
      base_system_prompt: base_system_prompt,
      open_to_internet_knowledge: open_to_internet_knowledge,
      creativity: creativity
    },
    onUploadProgress,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 1000 * 60 * 60 * 6 // 6-hour timeout specifically for this endpoint
  });
};

export const updateChatbotFiles = async (chatbotId, files) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/' + chatbotId + '/files', files, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 1000 * 60 * 60 * 6 // 6-hour timeout specifically for this endpoint
  });
};

export const updateChatbotUrls = async (chatbotId, urls) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/v2/' + chatbotId + '/urls',
    urls,
    {
      params: {
        exclude_urls: [],
      },
      paramsSerializer: {
        indexes: null // by default: false
      },
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 1000 * 60 * 60 * 6 // 6-hour timeout specifically for this endpoint
    });
};

export const updateChatbotAll = async (chatbotId, urls, files, max_links) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/v2/' + chatbotId + '/all',
    files,
    {
      params: {
        urls: urls,
        max_links: max_links
      },
      paramsSerializer: {
        indexes: null // by default: false
      },
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 1000 * 60 * 60 * 6 // 6-hour timeout specifically for this endpoint
    }
  );
};

export const trainChatbot = async (chatbotId, chatbot_type) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'model/v2/create_index',
    {},
    {
      params: {
        kb_id: chatbotId,
        chatbot_type: chatbot_type
      },
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 1000 * 60 * 60 * 6 // 6-hour timeout specifically for this endpoint
    }
  );
};

export const cancelTraining = (kb_id) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'tasks/stop_index/' + kb_id,
    {},
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const getPersonalities = async (kb_id) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'personalities', {
    params: {
      kb_id: kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const deleteChatbotFile = async (fileId) => {
  return http.delete(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/files/' + fileId, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const deleteChatbotAllFiles = async (chatbotId) => {
  return http.delete(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/' + chatbotId + '/files', {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const searchChatbotFiles = async (chatbotId, search, sort, offset, limit) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/' + chatbotId + '/search', {
    params: {
      file_name: search,
      name: sort === 'name' ? true : null,
      size_ascending: sort === 'size_ascending' ? true : null,
      size_descending: sort === 'size_descending' ? true : null,
      character_count:
        sort === 'character_ascending'
          ? true
          : null || sort === 'character_descending'
            ? false
            : null,
      offset: offset,
      limit: limit
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const exportChatbotFiles = async (chatbotId) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/' + chatbotId + '/export_files', {
    headers: {
      'Content-Type': 'application/json'
    },
  });
}

export const chatbotAuthentication = (kb_id, password) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/customization/authenticate',
    {},
    {
      params: {
        kb_id: kb_id,
        password: password
      },
      withCredentials: true
    }
  );
}

/**
 * Deletes a chatbot (knowledge base) by its ID.
 *
 * @param {string} kb_id - The ID of the knowledge base (chatbot) to delete.
 * @returns {Promise} The HTTP response from the delete request.
 */
export const deleteChatbot = async (kb_id) => {
  return http.delete(`${import.meta.env.VITE_APP_BASE_API}knowledge-bases/${kb_id}`, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const patchUpdateChatbotName = async ({ kb_id, name }) => {

  return http.patch(
    `${import.meta.env.VITE_APP_BASE_API}knowledge-bases/${kb_id}/name`,
    {
      name,
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};
export const updateChatbotLLMModel = async ({ kb_id, llmModel }) => {

  return http.patch(
    `${import.meta.env.VITE_APP_BASE_API}knowledge-bases/${kb_id}/last-model-used`,
    {
      last_model_used: llmModel,
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const getShareToken = async (id, modelType) => {
  return http.get(
    generateApiEndpoint(`knowledge-bases/${id}/share?model_type=${modelType}`),
    {
      headers: DEFAULT_HEADERS

    }
  );
};

export const getChatbotLeadGen = async (kb_id, date_from, date_to) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'knowledge-bases/' + kb_id + '/lead-gen', {
    params: {
      date_created_from: date_from,
      date_created_to: date_to
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const activateNewDesign = async (kb_id) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + `knowledge-bases/${kb_id}/activate_new_design`, {}, {
    headers: DEFAULT_HEADERS
  });
};

export const getDNSRecords = async (id, url) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + `knowledge-bases/customization/${id}/dns-values`, {
    params: {
      custom_url: url
    },
    headers: DEFAULT_HEADERS
  });
}

export const klaviyoChatbotName = async (name) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'klaviyo/chatbot-started', {}, {
    params: {
      chatbot_name: name
    },
    headers: DEFAULT_HEADERS
  });
}

/**
 * Sends events to Klaviyo tracking
 * @param {string} eventName - The name of the event to track
 * @param {Object} properties - Additional properties to send with the event (chatbot name, etc.)
 * @returns {Promise} The HTTP response from the post request
 */
export const trackKlaviyoEvent = async (eventName, properties = {}) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'klaviyo/event', properties, {
    params: {
      event_name: eventName
    },
    headers: {
      'Content-Type': 'application/json',
      ...DEFAULT_HEADERS
    }
  });
}