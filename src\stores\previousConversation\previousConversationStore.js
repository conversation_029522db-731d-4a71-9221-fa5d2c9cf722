import { create } from 'zustand';
import { getConversationsFromDB, removeExpiredConversations, saveConversationToDB } from '@/services/previous-conversation.service';

export const usePreviousConversationsStore = create((set, get) => ({
  conversations: {},

  fetchConversations: async () => {
    await removeExpiredConversations();
    const conversations = await getConversationsFromDB();
    const groupedConversations = conversations.reduce((acc, conv) => {
      if (!acc[conv.kb_id]) acc[conv.kb_id] = [];
      acc[conv.kb_id].push(conv);
      return acc;
    }, {});
    set({ conversations: groupedConversations });
  },

  addConversation: async (kb_id, conversation) => {
    const newConversation = { kb_id, ...conversation };
    await saveConversationToDB(newConversation);
    const updatedConversations = { ...get().conversations };
    if (!updatedConversations[kb_id]) updatedConversations[kb_id] = [];
    updatedConversations[kb_id].push(newConversation);
    set({ conversations: updatedConversations });
  },
}));
