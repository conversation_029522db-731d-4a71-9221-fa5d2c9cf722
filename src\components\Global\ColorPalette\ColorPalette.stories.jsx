import ColorPalette from './index';

export default {
  title: 'Design System/Color Palette',
  component: ColorPalette,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A comprehensive display of all Tailwind colors used in the application.',
      },
    },
  },
};

const Template = (args) => <ColorPalette {...args} />;

export const Default = Template.bind({});
Default.args = {};

export const WithDarkMode = Template.bind({});
WithDarkMode.args = {};
WithDarkMode.parameters = {
  backgrounds: {
    default: 'dark',
  },
}; 