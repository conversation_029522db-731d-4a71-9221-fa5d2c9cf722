
export const fakerSuggestionsPrompts = [
  {
    type: 'suggestion',
    content: 'Explain the contents of this AI Chatbot'
  },
  {
    type: 'suggestion',
    content: 'Write a poem about this AI Chatbot'
  },
  {
    type: 'suggestion',
    content: 'Give me three key points from this AI Chatbot'
  }
];

export const fakerInitialMessages = [
  {
    role: 'assistant',
    content: '👋 Hi there I’m ready',
    type: 'welcome_message'
  }
];

const fakerQuestionAnswer = [
  {
    role: 'user',
    status: 'complete',
    content: 'Explain the contents of this chatbot'
  },
  {
    role: 'assistant',
    status: 'complete',
    content: 'The contents of this AI Chatbot are the following: '
  }
];

export const fakerShortConversation = [...fakerInitialMessages, ...fakerQuestionAnswer];

export const fakerLoadingConversation = [
  ...fakerInitialMessages,
  ...fakerQuestionAnswer,
  {
    role: 'user',
    status: 'complete',
    content: 'Explain the contents of this AI Chatbot'
  },
  {
    role: 'assistant',
    status: 'loading'
  }
];
export const fakerLongConversation = [
  ...fakerInitialMessages,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer,
  ...fakerQuestionAnswer
];

export const fakerLiveAgentConversation = [
 ...fakerShortConversation,
 {
  type: 'info',
  content: 'Connecting agent (est. 3 min)'
 },
 {
  type: 'info',
  content: 'Dan joined conversation at 09:53'
 },
 {
  role: 'assistant',
  status: 'complete',
  content: 'Hi, Dan here I will be with you in a sec! Allow me a moment to take a look at your conversation so far.'
 },
]