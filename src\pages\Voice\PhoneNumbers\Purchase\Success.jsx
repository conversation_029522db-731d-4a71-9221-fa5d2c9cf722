import { useEffect, useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import LayoutMain from '@/layouts/LayoutMain';
import DLoading from '@/components/DLoading';
import useToast from '@/hooks/useToast';
import { confirmNumberPurchase } from '@/services/phoneNumberPurchase.service';
import SuccessIcon from '@/components/Global/Icons/SuccessIcon';

const PhoneNumberPurchaseSuccess = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { addSuccessToast, addErrorToast } = useToast();

    const [isLoading, setIsLoading] = useState(true);
    const [countdown, setCountdown] = useState(5);
    const [purchaseDetails, setPurchaseDetails] = useState(null);
    
    // Use a ref to track if confirmation has been initiated
    const confirmationInitiated = useRef(false);

    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const token = params.get('token');
        const sessionId = params.get('session_id');
        const userId = params.get('user_id');

        if (!token || !sessionId || !userId) {
            addErrorToast({ message: 'Invalid purchase parameters. Redirecting to phone numbers page.' });
            setTimeout(() => {
                navigate('/phone-numbers');
            }, 3000);
            return;
        }

        // Generate a unique key for this purchase confirmation
        const confirmationKey = `phone_number_purchase_${sessionId}`;
        // Check if this purchase has already been confirmed
        const alreadyConfirmed = sessionStorage.getItem(confirmationKey);

        const confirmPurchase = async () => {
            // Prevent duplicate confirmations
            if (alreadyConfirmed === 'confirmed' || confirmationInitiated.current) {
                // If already confirmed, just use the stored data if available
                const storedData = sessionStorage.getItem(`${confirmationKey}_data`);
                if (storedData) {
                    try {
                        setPurchaseDetails(JSON.parse(storedData));
                    } catch (e) {
                        console.error('Error parsing stored purchase data:', e);
                    }
                }
                setIsLoading(false);
                return;
            }

            // Mark as initiated to prevent duplicate API calls
            confirmationInitiated.current = true;
            
            try {
                const response = await confirmNumberPurchase(token, sessionId, userId);
                if (response.data) {
                    // Save purchase details
                    setPurchaseDetails(response.data);
                    addSuccessToast({ message: 'Phone number purchased successfully!' });
                    
                    // Mark this purchase as confirmed and store the data
                    sessionStorage.setItem(confirmationKey, 'confirmed');
                    sessionStorage.setItem(`${confirmationKey}_data`, JSON.stringify(response.data));
                }
            } catch (error) {
                console.error('Error confirming purchase:', error);
                addErrorToast({ message: 'Failed to confirm purchase. Please contact support.' });
            } finally {
                setIsLoading(false);
            }
        };

        confirmPurchase();
        
        // Cleanup function
        return () => {
            confirmationInitiated.current = false;
        };
    }, [location.search, navigate, addSuccessToast, addErrorToast]);

    useEffect(() => {
        if (!isLoading) {
            const timer = setInterval(() => {
                setCountdown((prev) => {
                    if (prev <= 1) {
                        clearInterval(timer);
                        // Navigate with the purchased phone number as a query parameter
                        if (purchaseDetails && purchaseDetails.phone_number) {
                            navigate(`/phone-numbers?just_purchased=${purchaseDetails.phone_number.replace('+', '')}`);
                        } else {
                            navigate('/phone-numbers');
                        }
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);

            return () => clearInterval(timer);
        }
    }, [isLoading, navigate, purchaseDetails]);

    return (
        <LayoutMain title="Purchase Successful">
            {isLoading ? (
                <DLoading show={true} />
            ) : (
                <div className="flex flex-col items-center justify-center py-size10">
                    <div className="bg-white p-size5 rounded-size1 shadow-sm max-w-md w-full flex flex-col items-center">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-size3">
                            <SuccessIcon className="w-8 h-8 text-green-500" />
                        </div>

                        <h2 className="text-2xl font-medium mb-size2">Purchase Successful!</h2>

                        <p className="text-grey-75 text-center mb-size5">
                            Your new phone number has been added to your account and is ready to use with your AI Voice Agent.
                        </p>

                        {purchaseDetails && purchaseDetails.phone_number && (
                            <div className="bg-grey-5 p-size3 rounded-size1 w-full mb-size5">
                                <p className="text-center font-medium">{purchaseDetails.phone_number}</p>
                            </div>
                        )}

                        <p className="text-sm text-grey-50">
                            Redirecting to phone numbers page in {countdown} seconds...
                        </p>
                    </div>
                </div>
            )}
        </LayoutMain>
    );
};

export default PhoneNumberPurchaseSuccess;
