// src/hooks/useTaskHandler.js
import { STATUS } from '../constants';
import useTaskStore from '../stores/task/taskStore';

const useTaskHandler = () => {
  const { addTask, startPollingTask } = useTaskStore();

  const handleTask = async (type, serviceCall, ...params) => {
    try {
      const { data } = await serviceCall(...params);
      const { task_id } = data;

      if (!task_id) {
        throw new Error('No task_id returned from service.');
      }

      addTask(type, { id: task_id, status: STATUS.PENDING });
      startPollingTask(type, task_id);

      return task_id;
    } catch (error) {
      console.error(`<PERSON>rror creating ${type} task:`, error);
      return null;
    }
  };

  return { handleTask };
};

export default useTaskHandler;
