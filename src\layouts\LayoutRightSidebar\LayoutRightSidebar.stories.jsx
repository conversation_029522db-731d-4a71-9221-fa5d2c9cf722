import ChatbotBlock from '@/components/Chatbot/ChatbotBlock';
import DButton from '@/components/Global/DButton';
import DButtonIcon from '@/components/Global/DButtonIcon';
import DInput from '@/components/Global/DInput/DInput';
import DInputBlock from '@/components/Global/DInput/DInputBlock';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import { fn } from '@storybook/test';

import LayoutWithButtons from '../LayoutWithButtons';

import LayoutRightSidebar from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Layout/LayoutRightSidebar',
  component: LayoutRightSidebar,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'fullscreen'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {}
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  parameters: {
    viewport: {
      defaultViewport: 'reset'
    }
  },
  args: {
    RightSidebar: (handleRightSidebar) => {
      return (
        <div className="w-full">
          <header className="w-full flex">
            <div className="flex flex-end md:hidden">
              <DButtonIcon variant="contained" onClick={handleRightSidebar}>
                <CloseIcon />
              </DButtonIcon>
            </div>
          </header>
          <div className="w-full md:w-96 flex flex-col gap-4">
            {Array.from({ length: 2 }, (value, index) => index).map((i) => (
              <DInputBlock description="Description" key={i} label="Title" name="full_name">
                <DInput />
              </DInputBlock>
            ))}
          </div>
        </div>
      );
    },
    children: (handleRightSidebar) => {
      return (
        <LayoutWithButtons buttons>
          <div className="flex flex-end md:hidden">
            <DButton variant="contained" onClick={handleRightSidebar}>
              Open RightSidebar
            </DButton>
          </div>
          <div className="flex flex-col gap-4">
            {Array.from({ length: 20 }, (value, index) => index).map((i) => (
              <DInputBlock description="Description" key={i} label="Title" name="full_name">
                <DInput />
              </DInputBlock>
            ))}
          </div>
        </LayoutWithButtons>
      );
    }
  }
};
export const SmallScreen = {
  parameters: {
    viewport: {
      defaultViewport: 'iphone14promax'
    }
  },
  args: Default.args
};
