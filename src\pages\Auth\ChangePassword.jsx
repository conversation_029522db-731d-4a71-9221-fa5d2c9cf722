import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import DFullLogo from '@/components/Global/DLogo/DFullLogo';
import EyeIcon from '@/components/Global/Icons/EyeIcon';
import EyeClosedIcon from '@/components/Global/Icons/EyeClosedIcon';
import { resetPassword } from '@/services/user.service';
import { useUserStore } from '@/stores/user/userStore';

import './login.css';

const ChangePasswordPage = () => {
  const navigate = useNavigate();

  const [oldPassword, setOldPassword] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [contactConsent, setContactConsent] = useState(true);

  const [oldPasswordError, setOldPasswordError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');

  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [canSubmit, setCanSubmit] = useState(false);

  const saveAuthDetail = useUserStore((state) => state.saveAuthDetail);

  // Check if passwords match and validate on every keystroke
  useEffect(() => {
    // Validate old password
    if (oldPassword) {
      if (oldPassword.length < 1) {
        setOldPasswordError('Old password is required');
      } else {
        setOldPasswordError('');
      }
    } else {
      setOldPasswordError('');
    }

    // Only validate if confirmPassword is not empty
    if (confirmPassword) {
      if (password !== confirmPassword) {
        setConfirmPasswordError('Passwords do not match');
      } else {
        setConfirmPasswordError('');
      }
    } else {
      setConfirmPasswordError('');
    }

    // Validate password on every keystroke, but only if not empty
    if (password) {
      validatePassword();
    } else {
      setPasswordError('');
    }

    // Update canSubmit based on validation
    setCanSubmit(
      oldPassword &&
        password &&
        confirmPassword &&
        password === confirmPassword &&
        !passwordError &&
        !confirmPasswordError &&
        !oldPasswordError
    );
  }, [
    oldPassword,
    password,
    confirmPassword,
    passwordError,
    confirmPasswordError,
    oldPasswordError,
  ]);

  /**
   * Validates the password against security requirements
   *
   * @returns {boolean} Whether the password is valid
   */
  const validatePassword = () => {
    let isValid = true;

    // Don't show errors for empty password
    if (!password) {
      setPasswordError('');
      return false;
    }

    // Check for minimum length
    if (password.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      isValid = false;
    }
    // Check for letters (both uppercase and lowercase)
    else if (!/[a-z]/i.test(password)) {
      setPasswordError('Password must include at least one letter');
      isValid = false;
    }
    // Check for numbers
    else if (!/[0-9]/.test(password)) {
      setPasswordError('Password must include at least one number');
      isValid = false;
    }
    // Check for symbols/special characters
    else if (!/[^a-zA-Z0-9]/.test(password)) {
      setPasswordError('Password must include at least one symbol');
      isValid = false;
    } else {
      setPasswordError('');
    }

    // Check if passwords match - this is handled by the useEffect
    if (confirmPasswordError) {
      isValid = false;
    }

    return isValid;
  };

  /**
   * Handles the form submission for changing the password.
   */
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);

    // Validate password before submitting
    if (!validatePassword() || password !== confirmPassword) {
      setError('Please fix the validation errors before submitting');
      return;
    }

    try {
      setLoading(true);
      const response = await resetPassword({
        old_password: oldPassword,
        new_password: password,
        contact_consent: contactConsent,
      });

      if (response.status === 200) {
        saveAuthDetail({
          access_token: response.data.access_token,
        });
        setSuccess(true);
        navigate('/');
      }
    } catch (error) {
      console.error(error);
      setError(
        error.message || 'An error occurred while changing your password'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleContactConsentChange = (checked) => {
    setContactConsent(checked);
  };

  return (
    <div className="relative flex flex-col items-center justify-center min-h-dvh w-full overflow-hidden">
      <div className="absolute bottom-[-60%] right-[-20%] w-[120%] h-[120%] rounded-full bg-gradient-radial from-purple-200 to-transparent opacity-20"></div>

      {/* Content with entry animation */}
      <div className="relative z-10 w-full max-w-md p-4 animate-fadeIn">
        <div className="flex justify-center mb-4 h-4 md:h-8">
          <DFullLogo />
        </div>

        {/* Main content with animation */}
        <div className="animate-fadeInUp">
          <div className="bg-white rounded-t-xl md:rounded-t-3xl shadow-lg p-4 md:p-7 mx-2 md:mx-0">
            {/* Back Button */}
            <div className="mb-6">
              <button
                onClick={() => navigate('/log-in')}
                className="flex items-center text-gray-600 font-medium hover:text-gray-900"
                disabled={loading}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15 10H5M5 10L10 15M5 10L10 5"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="ml-1">Back to Login</span>
              </button>
            </div>

            {/* Lock Icon */}
            <div className="flex justify-center mb-6">
              <div className="relative">
                <svg
                  width="80"
                  height="80"
                  viewBox="0 0 80 80"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="40" cy="40" r="30" fill="#F0F0FF" />
                  <path
                    d="M30 36.6667V33.3333C30 26.6667 33.3333 23.3333 40 23.3333C46.6667 23.3333 50 26.6667 50 33.3333V36.6667"
                    stroke="#8070F2"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M46.6673 56.6667H33.334C28.334 56.6667 26.6673 55 26.6673 50V43.3333C26.6673 38.3333 28.334 36.6667 33.334 36.6667H46.6673C51.6673 36.6667 53.334 38.3333 53.334 43.3333V50C53.334 55 51.6673 56.6667 46.6673 56.6667Z"
                    fill="white"
                    stroke="#8070F2"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M45.8327 46.6667H45.8477"
                    stroke="#8070F2"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M39.9987 46.6667H40.0137"
                    stroke="#8070F2"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M34.1654 46.6667H34.1804"
                    stroke="#8070F2"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>

            {/* Header */}
            <div className="text-center mb-7">
              <h1 className="text-2xl font-medium mb-2">
                Change your password
              </h1>
              <p className="text-gray-500 text-sm">
                Create a strong password with a mix of letters, numbers and
                symbols
              </p>
            </div>

            {/* Password Form */}
            <div className="mb-6">
              <form onSubmit={handleSubmit}>
                {error && (
                  <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg text-sm">
                    {error}
                  </div>
                )}
                {success && (
                  <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-lg text-sm">
                    Your password has been changed successfully.
                  </div>
                )}

                <div className="mb-4">
                  {/* Old Password Field */}
                  <div className="mb-3">
                    <div className="relative">
                      <input
                        type={showOldPassword ? 'text' : 'password'}
                        placeholder="Current password"
                        className={`w-full py-3 px-4 border ${
                          oldPasswordError
                            ? 'border-red-500'
                            : 'border-gray-200'
                        } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-gray-700 text-min-safe-input`}
                        value={oldPassword}
                        onChange={(e) => setOldPassword(e.target.value)}
                        disabled={loading}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none p-1"
                        onClick={() => setShowOldPassword(!showOldPassword)}
                        tabIndex="-1"
                        aria-label={
                          showOldPassword ? 'Hide password' : 'Show password'
                        }
                      >
                        <span className="flex items-center justify-center w-5 h-5">
                          {showOldPassword ? <EyeIcon /> : <EyeClosedIcon />}
                        </span>
                      </button>
                    </div>
                    <div className="h-5 mt-1">
                      {oldPasswordError && (
                        <p className="text-red-500 text-xs">
                          {oldPasswordError}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* New Password Field */}
                  <div className="mb-3">
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        placeholder="New password"
                        className={`w-full py-3 px-4 border ${
                          password && passwordError
                            ? 'border-red-500'
                            : 'border-gray-200'
                        } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-gray-700 text-min-safe-input`}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        disabled={loading}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none p-1"
                        onClick={() => setShowPassword(!showPassword)}
                        tabIndex="-1"
                        aria-label={
                          showPassword ? 'Hide password' : 'Show password'
                        }
                      >
                        <span className="flex items-center justify-center w-5 h-5">
                          {showPassword ? <EyeIcon /> : <EyeClosedIcon />}
                        </span>
                      </button>
                    </div>
                    <div className="h-5 mt-1">
                      {passwordError && (
                        <p className="text-red-500 text-xs">{passwordError}</p>
                      )}
                    </div>
                  </div>

                  {/* Confirm Password Field */}
                  <div className="mb-3">
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        placeholder="Confirm new password"
                        className={`w-full py-3 px-4 border ${
                          confirmPassword && confirmPasswordError
                            ? 'border-red-500'
                            : 'border-gray-200'
                        } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-gray-700 text-min-safe-input`}
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        disabled={loading}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none p-1"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                        tabIndex="-1"
                        aria-label={
                          showConfirmPassword
                            ? 'Hide password'
                            : 'Show password'
                        }
                      >
                        <span className="flex items-center justify-center w-5 h-5">
                          {showConfirmPassword ? (
                            <EyeIcon />
                          ) : (
                            <EyeClosedIcon />
                          )}
                        </span>
                      </button>
                    </div>
                    <div className="h-5 mt-1">
                      {confirmPasswordError && (
                        <p className="text-red-500 text-xs">
                          {confirmPasswordError}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Contact Consent Checkbox */}
                  <div className="mt-4 flex items-start gap-2">
                    <input
                      type="checkbox"
                      id="contactConsent"
                      className="mt-1 accent-indigo-600"
                      checked={contactConsent}
                      onChange={(e) => setContactConsent(e.target.checked)}
                    />
                    <label
                      htmlFor="contactConsent"
                      className="text-xs text-gray-500"
                    >
                      Send me additional free support and feature updates
                    </label>
                  </div>
                </div>

                <button
                  type="submit"
                  className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center hover:bg-indigo-700 transition"
                  disabled={!canSubmit || loading}
                >
                  {loading ? (
                    <svg
                      className="animate-spin h-5 w-5 mr-2"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  ) : null}
                  Change Password
                </button>
              </form>
            </div>
          </div>

          {/* Sign In Link */}
          <div className="bg-gray-100 py-4 rounded-b-xl md:rounded-b-3xl text-center shadow-lg mx-2 md:mx-0">
            <p className="text-sm text-gray-500">
              Want to use same password?{' '}
              <Link to="/log-in" className="text-indigo-600 hover:underline">
                Log in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangePasswordPage;
