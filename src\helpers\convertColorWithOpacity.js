import hexToRgba from 'hex-to-rgba';

/**
 * Converts a hex color code to an RGBA color with the specified opacity.
 *
 * @param {string} color - The hex color code (e.g., '#ffffff').
 * @param {number} opacity - The opacity level (between 0 and 1).
 * @returns {string} - The RGBA color string (e.g., 'rgba(255, 255, 255, 0.5)').
 */
const convertColorWithOpacity = (color, opacity) => {
  return hexToRgba(color, opacity);
};

export default convertColorWithOpacity;
