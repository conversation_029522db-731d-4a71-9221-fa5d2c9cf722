import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import DChatbotSidebar from '@/components/Chatbot/Details/ChatbotSidebar';
import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DSwitch from '@/components/Global/DSwitch';
import IntercomColorIcon from '@/components/Global/Icons/IntercomColorIcon';
import IntegrationApps from '@/components/IntegrationApps';
import useDanteApi from '@/hooks/useDanteApi';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import * as integrationService from '@/services/integration.service';
import useToast from '@/hooks/useToast';

const IntercomIntegration = () => {
  const { addSuccessToast } = useToast();
  const navigate = useNavigate();
  const params = useParams();
  const { data } = useDanteApi(
    integrationService.getIntercomIntegration(params.id)
  );
  const [intercomData, setIntercomData] = useState(data);

  const createIntercomIntegration = async () => {
    try {
      const response = await integrationService.updateIntercomIntegration(
        params.id,
        intercomData
      );
      if (response.status === 200) {
        addSuccessToast({
          message: 'Intercom integration updated successfully',
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <LayoutRightSidebar
      RightSidebar={() => {
        return <IntegrationApps row={false} />;
      }}
    >
      {() => {
        return (
          <div className="w-full h-[1px] grow overflow-y-auto bg-white rounded-size1 p-size5 flex flex-col gap-size5">
            <div className="flex text-black text-base">
              <a className="text-grey-50" onClick={() => navigate(-1)}>
                Integrations
              </a>{' '}
              /{' '}
              <a href="" className="text-black">
                Intercom
              </a>
            </div>
            <div className="w-full h-px bg-grey-5"></div>
            <div className="flex gap-size5">
              <div className="flex flex-col py-size4 px-size7 rounded-size3 border border-grey-5 items-center justify-center size-24">
                <IntercomColorIcon />
              </div>
              <div className="flex flex-col gap-size3">
                <p className="text-xl font-medium tracking-tight">Intercom</p>
                <p className="text-sm text-grey-50">
                  Connect your AI Chatbot to Intercom to enable real-time
                  messaging with your customers. Ideal for businesses aiming for
                  a wide-reaching presence on a popular messaging platform.
                </p>
              </div>
            </div>
            <div className="flex gap-size2 p-size5 bg-grey-2 rounded-size2 items-start">
              <div className="flex flex-col gap-size3">
                <p className="text-xl font-medium tracking-tight">
                  Activate Intercom
                </p>
                <p className="text-sm ">
                  Connect your AI Chatbot to Intercom to enable real-time
                  messaging with your customers. <a href="https://www.dante-ai.com/guides/intercom-integration" target="_blank" rel="noopener noreferrer" className="text-purple-300"> Click here</a> to view the
                  documentation on how to set up Intercom integration.
                </p>
              </div>
              <DSwitch
                checked={intercomData?.intercom_enabled}
                onChange={(checked) =>
                  setIntercomData({
                    ...intercomData,
                    intercom_enabled: checked,
                  })
                }
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                Intercom App ID
              </p>
              <DInput
                placeholder="Enter your Intercom App ID"
                value={intercomData?.intercom_company_id}
                onChange={(e) =>
                  setIntercomData({
                    ...intercomData,
                    intercom_company_id: e.target.value,
                  })
                }
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                Intercom Access Token
              </p>
              <DInput
                placeholder="Enter your Intercom Access Token"
                value={intercomData?.intercom_api_key}
                onChange={(e) =>
                  setIntercomData({
                    ...intercomData,
                    intercom_api_key: e.target.value,
                  })
                }
              />
            </div>
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">
                Human Handover Email Prompt
              </p>
              <DInput
                placeholder="Enter your Human Handover Email Prompt"
                value={intercomData?.live_agent_email_prompt}
                onChange={(e) =>
                  setIntercomData({
                    ...intercomData,
                    live_agent_email_prompt: e.target.value,
                  })
                }
              />
            </div>
            <DButton variant="dark" onClick={createIntercomIntegration}>
              Connect
            </DButton>
          </div>
        );
      }}
    </LayoutRightSidebar>
  );
};

export default IntercomIntegration;
