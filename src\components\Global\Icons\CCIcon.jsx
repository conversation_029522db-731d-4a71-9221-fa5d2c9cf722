import * as React from 'react';
const CCIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.103 5.558a3.58 3.58 0 0 1 2.503-1.014h6.788a3.58 3.58 0 0 1 2.503 1.014 3.43 3.43 0 0 1 1.036 2.45v3.985c0 .918-.373 1.8-1.036 2.449a3.58 3.58 0 0 1-2.503 1.014H6.606a3.58 3.58 0 0 1-2.503-1.014 3.43 3.43 0 0 1-1.036-2.45V8.008c0-.918.373-1.8 1.036-2.449M6.606 3.5A4.66 4.66 0 0 0 3.35 4.82 4.46 4.46 0 0 0 2 8.007v3.986c0 1.195.485 2.342 1.35 3.187a4.66 4.66 0 0 0 3.256 1.32h6.788a4.66 4.66 0 0 0 3.257-1.32A4.46 4.46 0 0 0 18 11.993V8.007a4.46 4.46 0 0 0-1.35-3.187 4.66 4.66 0 0 0-3.256-1.32zm1.84 4.168a2.5 2.5 0 0 0-2.673.52 2.38 2.38 0 0 0-.718 1.698v.228a2.36 2.36 0 0 0 .718 1.698c.228.223.498.4.796.52a2.5 2.5 0 0 0 2.673-.52l.117-.114a.514.514 0 0 0 0-.739.54.54 0 0 0-.754 0l-.117.115a1.4 1.4 0 0 1-1.511.294 1.4 1.4 0 0 1-.75-.735 1.3 1.3 0 0 1-.106-.519v-.228c0-.36.146-.705.406-.96a1.4 1.4 0 0 1 1.511-.294c.168.069.321.168.45.294l.117.115a.54.54 0 0 0 .754 0 .514.514 0 0 0 0-.738l-.117-.115a2.5 2.5 0 0 0-.796-.52m5.43 0a2.5 2.5 0 0 0-2.673.52 2.38 2.38 0 0 0-.718 1.698v.228a2.4 2.4 0 0 0 .718 1.698c.228.223.498.4.796.52a2.5 2.5 0 0 0 2.673-.52l.117-.114a.514.514 0 0 0 0-.739.54.54 0 0 0-.754 0l-.117.115a1.4 1.4 0 0 1-1.51.294 1.4 1.4 0 0 1-.75-.735 1.3 1.3 0 0 1-.106-.519v-.228c0-.36.146-.705.406-.96a1.4 1.4 0 0 1 1.51-.294c.169.069.322.168.45.294l.117.115a.54.54 0 0 0 .754 0 .514.514 0 0 0 0-.738l-.117-.115a2.5 2.5 0 0 0-.796-.52"
      fill="currentColor"
    />
  </svg>
);
export default CCIcon;
