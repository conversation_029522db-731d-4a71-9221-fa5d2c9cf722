import useLogout from '@/application/auth/logout';
import DSelect from '@/components/Global/DSelect';
import OnboardingTiers from '@/components/OnboardingTiers';
import useDanteApi from '@/hooks/useDanteApi';
import * as plansService from '@/services/plans.service';
import { getTeams } from '@/services/teamManagement.service';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { useUserStore } from '@/stores/user/userStore';
import { useNavigate } from 'react-router-dom';
    
const Upgrade = () => {
    const logout = useLogout();
    const navigate = useNavigate();
    const userData = useUserStore((state) => state.user);
    const {
      setSelectedTeam,
      setTeamName,
    } = useTeamManagementStore((state) => state);
    const { data: teams } = useDanteApi(getTeams, [])

    const handleUpgrade = async (tierId, tierName, old_to_new) => {
      try {
        const response = await plansService.getCheckoutSession(tierId,'', old_to_new);
        if (response.status === 200) {
          window.open(response.data.checkout_session.url, '_blank');
        }
      } catch (error) {
        console.log(error);
      }
    };

  const handleLogout = () => {
    logout();
    navigate('/log-in');
  }

    return <div className="flex flex-col gap-size5 p-size5">
        <div className="flex w-full justify-end gap-size2 items-center">
          {teams?.results?.filter((team) => team.owner_id !== userData?.id).length > 0 && <DSelect
            options={teams?.results?.filter((team) => team.owner_id !== userData?.id).map((team) => ({
              label: team.name,
              value: team.id
            }))}
            onChange={(value) => {
              setSelectedTeam(teams?.results?.find((team) => team.id === value));
              setTeamName(teams?.results?.find((team) => team.id === value)?.name);
              window.location.href = `/team-management/${value}`
            }}
            listButtonClass='!w-[200px]'
            placeholder='Select a team'
          />}
          <button className="text-base text-grey-50" onClick={handleLogout}>
            Log out
          </button>
        </div>
        <OnboardingTiers handleUpgrade={handleUpgrade} />
    </div>;
    };

export default Upgrade;
