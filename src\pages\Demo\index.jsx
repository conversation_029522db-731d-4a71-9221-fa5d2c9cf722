import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const Demo = () => {
    const [urlParams] = useSearchParams();
    const backgroundUrl = urlParams.get('background_url_16_9') ?? '';
    const [blurBg, setBlurBg] = useState(false);
  
    useEffect(() => {
      const script = document.createElement('script');

      const url =
        'https://app.dante-ai.com/bubble-embed.js' +
        '?kb_id=' +
        urlParams.get('kb_id') +
        '&token=' +
        urlParams.get('token') +
        '&modeltype=' +
        urlParams.get('modeltype') +
        '&mode=' +
        urlParams.get('mode');

      script.src = url;
      script.async = true;
      script.onload = () => {
        console.log('Bubble Embed Script Loaded Successfully');
      };
      script.onerror = () => {
        console.error('Failed to Load Bubble Embed Script');
      };
      document.head.appendChild(script);

      return () => {
        document.head.removeChild(script);
      };
    }, [urlParams]);
    
    window.addEventListener('message', (e) => {
      if (e.data.eventType === 'signup-btn') {
        setBlurBg(e.data.eventData);
      }
    });
  return     <>
  {urlParams.get('can_iframe') === 'true' ? (
    <div className='w-screen h-screen'>
      {
        <iframe
          id="demo-iframe"
          width={'100%'}
          height={'100%'}
          src={urlParams.get('url') ?? 'https://www.dante-ai.com/'}
        ></iframe>
      }
    </div>
  ) : (
    <div className='w-screen h-screen relative overflow-auto'>
      <img
        src={backgroundUrl}
        alt="Background"
        style={{
          width: '100%',
          position: 'absolute',
          top: 0,
          left: 0,
          objectFit: 'cover',
          filter: blurBg ? 'blur(3px)' : 'none'
        }}
      />
    </div>
  )}
</>
};

export default Demo;
