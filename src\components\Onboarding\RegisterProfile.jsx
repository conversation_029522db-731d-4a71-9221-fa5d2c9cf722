import DButton from '../Global/DButton';
import LayoutOnBoarding from './LayoutOnBoarding';
import { SignUpStepsEnum } from './signUpSteps';
import * as userService from '@/services/user.service';
import useDanteApi from '@/hooks/useDanteApi';
import { useState } from 'react';
import DInputBlock from '../Global/DInput/DInputBlock';
import DInput from '../Global/DInput/DInput';
import { useUserStore } from '@/stores/user/userStore';
import DTransition from '../Global/DTransition';
import DSelectSearch from '../Global/DSelectSearch';
import DProfileImage from '../DProfileImage';

const RegisterProfile = ({
  handleChange,
  setCurrentStep,
  userData,
  setUserData,
}) => {
  const { data: defaultImages } = useDanteApi(userService.getDefaultImages);
  const [accountImage, setAccountImage] = useState(null);
  const [accountImageUrl, setAccountImageUrl] = useState(
    userData?.profile_image || defaultImages?.results?.[0]?.big_image_url || ''
  );
  const user = useUserStore((state) => state.user);
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const setUser = useUserStore((state) => state.setUser);

  const jobRoles = [
    {
      label: 'Account Manager',
      value: 'account_manager',
    },
    {
      label: 'Accountant',
      value: 'accountant',
    },
    {
      label: 'Business Analyst',
      value: 'business_analyst',
    },
    {
      label: 'Civil Engineer',
      value: 'civil_engineer',
    },
    {
      label: 'Customer Support Specialist',
      value: 'customer_support_specialist',
    },

    {
      label: 'Data Scientist',
      value: 'data_scientist',
    },
    {
      label: 'Digital Marketing Specialist',
      value: 'digital_marketing_specialist',
    },
    {
      label: 'E-learning Designer',
      value: 'e_learning_designer',
    },
    {
      label: 'Financial Analyst',
      value: 'financial_analyst',
    },
    {
      label: 'Graphic Designer',
      value: 'graphic_designer',
    },

    {
      label: 'Healthcare Administrator',
      value: 'healthcare_administrator',
    },

    {
      label: 'Sales Executive',
      value: 'sales_executive',
    },

    {
      label: 'HR Manager',
      value: 'hr_manager',
    },

    {
      label: 'Illustrator',
      value: 'illustrator',
    },

    {
      label: 'Lawyer',
      value: 'lawyer',
    },
    {
      label: 'Logistics Coordinator',
      value: 'logistics_coordinator',
    },
    {
      label: 'Mechanical Engineer',
      value: 'mechanical_engineer',
    },
    {
      label: 'Paralegal',
      value: 'paralegal',
    },
    {
      label: 'Photographer',
      value: 'photographer',
    },
    {
      label: 'Project Manager',
      value: 'project_manager',
    },
    {
      label: 'Recruiter',
      value: 'recruiter',
    },
    {
      label: 'Registered Nurse',
      value: 'registered_nurse',
    },

    {
      label: 'Software Developer',
      value: 'software_developer',
    },
    {
      label: 'Supply Chain Manager',
      value: 'supply_chain_manager',
    },
    {
      label: 'Teacher',
      value: 'teacher',
    },
    {
      label: 'Technical Support Agent',
      value: 'technical_support_agent',
    },
  ];

  const howFindUs = [
    {
      label: 'Social Media (e.g., Instagram, Twitter, Facebook)',
      value: 'social-media',
    },
    {
      label: 'Email',
      value: 'email',
    },
    {
      label: 'Search Engine (e.g., Google, Bing)',
      value: 'search-engine',
    },
    {
      label: 'Referral',
      value: 'referral',
    },
    {
      label: 'Online Advertisement (e.g., Google Ads, Banner Ads)',
      value: 'online-advertisement',
    },

    {
      label: 'Review Website',
      value: 'review-website',
    },
  ];

  const validateFields = async () => {
    const newErrors = {};
    let hasErrors = false;

    if (!userData?.full_name) {
      newErrors.full_name = 'Full name is required';
      hasErrors = true;
    }

    if (!userData?.job_role) {
      newErrors.job_role = 'Role is required';
      hasErrors = true;
    }
    if (userData?.job_role === 'other' && !userData?.role_others) {
      newErrors.role_others =
        'You selected \'Other.\' Please provide additional details';
      hasErrors = true;
    }

    if (!userData?.referral_source) {
      newErrors.referral_source =
        'Please select an option to let us know how you found out about us';
      hasErrors = true;
    }
    if (
      userData?.referral_source === 'other' &&
      !userData?.referral_source_others
    ) {
      newErrors.referral_source_others =
        'You selected \'Other.\' Please provide additional details';
      hasErrors = true;
    }

    if (!accountImageUrl && !accountImage) {
      newErrors.accountImage = 'Please select or upload an image';
      hasErrors = true;
    } else if (accountImage instanceof File) {
      if (accountImage.size > 150 * 1024) {
        newErrors.accountImage = 'Max file size: 150kb';
        hasErrors = true;
      } else {
        const isImageValid = await new Promise((resolve) => {
          const image = new Image();
          image.src = URL.createObjectURL(accountImage);
          image.onload = () => {
            if (image.width > 200 || image.height > 200) {
              resolve(false);
            } else {
              resolve(true);
            }
          };
          image.onerror = () => resolve(false);
        });

        if (!isImageValid) {
          newErrors.accountImage = 'Max width: 200px, max height: 200px';
          hasErrors = true;
        }
      }
    }

    setErrors(newErrors);
    return !hasErrors;
  };

  const handleRegisterProfile = async () => {
    const isValid = await validateFields();

    if (!isValid) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    let profile_image = accountImageUrl;

    try {
      if (accountImage instanceof File) {
        const formData = new FormData();
        formData.append('file', accountImage);
        const response = await userService.uploadFile(formData);
        if (response.status === 200) {
          profile_image = response?.data?.url;
        }
      }

      const responseReferral = await userService.sendReferralSource({
        referral_source:
          userData?.referral_source === 'other'
            ? userData?.referral_source_others
            : userData?.referral_source,
      });

      const response = await userService.updateUserInfo({
        full_name: userData?.full_name,
        job_role:
          userData?.job_role === 'other'
            ? userData?.role_others
            : userData?.job_role,
        company_name: userData?.company_name,
        profile_image,
      });

      if (response.status === 200) {
        setUser({ ...user, ...response.data });

        setUserData({
          ...response.data,
          job_role:
            userData?.job_role === 'other' ? 'other' : userData?.job_role,
          role_others:
            userData?.job_role === 'other' ? userData?.role_others : '',

          referral_source:
            userData?.referral_source === 'other'
              ? 'other'
              : userData?.referral_source,

          referral_source_others:
            userData?.referral_source === 'other'
              ? userData?.referral_source_others
              : '',
        });

        setCurrentStep(SignUpStepsEnum.TEAM);
      }
    } catch (e) {
      console.error('Error updating profile:', e);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageChange = (file) => {
    if (file instanceof File) {
      setAccountImage(file);
      setAccountImageUrl(URL.createObjectURL(file));
    } else {
      setAccountImage(null);
      setAccountImageUrl(file);
    }
    handleChange({ profile_image: file }, 'userData');
  };

  return (
    <LayoutOnBoarding
      title="Let’s get to know you"
      alert={`You are registering with ${user?.email}`}
    >
      <DProfileImage
        label="Profile Image"
        name="accountImage"
        defaultImages={defaultImages?.results}
        isRounded
        imageUrl={accountImageUrl}
        imageFile={accountImage}
        handleImageChange={handleImageChange}
        setImageError={(name, errorMessage) => {
          setErrors((prevErrors) => ({
            ...prevErrors,
            [name]: errorMessage,
          }));
        }}
        error={errors.accountImage}
        required
      />

      <DInputBlock label="Full name" name="full_name" required>
        <DInput
          name="full_name"
          type="text"
          placeholder="Enter your full name"
          value={userData?.full_name}
          onChange={(e) =>
            handleChange({ full_name: e.target.value }, 'userData')
          }
          error={errors?.full_name}
          required
        />
      </DInputBlock>

      <div className="flex flex-col gap-size1 w-full">
        <DSelectSearch
          label="Job role"
          name="role"
          required
          options={jobRoles.sort((a, b) => a.label.localeCompare(b.label))}
          showOtherOptions
          value={userData?.job_role}
          onChange={(value) => handleChange({ job_role: value }, 'userData')}
          error={errors?.job_role}
          hideError={userData?.job_role === 'other'}
        />

        <DTransition show={userData?.job_role === 'other'}>
          <DInputBlock label="Specify your role" name="role_others" hiddenLabel>
            <DInput
              name="role_others"
              type="text"
              placeholder="Specify your role"
              value={userData?.role_others}
              onChange={(e) =>
                handleChange({ role_others: e.target.value }, 'userData')
              }
              error={errors?.role_others}
            />
          </DInputBlock>
        </DTransition>
      </div>

      <div className="flex flex-col gap-size1 w-full">
        <DSelectSearch
          label="How did you find out about us?"
          name="role"
          required
          options={howFindUs}
          showOtherOptions
          value={userData?.referral_source}
          onChange={(value) =>
            handleChange({ referral_source: value }, 'userData')
          }
          error={errors?.referral_source}
          hideError={userData?.referral_source === 'other'}
        />

        <DTransition show={userData?.referral_source === 'other'}>
          <DInputBlock
            label="Specify where"
            name="referral_source_others"
            hiddenLabel
          >
            <DInput
              name="referral_source_others"
              type="text"
              placeholder="Specify"
              value={userData?.referral_source_others}
              onChange={(e) =>
                handleChange(
                  { referral_source_others: e.target.value },
                  'userData'
                )
              }
              error={errors?.referral_source_others}
            />
          </DInputBlock>
        </DTransition>
      </div>

      <DInputBlock label="Company name" name="company_name">
        <DInput
          name="company_name"
          type="text"
          placeholder="Enter your company name"
          value={userData?.company_name}
          onChange={(e) =>
            handleChange({ company_name: e.target.value }, 'userData')
          }
          error={errors?.company_name}
        />
      </DInputBlock>

      <div className="flex gap-size3 w-full">
        {/* <DButton
          variant="grey"
          size="lg"
          className="w-max"
          onClick={() => setCurrentStep(SignUpStepsEnum.PASSWORD)}
        >
          Go Back
        </DButton> */}
        <DButton
          type="submit"
          variant="dark"
          size="lg"
          fullWidth
          onClick={handleRegisterProfile}
          loading={isLoading}
        >
          Continue
        </DButton>
      </div>
    </LayoutOnBoarding>
  );
};

export default RegisterProfile;
