const CustomIcon = (props) => {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 1C8 0.723858 8.22386 0.5 8.5 0.5H12C12.2761 0.5 12.5 0.723858 12.5 1C12.5 1.27614 12.2761 1.5 12 1.5H10.75V3H12.5C13.3284 3 14 3.67157 14 4.5V9.5C14 10.3284 13.3284 11 12.5 11H10.75V12.5H12C12.2761 12.5 12.5 12.7239 12.5 13C12.5 13.2761 12.2761 13.5 12 13.5H8.5C8.22386 13.5 8 13.2761 8 13C8 12.7239 8.22386 12.5 8.5 12.5H9.75V1.5H8.5C8.22386 1.5 8 1.27614 8 1ZM10.75 10H12.5C12.7761 10 13 9.77614 13 9.5V4.5C13 4.22386 12.7761 4 12.5 4H10.75V10ZM0 4.5C0 3.67157 0.671573 3 1.5 3H7.5C7.77614 3 8 3.22386 8 3.5C8 3.77614 7.77614 4 7.5 4H1.5C1.22386 4 1 4.22386 1 4.5V9.5C1 9.77614 1.22386 10 1.5 10H7.5C7.77614 10 8 10.2239 8 10.5C8 10.7761 7.77614 11 7.5 11H1.5C0.671573 11 0 10.3284 0 9.5V4.5Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default CustomIcon;
