import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import ChatbotDashboard from '@/components/Chatbot/Dashboard';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import * as chatbotService from '@/services/chatbot.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import useToast from '@/hooks/useToast';
import DLoading from '@/components/DLoading';
import { useUserStore } from '@/stores/user/userStore';
import { ProductFruits } from 'react-product-fruits';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import * as userService from '@/services/user.service';
import userflow from 'userflow.js'

const Home = () => {
  // Store hooks and state
  const setChatbots = useChatbotStore((state) => state.setChatbots);
  const chatbots = useChatbotStore((state) => state.chatbots);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const selectedTeam = useTeamManagementStore((state) => state.selectedTeam);
  const { user } = useUserStore((state) => state);
  const setUser = useUserStore((state) => state.setUser);

  const [userProps, setUserProps] = useState({});
  const { response: tooltipsShownResponse } = useDanteApi(userService.getTooltipsShownForUser);
  const { response, data, isLoading, error } = useDanteApi(
    chatbotService.getChatbotsDashboard,
    [selectedTeam]
  );
  const { addWarningToast } = useToast();

  const [hideTooltips, setHideTooltips] = useState(false);
  const [shouldLoadScript, setShouldLoadScript] = useState(false);

  const handleShowTooltips = async () => {
    try{
      const response = await userService.setTooltipsShownForUser();
    }catch(e){
      console.log('error', e);
    }
  };

  const getUserProfile = async () => {
    try {
      const response = await userService.getUserProfile();
      if (response.status === 200) {
        setUser(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
    }
  };

  useEffect(() => {
    if (tooltipsShownResponse?.data) {
      if (tooltipsShownResponse.data.bubble_tooltips_shown) {
        sessionStorage.setItem('tooltipShown', true);
      } else {
        handleShowTooltips();
      }
    }
  }, [tooltipsShownResponse]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldLoadScript(true);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    setChatbots(data?.results);
    if (data?.message) {
      addWarningToast({ message: data?.message });
    }
  }, [data, isLoading, setChatbots]);

  useEffect(() => {
    setProgressBar([]);
    setLayoutTitle('');
  }, []);

  useEffect(() => {
    if (!user || !user.email || !chatbots) return;

    const {
      email = '',
      date_created: signUpAt = '',
      has_created_chatbot: hasCreatedChatbot,
      login_count: loginCount,
      applied_one_dollar_bf_starter: appliedOneDollarBFStarter,
      applied_25_percent_bf_discount: applied25PercentBFDiscount,
      new_design_trial_days_left: newDesignTrialDaysLeft,
      new_design_trial: newDesignTrial,
    } = user;

    const isFirstChatbot = hasCreatedChatbot && (chatbots.length === 1 || hasCreatedChatbot);

    setUserProps({
      username: email,
      email,
      signUpAt,
      props: {
        first_chatbot: isFirstChatbot,
        login_count: loginCount,
        applied_one_dollar_bf_starter: appliedOneDollarBFStarter,
        applied_25_percent_bf_discount: applied25PercentBFDiscount,
        has_created_chatbot: hasCreatedChatbot,
        new_design_trial: newDesignTrial,
        new_design_trial_days_left: newDesignTrialDaysLeft,
        chatbot_id: chatbots.length > 0 ? chatbots[0]?.kb_id : null,
      },
    });

    userflow.identify(user.email, {
      name: user.name,
      email: user.email,
      signed_up_at: user.date_created,
      device_type: window.innerWidth > 800 ? 'desktop' : 'mobile'
     })
  }, [user, chatbots]);

  useEffect(() => {
    if (!user || !user.email) {
      const bubbleScript = document.querySelector(
        'script[src*="bubble-embed.js"]'
      );
      if (bubbleScript) {
        bubbleScript.remove();
      }
      getUserProfile();
    }
  }, [user]);

  if (isLoading || !user || user.email === '' || !userProps.username) {
    return <DLoading show={true} />;
  }

  const bubbleScriptUrl = 'https://app.dante-ai.com/bubble-embed.js?kb_id=15d4d80d-fe73-48fd-af67-2136555bb10c&token=20ab9419-baaf-422d-a73b-1661e51aed28&modeltype=gpt-4-omnimodel-mini&tabs=false}';

  return (
    <>
      {/* {user && user.email && shouldLoadScript && (
        <Helmet>
          <script
            key={`bubble-embed-${user.email}`}
            src={bubbleScriptUrl}
            type="text/javascript"
          ></script>
        </Helmet>
      )} */} 
      <ChatbotDashboard chatbots={chatbots} />
      <ProductFruits
        workspaceCode={
          window.location.href.includes('https://app.dante-ai.com/')
            ? import.meta.env.VITE_APP_PRODUCT_FRUIT_PROD
            : import.meta.env.VITE_APP_PRODUCT_FRUIT_STAGING
        }
        language="en"
        user={userProps}
      />
    </>
  );
};

export default Home;