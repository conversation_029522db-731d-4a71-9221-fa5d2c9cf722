import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import clsx from 'clsx';

import DButton from '../../Global/DButton';
import DInputEditable from '../../Global/DInputEditable';
import DSwitch from '../../Global/DSwitch';
import CoinsIcon from '../../Global/Icons/CoinsIcon';
import ConversationsIcon from '../../Global/Icons/ConversationsIcon';
import FlagIcon from '../../Global/Icons/FlagIcon';
import UpRightIcon from '@/components/Global/Icons/UpRightIcon';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
import AiAvatarIcon from '@/components/Global/Icons/AiAvatarIcon';
import ConnectIcon from '@/components/Global/Icons/ConnectIcon';
import AiVoiceIcon from '@/components/Global/Icons/AiVoiceIcon';
import PhoneIcon from '@/components/Global/Icons/PhoneIcon';
import ChatbotActions from '../ChatbotActions';

import ChatbotShortcutsChart from './ShortcutsChart';
import ShortcutsGroup from './ShortcutsGroup';
import DModal from '@/components/Global/DModal';
import DModalShareChatbot from '../DModalShareChatbot';
import DCircularChart from '@/components/Global/DCircularChart';
import DTooltip from '@/components/Global/DTooltip';
import GeneralStats from '../GeneralStats';
import {
  deleteChatbot,
  patchUpdateChatbotName,
} from '@/services/chatbot.service';
import useDanteApi from '@/hooks/useDanteApi';
import * as customizationService from '@/services/customization.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useToast from '@/hooks/useToast';
import DLoading from '@/components/DLoading';
import { LLM_MODEL_DEFAULT } from '@/constants';
import BlurredOverlay from '@/components/BlurredOverlay';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import { getUserProfile } from '@/services/user.service';
import { useUserStore } from '@/stores/user/userStore';
import VoicePreview from '@/components/Voice/VoicePreview';

const ChatbotShortcuts = ({
  customization,
  data,
  loading,
  refetchOverview,
  onVoicePreviewStateChange,
}) => {
  const { addSuccessToast } = useToast();

  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);

  const setSelectedName = useChatbotStore((state) => state.setSelectedName);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showShareChatbot, setShowShareChatbot] = useState(false);
  const [showRealtimeVoiceModal, setShowRealtimeVoiceModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [chatbotOverview, setChatbotOverview] = useState({
    name: '',
    connection_stats: {
      total_avatars: 0,
      total_integrations: 0,
      total_voices: 0,
    },
    insights: {
      rate_success: 0,
      flagged_messages: 0,
      stats: [],
    },
    safety: {
      credit_limit: 0,
      enabled: false,
      message_limit: 0,
    },
  });

  const [kbName, setKbName] = useState(data?.name);
  const setUser = useUserStore((state) => state.setUser);

  const navigate = useNavigate();

  // Reference to track the VoicePreview's playing state
  const [voicePreviewPlaying, setVoicePreviewPlaying] = useState(false);
  
  // Update parent component when voice preview state changes
  useEffect(() => {
    if (onVoicePreviewStateChange) {
      onVoicePreviewStateChange(voicePreviewPlaying);
    }
  }, [voicePreviewPlaying, onVoicePreviewStateChange]);

  /**
   * Handles chatbot activation toggle.
   * @param {boolean} checked - New state of the toggle.
   */
  const handleEnableChatbot = async (checked) => {
    try {
      await customizationService.toggleChatbotActivation(
        customization?.kb_id,
        checked
      );
      setChatbotOverview({
        ...chatbotOverview,
        safety: { ...chatbotOverview.safety, enabled: checked },
      });
      addSuccessToast({
        message: `AI Chatbot ${
          checked ? 'activated' : 'deactivated'
        } successfully`,
      });
    } catch (error) {
      console.error('Error enabling chatbot:', error);
    }
  };

  /**
   * Updates the chatbot name.
   */
  const handleUpdateChatbotName = async () => {
    try {
      await patchUpdateChatbotName({
        kb_id: customization?.kb_id,
        name: kbName,
      });
      setSelectedName(kbName);
      addSuccessToast({
        message: 'Your AI Chatbot name has been updated successfully',
      });
      refetchOverview();
    } catch (error) {
      console.error('Error updating AI Chatbot name:', error);
    }
  };

  const handleShareChatbot = () => {
    setShowShareChatbot(true);
  };

  useEffect(() => {
    if (data) {
      const chartData = Object.keys(data?.insights?.stats || {}).map((key) => ({
        date: key,
        value: data.insights.stats[key],
      }));

      setKbName(data.name || '');
      setChatbotOverview({
        ...data,
        insights: { ...data.insights, stats: chartData },
      });
    }
  }, [data]);

  // Function to attach to window so VoicePreview can update us
  useEffect(() => {
    // Create a function that VoicePreview can call
    window.__chatbotShortcutsVoicePlayingHandler = (isPlaying) => {
      setVoicePreviewPlaying(isPlaying);
    };

    // Clean up when component unmounts
    return () => {
      delete window.__chatbotShortcutsVoicePlayingHandler;
    };
  }, []);

  if (loading) {
    return <DLoading show={true} />;
  }

  return (
    <>
      <div className="flex flex-col gap-size4 w-full md:w-[360px] 3xl:w-[440px] md:h-full md:overflow-y-auto md:overflow-x-hidden scrollbar justify-between pb-size4 sm:pb-0">
        <div className="flex flex-col gap-size3 xl:gap-size2 2xl:gap-size4 px-size1">
          {/* Activation Toggle */}
          {checkTeamManagementPermission('edit_chatbot_status') && (
            <div
              className={clsx(
                'flex p-size1 rounded-size0 border items-center',
                chatbotOverview?.safety?.enabled
                  ? 'bg-green-5 border-green-10'
                  : 'bg-grey-2 border-grey-5'
              )}
            >
              <DSwitch
                checked={chatbotOverview?.safety?.enabled}
                variant="positive"
                onChange={handleEnableChatbot}
                label={
                  chatbotOverview?.safety?.enabled
                    ? 'AI Chatbot active'
                    : 'AI Chatbot inactive'
                }
                size="sm"
              />
              <DTooltip content="Activate AI Chatbot. If disabled, the AI Chatbot will not be visible to users">
                <InfoIcon className="text-grey-50 ml-size0 size-3" />
              </DTooltip>
            </div>
          )}

          {/* Editable Chatbot Name */}
          {checkTeamManagementPermission('edit_chatbot_name') && (
            <DInputEditable
              value={kbName}
              onChange={({ target: { value } }) => setKbName(value)}
              onEdit={() => handleUpdateChatbotName()}
              is_editable
              className="!h-auto"
              size="xl"
            />
          )}
          {/* Connection Stats */}
          <ShortcutsGroup title="">
            <GeneralStats
              Icon={ConnectIcon}
              value={chatbotOverview?.connection_stats?.total_integrations}
              labelSingular="Active Integration"
              labelPlural="Active Integrations"
              status={status}
            />
            {/* <GeneralStats
              Icon={AiAvatarIcon}
              value={chatbotOverview?.connection_stats?.total_avatars}
              labelSingular="Persona connected"
              labelPlural="Personas connected"
              status={status}
              beta={true}
            /> */}
            <GeneralStats
              Icon={AiVoiceIcon}
              value={chatbotOverview?.connection_stats?.total_voices}
              labelSingular="Voice connected"
              labelPlural="Voices connected"
              status={status}
              beta={true}
            />
          </ShortcutsGroup>

          {/* Quick Insights */}
          <ShortcutsGroup
            title="Quick insights"
            icon={<UpRightIcon width={16} height={16} />}
            actionClick={() =>
              navigate(`/chatbot/${customization?.kb_id}/insights`)
            }
          >
            <div className="flex items-start gap-size1">
              <DCircularChart
                value={chatbotOverview?.insights?.rate_success}
                variant="positive"
              />
              <div className="flex gap-size1 items-end">
                <p className="text-2xl tracking-tighter">
                  {chatbotOverview?.insights?.rate_success}%
                </p>
                <p className="text-base tracking-tight">Satisfaction rate</p>
              </div>
            </div>
            <ChatbotShortcutsChart data={chatbotOverview?.insights?.stats} />
            <div className="flex flex-row text-grey-50 gap-size0">
              <FlagIcon className="text-negative-100" />
              <span>
                {chatbotOverview?.insights?.flagged_messages} Flagged message
                {chatbotOverview?.insights?.flagged_messages !== 1 && 's'}
              </span>
            </div>
          </ShortcutsGroup>

          {/* Safety Section */}
          <ShortcutsGroup
            title="Safety"
            icon={<UpRightIcon width={16} height={16} />}
            actionClick={() =>
              navigate(`/chatbot/${customization?.kb_id}/safety`)
            }
          >
            <div className="flex gap-size0 text-grey-50">
              <CoinsIcon width={24} height={24} />
              {chatbotOverview?.safety?.credit_limit === 0
                ? 'No credit allowance limit'
                : `${chatbotOverview?.safety?.credit_limit.toLocaleString(
                    'en-US'
                  )} credit allowance limit`}
            </div>
            <div className="flex gap-size0 text-grey-50">
              <ConversationsIcon width={24} height={24} />
              {chatbotOverview?.safety?.message_limit === 0
                ? 'No conversation message limit'
                : `${chatbotOverview?.safety?.message_limit.toLocaleString(
                    'en-US'
                  )} conversation message limit`}
            </div>
          </ShortcutsGroup>
        </div>

        <div className='flex flex-col gap-size1 mt-auto xl:mt-size1 2xl:mt-auto'>
            <div className='flex flex-col gap-0'>
              <div className='flex items-center justify-between px-size1'>
                <DButton onClick={() => setShowRealtimeVoiceModal(true)} className='w-full !pl-0'>
                  <span className='text-sm text-grey-75 underline'>Integrate AI Voice</span>
                </DButton>
                <DTooltip content="This will cost 7 credits per minute when you preview the AI Voice Agent.">
                  <div className='flex items-center  gap-size1'>
                    <p className='text-xs text-grey-50'>Consumes Credits</p>
                    <InfoIcon className="text-grey-50  size-3 " />
                  </div>
                </DTooltip>
              </div>
            <div className='rounded-size1 bg-black scale-70 origin-top-left h-[180px] md:h-[180px] lg:h-[180px] xl:h-[140px] 2xl:h-[180px]'>
              <VoicePreview
                chatbotId={customization?.kb_id}
                voiceId="a56a7938-bd6b-44b4-8e52-2c652946d528"
                welcomeMessage={`I'm your AI Voice Assistant, fully trained on your chatbot data. Ask me anything connected to your chatbot data, and I'll provide the answers. Let's begin! ${customization?.initial_messages?.map(msg => msg.content).join(' ')}`}
                phoneNumbers={customization?.phone_numbers}
                personalityPrompt={customization?.personality_prompt}
                hideCloseButton={true}
                place="chatbot-shortcuts"
              />
            </div>
          </div>
        </div>
        <div className="mt-auto flex md:hidden flex-col gap-size1">
          <ChatbotActions 
            chatbotId={customization?.kb_id}
            selectedChatbot={selectedChatbot}
            variant="mobile"
          />
        </div>
      </div>
      <DModalShareChatbot
        kb_id={customization?.kb_id}
        llm_model={selectedChatbot?.last_model_used?.value || LLM_MODEL_DEFAULT.value}
        isOpen={showShareChatbot}
        onClose={() => setShowShareChatbot(false)}
      />
      <DModalRealtimeVoice
        isOpen={showRealtimeVoiceModal}
        onClose={() => setShowRealtimeVoiceModal(false)}
        chatbotId={customization?.kb_id}
        chatbotName={kbName}
      />
    </>
  );
};

// New modal component for Realtime Voice
const DModalRealtimeVoice = ({ isOpen, onClose, chatbotId, chatbotName }) => {
  const navigate = useNavigate();
  return (
    <DModal
      isOpen={isOpen}
      onClose={onClose}
      title="Integrate AI Voice"
      // className="!w-[600px] max-w-none !p-size2 !pt-size1 !rounded-2xl overflow-hidden"
      contentBgColor='bg-white'
    >
      <div className="flex items-center gap-size1">
        <div
          onClick={() => {
            navigate('/voice/create', {
              state: {
                preSelectedChatbot: {
                  label: chatbotName,
                  value: chatbotId
                },
                defaultName: chatbotName
              }
            });
          }}
          className="flex flex-1 flex-col items-center rounded-lg border border-border p-6 text-center transition-all hover:bg-purple-5 hover:shadow-md cursor-pointer"
        >
          <div className="mb-4 rounded-full bg-primary/10 p-3 text-primary">
           <PhoneIcon className='text-purple-300 size-6' />
          </div>
          <h3 className="mb-2 text-lg font-medium">Create AI Voice Agent</h3>
          <p className="text-xs text-grey-50 leading-[1.2]">
            Create a custom AI voice agent and connect it to a phone number.
          </p>
        </div>
        <div
          onClick={() => navigate(`/chatbot/${chatbotId}/powerups`)}
          className="flex flex-1 flex-col items-center rounded-lg border border-border p-6 text-center transition-all hover:bg-purple-5 hover:shadow-md cursor-pointer"
        >
          <div className="mb-2 rounded-full bg-primary/10 p-3 text-primary">
           <AiVoiceIcon className='text-purple-300 size-8' />
          </div>
          <h3 className="mb-2 text-lg font-medium">Enable real-time voice</h3>
          <p className="text-xs text-grey-50 leading-[1.2]">
            Enable real-time voice within this AI chatbot.
          </p>
        </div>
        </div>
    </DModal>
  );
};

export default ChatbotShortcuts;
