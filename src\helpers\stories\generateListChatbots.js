import { dataChart } from './generateDateChart';

export const infoChatbot = {
  avatars: {
    value: 12,
    status: 'active'
  },
  flagged_messages: {
    value: 12,
    status: 'active'
  },
  active_integrations: {
    value: 1,
    status: 'active'
  }
};

export const listFakeChatbots = Array.from({ length: 24 }, (value, index) => index).map((i) => ({
  knowledge_base: {
    knowledge_base_name: 'Chatbot 1'
  },
  info: infoChatbot,
  stats: dataChart
}));
