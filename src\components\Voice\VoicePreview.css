/* VoicePreview.css */
.voice-preview-container {
  background-color: black;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.voice-ui {
  background: radial-gradient(50% 50% at 50% 50%, rgba(97, 0, 255, 0.25) 0%, rgba(97, 0, 255, 0) 100%);
  backdrop-filter: blur(56px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.voice-preview-container:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.voice-preview-card {
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #f0f0ff;
  transition: all 0.3s ease;
}

.voice-preview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.voice-preview-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.voice-preview-badge.connected {
  background-color: #e6f7e6;
  color: #2e7d32;
}

.voice-preview-badge.disconnected {
  background-color: #f5f5f5;
  color: #757575;
}

.voice-preview-badge.error {
  background-color: #ffebee;
  color: #c62828;
}

.voice-preview-badge .dot {
  height: 8px;
  width: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.voice-preview-badge.connected .dot {
  background-color: #2e7d32;
}

.voice-preview-badge.disconnected .dot {
  background-color: #757575;
}

.voice-preview-badge.error .dot {
  background-color: #c62828;
}

.phone-number-tag {
  display: inline-flex;
  padding: 2px 8px;
  background-color: white;
  border-radius: 12px;
  font-size: 0.75rem;
  margin-right: 4px;
  margin-bottom: 4px;
  border: 1px solid #f0f0ff;
}

.phone-number-tag:hover {
  background-color: #f9f7ff;
}

.voice-preview-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

@media (min-width: 640px) {
  .voice-preview-button {
    width: 44px;
    height: 44px;
  }
}

@media (min-width: 768px) {
  .voice-preview-button {
    width: 48px;
    height: 48px;
  }
}

.voice-preview-button.primary {
  background-color: #6200ee;
  color: white;
}

.voice-preview-button.primary:hover:not(:disabled) {
  background-color: #3700b3;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transform: scale(1.05);
}

.voice-preview-button.secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.voice-preview-button.secondary:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.voice-preview-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.voice-preview-controls {
  position: absolute;
  bottom: 16px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 12px;
  z-index: 10;
}

@media (min-width: 640px) {
  .voice-preview-controls {
    bottom: 20px;
    gap: 14px;
  }
}

@media (min-width: 768px) {
  .voice-preview-controls {
    bottom: 24px;
    gap: 16px;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
