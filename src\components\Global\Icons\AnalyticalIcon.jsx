const AnalyticalIcon = (props) => {
  return (
    <svg
      width="22"
      height="20"
      viewBox="0 0 22 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M2.75098 1V12.25C2.75098 12.8467 2.98803 13.419 3.40999 13.841C3.83194 14.2629 4.40424 14.5 5.00098 14.5H7.25098M2.75098 1H1.25098M2.75098 1H19.251M7.25098 14.5H14.751M7.25098 14.5L6.25098 17.5M19.251 1H20.751M19.251 1V12.25C19.251 12.8467 19.0139 13.419 18.592 13.841C18.17 14.2629 17.5977 14.5 17.001 14.5H14.751M14.751 14.5L15.751 17.5M6.25098 17.5H15.751M6.25098 17.5L5.75098 19M15.751 17.5L16.251 19M6.50098 10L9.50098 7L11.649 9.148C12.6582 7.69929 13.9731 6.48982 15.501 5.605"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default AnalyticalIcon;
