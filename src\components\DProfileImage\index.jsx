import { useEffect, useRef, useState } from 'react';
import DInputBlock from '../Global/DInput/DInputBlock';
import ValidationError from '../Global/ValidationError';
import PropTypes from 'prop-types';
import { ACCEPTED_IMAGE_TYPES } from '@/constants';
import AddImageIcon from '../Global/Icons/AddImageIcon';
import clsx from 'clsx';
import DButtonIcon from '../Global/DButtonIcon';
import DeleteIcon from '../Global/Icons/DeleteIcon';

const DProfileImage = ({
  label,
  name,
  isRounded = false,
  imageUrl,
  imageFile,
  defaultImages,
  handleImageChange,
  setImageError,
  error,
  maxSizeFile = 150 * 1024,
  required = false,
  showDefaultImages = true,
  readOnly = false,
}) => {
  const ratioWidth = 1;
  const ratioHeight = 1;
  const ratio = ratioWidth / ratioHeight;
  const tolerance = 0.1;

  const accountImageRef = useRef(null);

  useEffect(() => {
    const checkImageSize = async () => {
      const newErrors = [];
      let hasErrors = false;
      if (imageFile instanceof File) {
        if (imageFile.size > maxSizeFile) {
          newErrors.push(`Max file size: ${maxSizeFile / 1024}kb`);
          hasErrors = true;
        } else {
          const isImageValid = await new Promise((resolve) => {
            const image = new Image();
            image.src = URL.createObjectURL(imageFile);
            image.onload = () => {
              const imageRatio = image.width / image.height;
              const compare = Math.abs(imageRatio - ratio) <= tolerance;
              if (compare) {
                resolve(true);
              } else {
                resolve(false);
              }
            };
            image.onerror = () => resolve(false);
          });

          if (!isImageValid) {
            newErrors.push(
              `Image not in the correct ratio: ${ratioWidth}:${ratioHeight}`
            );
            hasErrors = true;
          }
        }
      }

      setImageError(name, newErrors.join(', '));

      return !hasErrors;
    };
    checkImageSize();
  }, [imageFile]);

  const handleClick = () => {
    if (!readOnly) {
      accountImageRef.current.click();
    }
  };

  return (
    <DInputBlock label={label} required={required}>
      <div className="flex items-center gap-size2">
        <div>
          <input
            type="file"
            className="hidden"
            onChange={(e) => handleImageChange(e.target.files[0])}
            ref={accountImageRef}
            accept={ACCEPTED_IMAGE_TYPES.join(',')}
          />
          {imageUrl ? (
            <div className="relative">
              <img
                src={typeof imageUrl === 'string' ? imageUrl : imageFile}
                alt="team icon"
                className={clsx(
                  'w-20 h-20 rounded',
                  isRounded ? 'rounded-full' : 'rounded-size2',
                  readOnly ? 'cursor-default' : 'cursor-pointer'
                )}
                onClick={handleClick}
              />
              {!readOnly && (
                <DButtonIcon
                  size="xs"
                  variant="light"
                  className="absolute bottom-0 right-0"
                  onClick={() => handleImageChange('')}
                >
                  <DeleteIcon />
                </DButtonIcon>
              )}
            </div>
          ) : (
            <button
              className={clsx(
                'dbutton size-20 bg-grey-100 border border-dashed border-grey-10 flex items-center justify-center ',
                isRounded ? 'rounded-full' : 'rounded-size2',
                readOnly ? 'cursor-default' : 'cursor-pointer'
              )}
              disabled={readOnly}
              onClick={handleClick}
            >
              <AddImageIcon />
            </button>
          )}
        </div>
        <div
          className={`flex-col gap-size0 min-h-10 ${
            showDefaultImages ? 'flex' : 'hidden'
          }`}
        >
          <div className="flex items-center gap-size0">
            <p className="text-xs font-medium tracking-tight">
              Choose from defaults
            </p>
          </div>
          <div className="flex flex-wrap gap-size2 min-h-5">
            {defaultImages?.map((image, index) => (
              <img
                src={image?.small_image_url}
                alt="team icon"
                className={`size-5 rounded ${!readOnly ? 'cursor-pointer' : 'cursor-default'} ${
                  imageUrl === image?.small_image_url
                    ? `shadow-[0px_0px_0px_4.67px_#8275F733] ${
                        isRounded ? 'rounded-full' : 'rounded-size2'
                      }`
                    : ''
                }`}
                onClick={() => {
                  if (!readOnly) {
                    handleImageChange(image?.big_image_url);
                  }
                }}
                key={index}
              />
            ))}
          </div>
        </div>
      </div>
      <span
        className={clsx(
          'text-xs tracking-tight text-grey-20',
          showDefaultImages ? 'visible' : 'invisible'
        )}
      >
        Format: PNG, JPEG, JPG • Image ratio: {ratioWidth}:{ratioHeight} • Size:{' '}
        {maxSizeFile / 1024}kb max
      </span>
      <ValidationError error={error} />
    </DInputBlock>
  );
};

DProfileImage.propTypes = {
  label: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  isRounded: PropTypes.bool,
  imageUrl: PropTypes.string,
  imageFile: PropTypes.instanceOf(File),
  defaultImages: PropTypes.array,
  handleImageChange: PropTypes.func,
  setImageError: PropTypes.func,
  maxSizeFile: PropTypes.number,
  error: PropTypes.array,
};

export default DProfileImage;
