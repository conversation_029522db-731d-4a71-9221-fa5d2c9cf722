import OnboardingTiers from '../OnboardingTiers';
import { SignUpStepsEnum } from './signUpSteps';
import * as authService from '@/services/auth.service';
const ChooseTrial = ({ setTrial, setCurrentStep, googleSignup }) => {
  const handleUpgrade = async (tierId, tierType) => {
    try{
      if (googleSignup) {
        const response = await authService.saveUserTier(tierType.toLowerCase());
        if (response.status === 200) {
          setCurrentStep(SignUpStepsEnum.PROFILE);
        }
      }else {
        setTrial(tierType.toLowerCase());
        setCurrentStep(SignUpStepsEnum.PASSWORD);
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="max-h-[90vh] overflow-auto flex flex-col gap-size5 p-size2 no-scrollbar">
      <OnboardingTiers handleUpgrade={handleUpgrade} />
    </div>
  );
};

export default ChooseTrial;
