// Audio utility functions for the AI Voice Demo

// Convert Float32Array to Int16Array for audio processing
export const convertFloat32ToS16PCM = (float32Array) => {
  let int16Array = new Int16Array(float32Array.length);

  for (let i = 0; i < float32Array.length; i++) {
    let clampedValue = Math.max(-1, Math.min(1, float32Array[i]));
    int16Array[i] = clampedValue < 0 ? clampedValue * 32768 : clampedValue * 32767;
  }
  return int16Array;
};

// Constants
export const SAMPLE_RATE = 16000;
export const NUM_CHANNELS = 1;
export const PLAY_TIME_RESET_THRESHOLD_MS = 1.0;
export const INITIAL_BUFFER_TIME = 0.15; // Increased from 0.1 for smoother startup
export const PLAYBACK_BUFFER_SIZE = 8192; // Increased from 4096 for smoother playback
export const JITTER_BUFFER_SIZE = 3; // Increased from 2 for better buffering
