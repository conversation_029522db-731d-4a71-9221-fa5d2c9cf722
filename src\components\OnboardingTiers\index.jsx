import DSwitch from '../Global/DSwitch';
import { useState } from 'react';
import CheckmarkIcon from '../Global/Icons/CheckmarkIcon';
import CloseIcon from '../Global/Icons/CloseIcon';
import { Star } from '../Onboarding/ReviewTrustPilot/Stars';
import useDanteApi from '@/hooks/useDanteApi';
import * as plansService from '@/services/plans.service';
import { useSearchParams } from 'react-router-dom';
import DLoading from '../DLoading';
import DButton from '../Global/DButton';

const OnboardingTiers = ({ handleUpgrade }) => {
  const [searchParams] = useSearchParams();
  const { data: tiers, loading: tiersLoading } = useDanteApi(
    plansService.getOnboardingTiers
  );
  const [period, setPeriod] = useState('monthly');

  if (tiersLoading) return <DLoading show={true} />;

  return (
    <div className="flex flex-col gap-size5 p-size0 md:p-size5">
      {/* Header */}
      <div className="flex flex-col gap-size2 items-center">
        <div className="flex gap-size0 items-center">
          <Star className="size-5" />
          <p className="text-lg text-grey-75">
            <span className="font-bold">4.7</span> "Dante AI was a saving grace."
          </p>
        </div>
        <h1 className="text-3xl md:text-4xl font-medium tracking-tight text-center">
          {searchParams.get('trial_ended') !== 'true' ? (
            'Choose Your Plan and Start Building Today!'
          ) : (
            <>
              We hope you've enjoyed your trial with Dante AI.
              <br />
              Choose your plan below and start building today.
            </>
          )}
        </h1>
        {searchParams.get('trial_ended') !== 'true' && (
          <p className="text-md md:text-lg text-grey-75 text-center">
            Start Your 14-Day Free Trial {searchParams.get('old_to_new') === 'true' ? '.' : '— No commitments. No credit card.'} Just your ideas, our tools.
          </p>
        )}
      </div>

      {/* Switch */}
      <div className="flex gap-size2 items-center justify-center">
        <DSwitch
          checked={period === 'yearly'}
          onChange={(value) => setPeriod(value ? 'yearly' : 'monthly')}
          label="Annual"
        />
        <div className="flex p-size1 items-center bg-purple-10 text-purple-300 rounded-size1">
          <p className="text-sm tracking-tight">2 Months Free</p>
        </div>
      </div>

      {/* Grid of Cards */}
      <div
        className="
          grid
          grid-cols-1
          md:grid-cols-2
          lg:grid-cols-4
          gap-size5
          p-size3
          items-stretch
        "
      >
        {tiers &&
          tiers[period].map((tier, index) => (
            <div
              key={index}
              className="
                flex
                flex-col
                h-full
                shadow-purple
                rounded-size2
                border
                border-grey-5
              "
            >
              {/* TOP SECTION: Plan Name, Price, Description, CTA */}
              <div
                className="
                  flex
                  flex-col
                  gap-size3
                  p-size5

                  /* Base min-height */
                  min-h-[340px]

                  /* Increase min-height at md (768px) */
                  md:min-h-[380px]

                  /* Then decrease slightly at lg (1024px) */
                  lg:min-h-[340px]

                  /* At xl (1280px and above), reduce further if you want */
                  xl:min-h-[320px]
                "
              >
                {/* Plan Name */}
                <p className="text-lg md:text-xl tracking-tight text-grey-50">
                  {tier.name}
                </p>

                {/* Price */}
                {tier.name !== 'Enterprise' ? (
                  <p className="text-xl md:text-2xl font-medium tracking-tight leading-tight whitespace-nowrap">
                    ${tier.price}{' '}
                    <span className="text-regular text-lg text-grey-50">
                      / {period === 'monthly' ? 'month' : 'year'}
                    </span>
                  </p>
                ) : (
                  <p className="text-xl md:text-2xl font-medium tracking-tight leading-tight">
                    Talk to our team
                  </p>
                )}

                {/* Description */}
                <p className="text-base tracking-tight text-grey-75">
                  {tier.description}
                </p>

                {/* CTA Button anchored at bottom */}
                <div className="mt-auto">
                  {tier.name !== 'Enterprise' ? (
                    <button
                      onClick={() => handleUpgrade(tier.id, tier.name, searchParams.get('old_to_new') === 'true')}
                      className="dbutton rounded-size1 p-size2 text-lg flex items-center justify-center text-white w-full"
                      style={{
                        background:
                          'radial-gradient(circle at center, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0) 100%), ' +
                          'radial-gradient(circle at center, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0) 100%), ' +
                          'radial-gradient(circle at center, rgba(255, 255, 255, 0.07) 0%, rgba(255, 255, 255, 0) 100%), ' +
                          '#6C5DED',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: 'cover',
                      }}
                    >
                      {searchParams.get('trial_ended') === 'true'
                        ? 'Select Plan'
                        : 'Start free trial'}
                    </button>
                  ) : (
                    <button
                      onClick={() => {
                        window.open(
                          'https://calendly.com/jetelira/enterprise-introduction',
                          '_blank'
                        );
                      }}
                      className="dbutton rounded-size1 p-size2 text-lg flex items-center justify-center text-white w-full"
                      style={{
                        background:
                          'radial-gradient(circle at center, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0) 100%), ' +
                          'radial-gradient(circle at center, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0) 100%), ' +
                          'radial-gradient(circle at center, rgba(255, 255, 255, 0.07) 0%, rgba(255, 255, 255, 0) 100%), ' +
                          '#6C5DED',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: 'cover',
                      }}
                    >
                      Contact Us
                    </button>
                  )}
                </div>
              </div>

              {/* Divider */}
              <div className="w-full h-[1px] bg-grey-5" />

              {/* BOTTOM SECTION: Features */}
              <div className="flex flex-col gap-size3 p-size5">
                <p className="text-base text-grey-75">What's included</p>
                {tier.features.map((feature, idx) => (
                  <div key={idx} className="flex gap-size1 items-center">
                    <p>
                      {feature.value ? (
                        <CheckmarkIcon />
                      ) : (
                        <CloseIcon className="text-grey-20" />
                      )}
                    </p>
                    <p
                      className={`text-sm ${
                        feature.value ? 'text-grey-75' : 'text-grey-50'
                      }`}
                    >
                      {feature.label}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};

export default OnboardingTiers;