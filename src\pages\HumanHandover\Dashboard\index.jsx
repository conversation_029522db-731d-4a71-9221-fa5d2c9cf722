import DFilterTable from '@/components/DFilterTable';
import FilterCard from '@/components/FilterCard';
import DBadge from '@/components/Global/DBadge';
import DButton from '@/components/Global/DButton';
import DButtonIcon from '@/components/Global/DButtonIcon';
import DInput from '@/components/Global/DInput/DInput';
import DMultiselect from '@/components/Global/DMultiselect';
import DSelect from '@/components/Global/DSelect';
import EyeIcon from '@/components/Global/Icons/EyeIcon';
import FlagIcon from '@/components/Global/Icons/FlagIcon';
import SearchIcon from '@/components/Global/Icons/SearchIcon';
import { DANTE_ICON, STATUS } from '@/constants';
import { useEffect, useRef, useState } from 'react';
import ChatBox from '../ChatBox';
import { useParams, useNavigate } from 'react-router-dom';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import * as humanHandoverService from '@/services/humanHandover';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { DateTime } from 'luxon';
import DDateRangePicker from '@/components/DDateRangePicker';
import DTooltip from '@/components/Global/DTooltip';
import formatDuration from '@/helpers/formatDuration';
import DLoading from '@/components/DLoading';
import UpRightIcon from '@/components/Global/Icons/UpRightIcon';
import { showNotificationWithSound, unlockAudioContext } from '@/helpers/notificationHelper';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';

const HumanHandoverDashboard = () => {
  const navigate = useNavigate();
  const { organization_id } = useParams();
  const chatbots = useChatbotStore((state) => state.chatbots);
  const { categories, showChatBox, addOpenedConversation, setShowChatBox, openedConversation, setLiveAgentConversationEvent } =
    useHumanHandoverStore((state) => state);

  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedState, setSelectedState] = useState('all');
  const [hashPingData, setHashPingData] = useState('');
  const [dateSelectedGeneral, setDateSelectedGeneral] = useState('Last 30 days');
  const timezone = useTimezoneStore((state) => state.timezone);
  const [timeRange, setTimeRange] = useState({
    date_from: DateTime.now()
      .setZone('UTC')
      .minus({ days: 30 })
      .startOf('day')
      .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    date_to: DateTime.now()
      .setZone('UTC')
      .endOf('day')
      .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
  });

  const [statistics, setStatistics] = useState({
    average_waiting_time: 0,
    live_conversations: 0,
    response_rate: 0,
    total_conversations: 0,
    total_count: 0,
    waiting_requests: 0,
  });
  const [rowsData, setRowsData] = useState([]);
  const [members, setMembers] = useState([]);
  const intervalPingData = useRef(null);
  const [isFirstRender, setIsFirstRender] = useState(true);
  const lastProcessedHash = useRef('');
  const lastNotificationHash = useRef('');

  const [checked, setChecked] = useState({
    totalConversations: false,
    waitingRequests: false,
    liveConversations: false,
  });
  const {
    data: conversationsDashboardData,
    isLoading: isConversationsDashboardLoading,
  } = useDanteApi(
    humanHandoverService.getConversationsDashboardData,
    [organization_id, selectedCategory, selectedState, timeRange, hashPingData, openedConversation?.map(conv => conv.category_id).join(',')],
    {},
    organization_id,
    {
      category_id: selectedCategory === 'all' ? null : selectedCategory,
      state: selectedState === 'all' ? null : selectedState,
      date_created_from: timeRange.date_from,
      date_created_to: timeRange.date_to,
      timezone: timezone,
    }
  );

  const filterOptions = [
    {
      label: 'Last 24 hours (demo only)',
      value: DateTime.now()
        .setZone('UTC')
        .minus({ hours: 24 })
        .startOf('day')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Week to date',
      value: DateTime.now()
        .setZone('UTC')
        .startOf('week')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Month to date',
      value: DateTime.now()
        .setZone('UTC')
        .startOf('month')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Last 7 days',
      value: DateTime.now()
        .setZone('UTC')
        .minus({ days: 7 })
        .startOf('day')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Last 30 days',
      value: DateTime.now()
        .setZone('UTC')
        .minus({ days: 30 })
        .startOf('day')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
    {
      label: 'Last 90 days',
      value: DateTime.now()
        .setZone('UTC')
        .minus({ days: 90 })
        .startOf('day')
        .toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    },
  ];

  const columns = [
    { key: 'id', label: 'ID', sortable: false, hideHeader: true },
    { key: 'requestNumber', label: 'Request number', sortable: true },
    { key: 'requestTime', label: 'Request time', sortable: true },
    { key: 'assignedTo', label: 'Assigned to', sortable: true },
    { key: 'chatbot', label: 'Chatbot' },
    { key: 'category', label: 'Category' },
    { key: 'state', label: 'State', sortable: true },
    { key: 'action', label: 'Action' },
  ];

  const handleOpenConversation = (conversation) => {
    if (window.innerWidth < 768) {
      navigate(`${window.location.pathname}/conversation/${conversation.id}`);
    } else {
      addOpenedConversation({ ...conversation, open: true });
      setShowChatBox(true);
    }
  };

  const showNotification = () => {
    console.log('showNotification');
    if (Notification.permission === 'granted') {
      new Notification('New Conversation', {
        body: 'You have new conversations data',
      });
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification('New Conversation', {
            body: 'You have new conversations data',
          });
        }
      });
    }
  };

  const getPingDashboardData = async () => {
    try {
      const response = await humanHandoverService.getPingDashboardData(organization_id);
      if (response.status === 200) {
        if (response.data.hash !== lastProcessedHash.current) {
          lastProcessedHash.current = response.data.hash;
          setHashPingData(response.data.hash);
        }

        if (response.data.notification_hash !== lastNotificationHash.current) {
          if (lastNotificationHash.current !== '') {
            showNotificationWithSound('New Message', {
              body: 'A user has requested to talk to a live agent',
              icon: DANTE_ICON,
            });
          }
          lastNotificationHash.current = response.data.notification_hash;
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const startPingDashboardData = () => {
    intervalPingData.current = setInterval(() => {
      getPingDashboardData();
    }, 3 * 1000);
  };

  const handleDateRangeChange = (fromDate, toDate) => {
    const fromDateTime = DateTime.fromISO(fromDate);
    const toDateTime = DateTime.fromISO(toDate);

    if (!fromDateTime.isValid || !toDateTime.isValid) {
      console.error('Invalid date format provided');
      return;
    }

    setTimeRange({
      date_from: fromDateTime.toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
      date_to: toDateTime.toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
      label: 'custom',
    });
  };

  const handleTakeConversation = async (conversation_id) => {
    try {
      const response = await humanHandoverService.takeConversation(conversation_id);
      if (response.status === 200) {
        if (window.innerWidth < 768) {
          navigate(`${window.location.pathname}/conversation/${conversation_id}`);
        } else {
          handleOpenConversation({
            ...response.data,
          });
        }
        setLiveAgentConversationEvent(conversation_id, {
          type: 'human_handover_taken',
          conversation_id,
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const generateRowsData = (conversations) => {
    console.log('Generating rows with:', {
      conversations: conversations,
      members: members,
    });
    const data = conversations.map((conversation) => {
      const foundMember = members.find((member) => member.user_response.id === conversation.user_id);
      console.log('Looking for member:', {
        conversationUserId: conversation.user_id,
        foundMember: foundMember,
      });
      const isTaken = conversation.state !== 'waiting' && conversation.state !== 'ai';
      return {
        id: conversation.is_flagged ? (
          <div className="flex items-center justify-center border border-orange-300 rounded-full size-7">
            <FlagIcon className="text-orange-300 size-4" />
          </div>
        ) : (
          ''
        ),
        requestNumber: `#${conversation.request_id.toString().padStart(4, '0')}`,
        requestTime: DateTime.fromISO(conversation.request_time, { zone: 'utc' }).toLocal().toFormat('d MMM yyyy - HH:mm:ss'),
        assignedTo: foundMember?.name,
        chatbot: chatbots?.find((chatbot) => chatbot.kb_id === conversation.kb_id)?.name,
        category: categories.find((category) => category.id === conversation.category_id)?.name,
        state: (
          <DBadge
            type={STATUS[conversation.state.toUpperCase()]}
            label={conversation.state.charAt(0).toUpperCase() + conversation.state.slice(1)}
            showIcon={false}
            size="md"
            className="w-max"
          />
        ),
        action: (
          <div className="flex items-center gap-size1">
            <div className="flex items-center gap-size1">
              <DButton
                variant="dark"
                size="sm"
                className="!h-6"
                onClick={() => handleOpenConversation(conversation)}
              >
                Open
              </DButton>
              {!isTaken && (
                <DButton
                  variant="grey"
                  size="sm"
                  className="!h-6"
                  onClick={() => handleTakeConversation(conversation.id)}
                >
                  Take
                </DButton>
              )}
              {isTaken && conversation?.last_messages?.length > 0 && (
                <DTooltip
                  content={`
                    <div class="flex flex-col gap-size1 min-w-40 max-w-80">
                      <p class="font-medium tracking-tight">Last messages:</p>
                      ${conversation?.last_messages?.map((message) => `
                        <div class="font-regular tracking-tight">
                          ${message?.question ? `<p class="font-regular tracking-tight">User: ${message?.question}</p>` : ''}
                          ${message?.answer ? `<p class="font-regular tracking-tight">Agent: ${message?.answer}</p>` : ''}
                        </div>
                      `).join('')}
                    </div>
                  `}
                >
                  <DButtonIcon variant="outlined" size="sm">
                    <EyeIcon className="size-4" />
                  </DButtonIcon>
                </DTooltip>
              )}
            </div>
            <DButtonIcon onClick={() => navigate(`conversation/${conversation.id}`)} className="ml-auto">
              <UpRightIcon className="size-4" />
            </DButtonIcon>
          </div>
        ),
      };
    });

    setRowsData(data);
  };

  useEffect(() => {
    // Initial call to set the hash
    getPingDashboardData();
    // Start the interval for subsequent calls
    startPingDashboardData();

    return () => clearInterval(intervalPingData.current);
  }, []);

  useEffect(() => {
    if (conversationsDashboardData) {
      generateRowsData(conversationsDashboardData.conversations);
      setMembers(conversationsDashboardData.members);
      setStatistics(conversationsDashboardData.statistics);
    }
  }, [conversationsDashboardData, members]);

  useEffect(() => {
    if (checked.totalConversations) {
      setSelectedState('all');
    } else if (checked.waitingRequests) {
      setSelectedState('waiting');
    } else if (checked.liveConversations) {
      setSelectedState('taken');
    }
  }, [checked]);

  useEffect(() => {
    const unlockAudio = () => {
      unlockAudioContext();
      document.removeEventListener('click', unlockAudio);
    };
    document.addEventListener('click', unlockAudio, { once: true });
  }, []);

  if (!conversationsDashboardData) {
    return <DLoading show={true} />;
  }

  return (
    <div className="bg-white h-full grow p-size3 md:p-size5 flex flex-col gap-size4 md:gap-size5 overflow-y-auto">
      <div className="border border-grey-5 rounded-size1 flex flex-col gap-size3 p-size3">
        <div className="flex justify-between items-center flex-col md:flex-row">
          <p className="text-2xl font-medium w-full">Conversations</p>
          <div className="flex items-center gap-size1 w-full md:w-2/5 flex-col md:flex-row">
            {/* <DInput
              iconPlacement="pre"
              icon={<SearchIcon />}
              placeholder="Search"
              variant="outline"
              fullWidth
            /> */}
            <DSelect
              options={filterOptions}
              onChange={(value) => {
                setDateSelectedGeneral(
                  filterOptions.find((option) => option.value === value)?.label || 'custom'
                );
                if (value !== 'custom') {
                  setTimeRange({ ...timeRange, date_from: value });
                }
              }}
              selectedChild={dateSelectedGeneral}
            />
            {dateSelectedGeneral?.toLowerCase() === 'custom' && (
              <DDateRangePicker
                fromDate={timeRange.date_from}
                toDate={timeRange.date_to}
                onApply={(from, to) => {
                  setTimeRange({
                    date_from: DateTime.fromISO(from).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
                    date_to: DateTime.fromISO(to).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS')
                  });
                }}
              />
            )}
          </div>
        </div>
        <div className="flex justify-between items-start md:items-center flex-col md:flex-row">
          <div className="flex items-center gap-size1">
            <p className="text-sm font-regular tracking-tight">Filters:</p>
          </div>
          <div className="flex flex-col md:flex-row items-center gap-size1 w-full md:w-2/5">
            <DSelect
              options={[
                { label: 'All States', value: 'all' },
                { label: 'Taken', value: 'taken' },
                { label: 'Waiting', value: 'waiting' },
                { label: 'Resolved', value: 'resolved' },
                { label: 'AI', value: 'ai' },
                { label: 'Closed', value: 'closed' },
                { label: 'Dropped', value: 'dropped' },
              ]}
              value={selectedState}
              buttonClassName="!min-h-8 !py-size0"
              onChange={(value) => setSelectedState(value)}
            />

            <DSelect
              options={[
                { label: 'All Categories', value: 'all' },
                ...categories.map((category) => ({
                  label: category.name,
                  value: category.id,
                })),
              ]}
              value={selectedCategory}
              listButtonClass="!h-8 !py-size0"
              onChange={(value) => setSelectedCategory(value)}
            />
          </div>
        </div>
        <div className="grid gap-size1 grid-cols-2 md:grid-cols-5">
          <FilterCard
            number={statistics.total_conversations}
            label="Total Conversations"
            tooltip="Total number of conversations"
            onClick={(checked) => {
              setChecked({ ...checked, totalConversations: checked });
            }}
            checked={checked.totalConversations}
          />
          <FilterCard
            number={statistics.waiting_requests}
            label="Waiting requests"
            tooltip="Total number of conversations waiting for a response"
            onClick={(checked) => {
              setChecked({ ...checked, waitingRequests: checked });
            }}
            checked={checked.waitingRequests}
          />
          <FilterCard
            number={statistics.live_conversations}
            label="Live Conversations"
            tooltip="Total number of conversations that have been taken by a live agent"
            onClick={(checked) => {
              setChecked({ ...checked, liveConversations: checked });
            }}
            checked={checked.liveConversations}
          />
          <FilterCard
            number={`${statistics.response_rate}%`}
            label="Response Rate"
            tooltip="Total number of conversations that have been resolved"
            showCheckbox={false}
          />
          <FilterCard
            number={formatDuration({ minutes: statistics.average_waiting_time })}
            label="Average waiting time"
            tooltip="Average waiting time for a response"
            showCheckbox={false}
          />
        </div>
      </div>
      <div className="flex-1 overflow-auto border border-grey-5 rounded-size1 min-h-[400px] md:min-h-auto">
        <DFilterTable columns={columns} data={rowsData} />
      </div>
      <div className="flex items-center gap-size1 mt-auto">
        <div className="flex items-center justify-center border border-orange-300 rounded-full size-7">
          <FlagIcon className="text-orange-300 size-4" />
        </div>
        <p className="text-xs font-regular tracking-tight">
          The user has requested to talk to a live agent.
        </p>
      </div>
      <div className="">{showChatBox && <ChatBox />}</div>
    </div>
  );
};

export default HumanHandoverDashboard;