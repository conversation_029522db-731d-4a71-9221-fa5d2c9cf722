import { useRouteError } from 'react-router-dom';

import DButton from '@/components/Global/DButton';

import './style.css';

const ErrorPage = () => {
  const error = useRouteError();

  return (
    <div id="error-page" className="h-screen w-screen flex justify-center items-center overflow-hidden relative">
      <div className="error-content z-10 max-w-3xl px-6 md:px-8 py-12 bg-white/10 backdrop-blur-sm rounded-2xl shadow-lg">
        <div className="flex flex-col gap-size3 items-center text-center">
          <span className="text-7xl font-bold mb-2">404</span>
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-semibold mb-4">
            Where am I?
          </h1>
          <p className="text-base md:text-lg mb-6 text-gray-200/90 max-w-lg">
            The page you are looking for isn't available or an error occurred. Please contact our support team at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-blue-300 hover:text-blue-200 underline transition-colors"
            >
              <EMAIL>
            </a>{' '}
            for assistance.
          </p>
          <DButton
            onClick={() => window.open('/', '_self')}
            variant="contained"
            color="primary"
            size="sm"
            style={{
              maxWidth: '300px',
              minWidth: '200px',
              margin: '12px auto',
              // borderRadius: '999px',
              fontWeight: 600,
              boxShadow: '0 4px 14px rgba(0, 0, 0, 0.2)',
              padding: '12px 24px'
            }}
          >
            Return to Home
          </DButton>
        </div>
      </div>
    </div>
  );
};

export default ErrorPage;
