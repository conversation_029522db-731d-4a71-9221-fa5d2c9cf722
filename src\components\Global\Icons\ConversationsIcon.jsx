import * as React from 'react';
const ConversationsIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.768 12.491a.15.15 0 0 0-.15-.15H3.88a1.715 1.715 0 0 1-1.715-1.715v-6.51c0-.948.768-1.716 1.715-1.716h9.766c.947 0 1.715.768 1.715 1.715v4.896c0 .*************.15h.789c.726 0 1.315.588 1.315 1.314v4.507c0 .727-.589 1.315-1.315 1.315h-.338a.15.15 0 0 0-.15.15v1.24a.413.413 0 0 1-.643.344L13.6 16.985a4.1 4.1 0 0 0-2.27-.688H9.54a1.315 1.315 0 0 1-1.315-1.315v-1.821a.15.15 0 0 0-.22-.133q-.45.233-.858.54L5.43 14.863a.413.413 0 0 1-.662-.33zm9.615-3.33a.15.15 0 0 0 .15-.15V4.115a.89.89 0 0 0-.888-.888H3.879a.89.89 0 0 0-.888.888v6.51c0 .491.398.89.888.89h1.042c.372 0 .674.3.674.673v1.214a.15.15 0 0 0 .24.12l.813-.613a7 7 0 0 1 1.484-.854.15.15 0 0 0 .092-.138v-1.442c0-.726.588-1.314 1.314-1.314zm.368 7.598a.15.15 0 0 0 .233-.125v-.57c0-.328.266-.594.594-.594h.72c.27 0 .489-.218.489-.488v-4.507a.49.49 0 0 0-.488-.488h-6.76a.49.49 0 0 0-.489.488v4.507c0 .27.219.488.488.488h1.79a4.9 4.9 0 0 1 2.73.827z"
      fill="currentColor"
    />
  </svg>
);
export default ConversationsIcon;
