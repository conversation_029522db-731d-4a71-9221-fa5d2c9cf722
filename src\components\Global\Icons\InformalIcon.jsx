const InformalIcon = (props) => {
  return (
    <svg
      width="19"
      height="19"
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.75 4L15 9.25M12.375 6.625L4.125 14.875M6.75 17.5L1.5 12.25M5.68969 18.2501H1.5C1.30109 18.2501 1.11032 18.1711 0.96967 18.0305C0.829018 17.8898 0.75 17.699 0.75 17.5001V13.3104C0.750092 13.1118 0.828988 12.9213 0.969375 12.7807L12.5306 1.2195C12.6713 1.07895 12.862 1 13.0608 1C13.2596 1 13.4503 1.07895 13.5909 1.2195L17.7806 5.40637C17.9212 5.54701 18.0001 5.7377 18.0001 5.93653C18.0001 6.13535 17.9212 6.32605 17.7806 6.46668L6.21937 18.0307C6.07883 18.1711 5.88834 18.25 5.68969 18.2501Z"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default InformalIcon;
