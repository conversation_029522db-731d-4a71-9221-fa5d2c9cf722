import LayoutWithButtons from './index';

// Default export configuration for Storybook
export default {
  title: 'Layout/LayoutWithButtons',
  component: LayoutWithButtons,
  parameters: {
    layout: 'fullscreen' // This sets the layout of the story to fullscreen
  },
  tags: ['autodocs'],
  argTypes: {
    // You can define controls for props here if needed
  },
  decorators: [
    (Story) => (
      <div className="h-screen flex flex-col">
        <Story />
      </div>
    )
  ]
};

// Story with default arguments
export const Default = {
  args: {
    children: (
      <div>
        <h1>Hello</h1>
      </div>
    ),
    buttons: true
  }
};
