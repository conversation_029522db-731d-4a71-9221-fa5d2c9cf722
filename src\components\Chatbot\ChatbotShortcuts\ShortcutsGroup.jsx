import DButtonIcon from '../../Global/DButtonIcon';

const ShortcutsGroup = ({ title, children, icon, actionClick }) => {
  return (
    <div className="flex flex-col gap-size2">
      {(title || icon) && (
        <header className="flex justify-between">
          {title && <h2 className="text-lg">{title}</h2>}
          {icon && (
             <DButtonIcon
              onClick={() => actionClick()}
              className="border border-grey-5 rounded-size1 p-size1"          
            >
              {icon}          
             </DButtonIcon>  
           
          )}
        </header>
      )}
      <div className="flex flex-col gap-size3">{children}</div>
    </div>
  );
};
export default ShortcutsGroup;
