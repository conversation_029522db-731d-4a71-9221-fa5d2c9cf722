import { useState, useEffect } from 'react';
import ReactConfetti from 'react-confetti';
import DModal from '@/components/Global/DModal';
import DButton from '@/components/Global/DButton';
import SuccessIcon from '@/components/Global/Icons/SuccessIcon';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import { useNavigate } from 'react-router-dom';
import DModalShareChatbot from '../DModalShareChatbot';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { LLM_MODEL_DEFAULT } from '@/constants';

const CompletionPopup = ({ isOpen, onClose, chatbotId }) => {
  const navigate = useNavigate();
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const [showShareModal, setShowShareModal] = useState(false);

  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.screen.width : 0,
    height: typeof window !== 'undefined' ? window.screen.height : 0,
  });
  const [copyState, setCopyState] = useState({
    type: null,
    show: false
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.screen.width,
        height: window.screen.height,
      });
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleStyleClick = () => {
    onClose();
    navigate(`/chatbot/${chatbotId}/styling`);
  };

  const handleShareClick = () => {
    setShowShareModal(true);
  };

  // Reset copy state after 3 seconds
  useEffect(() => {
    if (copyState.show) {
      const timer = setTimeout(() => {
        setCopyState({ type: null, show: false });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [copyState]);

  return (
    <>
      {isOpen && (
        <ReactConfetti
          width={windowSize.width}
          height={windowSize.height}
          numberOfPieces={200}
          recycle={false}
          colors={['#7C3AED', '#4F46E5', '#10B981', '#F59E0B']}
          gravity={0.2}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            zIndex: 9999,
            pointerEvents: 'none'
          }}
        />
      )}
      {/* <DModal
        isOpen={isOpen}
        onClose={onClose}
        className="!max-w-[500px]"
      >
        <div className="flex flex-col items-center gap-6 py-8">
          <div className="w-20 h-20 bg-green-50 rounded-full flex items-center justify-center animate-bounce">
            <SuccessIcon className="w-10 h-10 text-green-500" />
          </div>
          
          <div className="text-center">
            <h2 className="text-2xl font-medium mb-2">You created your chatbot! 🎉</h2>
            <p className="text-grey-75 mb-6">What would you like to do next?</p>
            
            <div className="flex flex-col gap-4 w-full">
              <div className="relative">
                <div 
                  className="p-4 border border-grey-10 rounded-lg hover:bg-grey-2 hover:border-grey-25 hover:shadow-sm transition-all cursor-pointer active:scale-[0.99]" 
                  onClick={handleStyleClick}
                >
                  <h3 className="font-medium mb-1">Style Your AI Chatbot</h3>
                  <p className="text-sm text-grey-75">Customize the appearance to match your brand</p>
                </div>
              </div>
              
              <div className="relative">
                <div 
                  className="p-4 border border-grey-10 rounded-lg hover:bg-grey-2 hover:border-grey-25 hover:shadow-sm transition-all cursor-pointer active:scale-[0.99]" 
                  onClick={handleShareClick}
                >
                  <h3 className="font-medium mb-1">Share Your AI Chatbot</h3>
                  <p className="text-sm text-grey-75">Get the embed code or share the link</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col w-full gap-2">
            <DButton
              variant="dark"
              fullWidth
              onClick={onClose}
            >
              Start Using My AI Chatbot
            </DButton>
          </div>
        </div>
      </DModal> */}
      <DModalShareChatbot
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        kb_id={chatbotId}
        llm_model={selectedChatbot?.last_model_used?.value || LLM_MODEL_DEFAULT.value}
      />
    </>
  );
};

export default CompletionPopup; 