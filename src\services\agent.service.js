import generateApiEndpoint from '@/helpers/generateApiEndpoint';
import http from './http';
import { DEFAULT_HEADERS } from './constants.service';

export const getAgentPermissions = () => {
  return http.get(
    generateApiEndpoint('agent/agents-permissions'),
    {
      headers: DEFAULT_HEADERS
    });
};

export const sendAgentInvitation = (params) => {
  return http.post(
    generateApiEndpoint('agent/send-invitation'),
    params,
    {
      headers: DEFAULT_HEADERS
    });
};

export const updateAgent = (params) => {
  return http.put(generateApiEndpoint(`agent/update-agent/${params.user_id}`), params, {
    headers: DEFAULT_HEADERS,
  });
};

export const removeAgent = (agent_email) => {
  return http.delete(generateApiEndpoint(`agent/remove-agent/${agent_email}`), {
    headers: DEFAULT_HEADERS,
  });
};
