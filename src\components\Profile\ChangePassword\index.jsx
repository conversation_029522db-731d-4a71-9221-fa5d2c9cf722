import { useState } from 'react';

import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import * as userService from '@/services/user.service';
import useToast from '@/hooks/useToast';

const ChangePassword = ({ onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { addSuccessToast } = useToast();

  const [form, setForm] = useState({
    old_password: '',
    new_password: '',
    confirm_new_password: '',
  });

  const [error, setError] = useState({
    old_password: null,
    new_password: null,
    confirm_new_password: null,
  });

  const validateForm = () => {
    if (form.new_password !== form.confirm_new_password) {
      setError({ ...error, confirm_new_password: 'Passwords do not match' });
      return false;
    } else {
      setError({ ...error, confirm_new_password: null });
    }

    if (form.old_password === '') {
      setError({ ...error, old_password: 'Current password is required' });
      return false;
    } else {
      setError({ ...error, old_password: null });
    }

    if (form.new_password === '') {
      setError({ ...error, new_password: 'New password is required' });
      return false;
    } else {
      setError({ ...error, new_password: null });
    }

    if (form.confirm_new_password === '') {
      setError({ ...error, confirm_new_password: 'Confirm new password is required' });
      return false;
    } else {
      setError({ ...error, confirm_new_password: null });
    }

    if(form.old_password === form.new_password) {
      setError({ ...error, old_password: 'New password cannot be the same as current password' });
      return false;
    } else {
      setError({ ...error, old_password: null });
    }
    return true;
  }

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await userService.resetPassword(form);
      if(response.status === 200) {
        addSuccessToast({ message: 'Password updated successfully' });
        onClose();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex flex-col gap-size5 p-size2 bg-grey-2 rounded-size0 w-full">
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Current password</p>
        <DInput
          type="password"
          placeholder="Enter your current password"
          value={form.old_password}
          onChange={(e) => setForm({ ...form, old_password: e.target.value })}
          error={error['old_password']}
        />
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">New password</p>
        <DInput
          type="password"
          placeholder="Enter your new password"
          value={form.new_password}
          onChange={(e) => setForm({ ...form, new_password: e.target.value })}
          error={error['new_password']}
        />
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Confirm new password</p>
        <DInput
          type="password"
          placeholder="Enter your new password"
          value={form.confirm_new_password}
          onChange={(e) => setForm({ ...form, confirm_new_password: e.target.value })}
          error={error['confirm_new_password']}
        />
      </div>
      <DButton variant="dark" size="sm" onClick={handleSubmit} loading={isLoading} fullWidth>Change password</DButton>
    </div>
  );
};

export default ChangePassword;
