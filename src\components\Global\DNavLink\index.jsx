import { Transition } from '@headlessui/react';
import clsx from 'clsx';

const DNavLink = ({
  disabled,
  variant,
  iconPlacement,
  label,
  icon,
  props,
  active,
  collapsed,
  onClick,
  className,
  page,
  children,
}) => {
  let buttonStyle = '';

  if ((variant === 'contained') && page !== 'ChatbotSidebar' && page !== 'LiveAgentTopBar') {
    buttonStyle = 'bg-purple-200 text-[#fff]';
  } else if (active && page !== 'ChatbotSidebar') {
    buttonStyle = 'bg-grey-5 text-black';
  }else if (page === 'ChatbotSidebar'&& active) {
    buttonStyle = 'bg-white text-black';
  } else if (page === 'LiveAgentTopBar' && active) {
    buttonStyle = 'bg-grey-2 text-black';
  }
  else if (variant === 'light') {
    buttonStyle = 'bg-white text-black';
  } else if (disabled) {
    buttonStyle = 'bg-transparent text-grey-20';
  } else {
    buttonStyle = 'bg-transparent';
  }

  return (
    <button
      className={`${buttonStyle} dbutton p-size2 flex items-center rounded-size0 h-11 w-full ${className} ${
        iconPlacement === 'post' || children ? 'justify-between' : 'gap-2'
      } hover:bg-grey-2 hover:text-black`}
      disabled={disabled}
      onClick={onClick}
      key={label}
      {...props}
    >
      <div className="flex gap-size0 w-full">
        <Transition show={collapsed || iconPlacement === 'pre'}>
          <span
            className={clsx(
              'transition-all duration-300 pl-0 w-[24px] flex items-center justify-center',
              {
                'justify-center': collapsed,
              }
            )}
          >
            {icon}
          </span>
        </Transition>
        <Transition show={!collapsed && label}>
          <span className={`transition-all duration-300 data-[closed]:opacity-0 data-[enter]:delay-300 text-base nav-label w-max whitespace-nowrap ${['Create New', 'AI Chatbots', 'AI Voice Agents', 'Human Handover'].includes(label) ? 'animated-nav-text' : ''}`}>
            {label}
          </span>
        </Transition>
        <Transition show={!collapsed && iconPlacement === 'post'}>
          <span>{icon}</span>
        </Transition>
      </div>
      {children}
    </button>
  );
};

export default DNavLink;
