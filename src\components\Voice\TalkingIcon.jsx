import React from 'react';

const TalkingIcon = ({ className = '', width = 18, height = 16 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 492.308 492.308"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g>
        <g>
          <path fill="currentColor" d="M398.534,141.25v79.298c0,45.077-18.644,88.106-51.144,118.048c-31.231,28.76-71.663,42.769-113.827,39.231
            c-78.385-6.433-139.789-73.144-139.789-151.865V141.25H74.082v84.712c0,88.894,69.346,164.221,157.865,171.5
            c1.461,0.12,2.908,0.096,4.365,0.179v74.975h-89.644v19.692h198.981v-19.692h-89.644v-75.019
            c38.858-2.221,75.401-17.505,104.731-44.519c36.538-33.654,57.49-81.962,57.49-132.529V141.25H398.534z"/>
        </g>
      </g>
      <g>
        <g>
          <path fill="currentColor" d="M246.159,0c-61.596,0-111.712,50.115-111.712,111.712v114.25c0,61.596,50.115,111.712,111.712,111.712
            S357.87,287.558,357.87,225.962v-114.25C357.87,50.115,307.755,0,246.159,0z M338.178,225.962
            c0,50.74-41.279,92.019-92.019,92.019c-50.74,0-92.019-41.279-92.019-92.019V183.75h45.615v-19.692h-45.615v-27.26h45.615v-19.692
            h-45.615v-5.394c0-50.74,41.279-92.019,92.019-92.019c50.74,0,92.019,41.279,92.019,92.019V225.962z"/>
        </g>
      </g>
    </svg>
  );
};

export default TalkingIcon;