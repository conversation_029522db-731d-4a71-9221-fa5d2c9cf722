import React from 'react';
import { DateTime } from 'luxon';

import DModalImage from '../Global/DModalImage';
import ModalWelcomeImage from '@/assets/modal/welcome.png';
import useLayoutStore from '@/stores/layout/layoutStore';

const DModalWelcome = () => {
  const today = DateTime.now();
  const lastDayOfTrial = today.plus({ days: 7 }).toFormat('MMMM dd');

  const { setWelcomeModal, welcomeModal } = useLayoutStore();

  const handleClose = () => {
    setWelcomeModal({ isOpen: false });
  };
  return (
    <DModalImage
      open={welcomeModal.isOpen}
      onClose={handleClose}
      hideCloseButton
      image={ModalWelcomeImage}
      text="Welcome to the new Dante AI Beta"
      description={<div className='flex flex-col gap-size2 items-left mt-size2'>
        <p>
        We've rebuilt our product from the ground up to serve you better. While you explore the new interface, our support team is standing by to help.
        </p>
        <p>
          <p><b>Feedback submission:</b> Click the Help button in the bottom right corner</p>
          <p><b>Email:</b> <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </p>
        <p>
        Your feedback helps shape the future of Dante AI – thank you for being part of this journey.
        </p>
      </div>}
      firstBtnText="Show me around"
      // secondBtnText="Great, let me explore"
      onFirstBtnClick={handleClose}
      // onSecondBtnClick={handleClose}
    />
  );
};

export default DModalWelcome;
