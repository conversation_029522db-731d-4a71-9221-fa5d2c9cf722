import DChatbotSidebar from '@/components/Chatbot/Details/ChatbotSidebar';
import DLoading from '@/components/DLoading';
import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';

const ChatbotDetailsOutlet = () => {
  return (
    <div className="flex flex-col md:flex-row gap-size3 h-full w-full">
      <DChatbotSidebar />
      <div className="w-full h-px grow overflow-y-auto md:h-full rounded-size1 flex flex-col gap-size5">
        <Suspense fallback={<DLoading show={true} />}>
          <Outlet />
        </Suspense>
      </div>
    </div>
  );
};

export default ChatbotDetailsOutlet;
