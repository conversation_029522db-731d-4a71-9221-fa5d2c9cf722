import DAlert from '../Global/DAlert';

const LayoutOnBoarding = ({ title, subtitle, alert, children }) => {
  return (
    <>
      <div
        className="flex flex-col gap-size5 items-center w-full"
        onSubmit={() => {}}
      >
        <div className="w-full">
          <h1 className="text-2xl md:text-3xl">{title}</h1>
          {subtitle && (
            <h2 className="text-2xl md:text-3xl text-grey-50">{subtitle}</h2>
          )}
          {alert && <DAlert className="!pl-0 -ml-[1px]">{alert}</DAlert>}
        </div>
        <div className="flex flex-col gap-size3 w-full">{children}</div>
      </div>
    </>
  );
};

export default LayoutOnBoarding;
