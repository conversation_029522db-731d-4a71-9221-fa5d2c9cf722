import React from 'react';

import CalendarIcon from '../Icons/CalendarIcon';
import LiveAgentIcon from '../Icons/LiveAgentIcon';

const DSuggestionPrompt = ({ content, type, disabled, ...props }) => {
  const isSpecialType = type === 'book_meet' || type === 'connect_live_agent';

  const textColorPrompt = isSpecialType
    ? 'var(--dt-color-brand-100)'
    : 'var(--dt-color-element-75)';
  const strokeColorPrompt = isSpecialType
    ? 'var(--dt-color-brand-100)'
    : 'var(--dt-color-element-10)';
  const backgroundColorPrompt = isSpecialType
    ? 'var(--dt-color-brand-5)'
    : 'var(--dt-color-surface-100)';

  return (
    <div className="flex text-sm justify-end transition-all duration-200 data-[enter]:delay-200 data-[closed]:opacity-0 h-full animate-fadeIn">
      <button
        className={`dbutton suggestion-prompt-button min-w-max text-sm text-left rounded-size1 border px-size1 py-size2 flex gap-size1 items-center ${
          isSpecialType ? 'font-medium' : ''
        } ${type ==='connect_live_agent' ? 'live-agent-button' : ''} ${type === 'book_meet' ? 'book-meet-button' : ''} ${
          disabled ? 'opacity-50' : ''
        }`}
        style={{
          color: textColorPrompt,
          borderColor: strokeColorPrompt,
          backgroundColor: backgroundColorPrompt,
        }}
        disabled={disabled}
        {...props}
      >
        {type === 'book_meet' && <CalendarIcon />}
        {type === 'connect_live_agent' && <LiveAgentIcon />}
        <span className={`${type === 'book_meet' ? 'book-meet-button-text' : ''} ${type === 'connect_live_agent' ? 'live-agent-button-text' : ''}`} style={{ color: textColorPrompt }}>{content}</span>
      </button>
    </div>
  );
};

export default DSuggestionPrompt;
