import { useEffect, useState } from 'react';

const DHorizontalBarChart = ({ data = [] }) => {
  const [maxValue, setMaxValue] = useState(0);

  useEffect(() => {
    const maxValue = Math.max(...data.map((item) => item.value));
    setMaxValue(maxValue);
  }, [data]);

  if (data.length === 0) return null;

  return (
    <div className="w-full h-full flex flex-col gap-size0">
      {data.map((item) => (
        <div
          key={item.label}
          className="grid grid-cols-12 gap-y-1 gap-x-4 w-full items-center md:h-12"
        >
          <div className="text-lg font-medium col-start-1 col-end-12 md:col-start-1 md:col-end-4">
            {item.label}
          </div>
          <div className="flex items-center bg-[#8275F71A] h-size0 col-start-1 col-end-11 md:col-start-4 md:col-end-11 rounded-md">
            <div
              className="w-full h-full bg-[#8275F780] rounded-md transition-all delay-300 duration-300"
              style={{ width: `${(item.value / maxValue) * 100}%` }}
            ></div>
          </div>
          <div className="text-base font-medium col-start-11 col-end-12 md:col-start-11 md:col-end-12 text-purple-200">
            {item.value}
          </div>
        </div>
      ))}
    </div>
  );
};

export default DHorizontalBarChart;
