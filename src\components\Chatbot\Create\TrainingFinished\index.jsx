// src/components/TrainingFinished.js
import React, { useCallback, useEffect, useState } from 'react';

import DAlert from '@/components/Global/DAlert';
import ResetIcon from '@/components/Global/Icons/ResetIcon';
import { STATUS, TASK_TYPES } from '@/constants';
import useTaskHand<PERSON> from '@/hooks/useTaskHandler';
import * as suggestionService from '@/services/suggestion.service';
import useTaskStore from '@/stores/task/taskStore';

const TrainingFinished = ({ config, isInApp, setTempCustomizationData, payload }) => {
  // Component state
  const [suggestions, setSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const defaultPayload = [
    {
      role: 'assistant',
      content: 'Hello How can I help you?',
      type: 'welcome_message'
    }
  ];

  /**
   * Handles the regeneration of suggestions by initiating a new 'Suggestion' task.
   */
  const regeneratePrompts = useCallback(async () => {
    try {
      const response = await suggestionService.postSuggestions({
        kb_id: config?.kb_id,
        // conversation_id: currentConversation?.id,
      })

      if(response.status === 200) {
        handlePromptSuggestions(response.data.result);
      }
      
    }catch(error) {
      console.error('Error regenerating prompts:', error);
      setIsLoading(false);
    }
     
  }, [config?.kb_id, isInApp]);

  /**
   * Handles the formatting and setting of prompt suggestions.
   *
   * @param {Array} suggestionsData - The array of suggestion strings from the task result.
   */
  const handlePromptSuggestions = useCallback((suggestionsData) => {
    setSuggestions(suggestionsData);
    setTempCustomizationData((prev) => ({
      ...prev,
      prompt_suggestions: suggestionsData,
      suggested_prompts_enabled: true
    }));
    setIsLoading(false);
  }, []);

  /**
   * Calls regeneratePrompts when the component mounts.
   */
  useEffect(() => {
    regeneratePrompts();
  }, []);

  return (
    <div className="h-full w-full flex flex-col gap-size5">
      <DAlert state="positive">
        <p className="text-sm">
          <span className="font-medium">{config?.name} training is now complete!</span> Test your new AI Chatbot on the right to ensure it's properly trained. If your AI Chatbot does not respond as expected,
          check the correct information is in the knowledge files or URLs you have trained it on. Adding rules to the personality 
          input will also help guide the direction of your AI Chatbot's narrative and style of responses. One testing is finished, 
          customize the look and feel of your AI Chatbot below. 
        </p>
      </DAlert>

      <div className="flex flex-col gap-size3">
        <div className="flex flex-col gap-size1">
          <p className="text-base tracking-tight">Test your AI Chatbot</p>
          <p className="text-grey-50 text-xs tracking-tight max-w-full md:max-w-sm">
          Test your AI Chatbot using the suggested prompts below or type in your custom questions directly into the AI Chatbot on the right.
          </p>
        </div>
        <div className="flex flex-col gap-size2">
          <button
            onClick={regeneratePrompts}
            loading={isLoading}
            className={`dbutton border border-grey-5 p-size1 flex gap-size0 items-center rounded-size0 w-fit ${
              isLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <ResetIcon />
            <span className="text-sm tracking-tight font-light">Regenerate prompts</span>
          </button>
          {suggestions.length > 0 && (
            <ul className="list-disc list-inside mt-size2 space-y-size1">
              {suggestions.map((prompt, index) => (
                <button
                  key={index}
                  className="dbutton border bg-grey-5 p-size1 flex items-center rounded-size0 w-fit"
                >
                  <span className="text-sm tracking-tight font-light">{prompt.content}</span>
                </button>
              ))}
            </ul>
          )}
          <p className="text-grey-50 text-xs tracking-tight font-light">
            *Suggested prompts are generated from your knowledge data
          </p>
        </div>
      </div>
    </div>
  );
};

export default TrainingFinished;