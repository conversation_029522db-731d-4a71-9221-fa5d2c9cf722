import { WS_STATES, WS_CONFIG, WS_EVENTS, WS_CLOSE_CODES } from './constants';

/**
 * WebSocketService - A reusable service for managing WebSocket connections
 * 
 * Features:
 * - Configurable connection and reconnection handling
 * - Event handling with callbacks
 * - Automatic reconnection with backoff
 * - Connection timeout detection
 * - Proper cleanup
 */
class WebSocketService {
  /**
   * Create a new WebSocketService instance
   * @param {Object} config - Configuration options
   * @param {string} config.url - WebSocket URL to connect to
   * @param {Object} config.options - Additional options
   * @param {number} [config.options.maxRetries=5] - Maximum number of reconnection attempts
   * @param {number} [config.options.retryDelayMs=1000] - Base delay between reconnection attempts in ms
   * @param {number} [config.options.connectionTimeoutMs=5000] - Connection timeout in ms
   * @param {boolean} [config.options.autoReconnect=true] - Whether to automatically reconnect
   * @param {string} [config.options.binaryType='blob'] - Binary type for the WebSocket ('blob' or 'arraybuffer')
   * @param {Object} callbacks - Callback functions for WebSocket events
   * @param {Function} [callbacks.onOpen] - Called when the connection is established
   * @param {Function} [callbacks.onMessage] - Called when a message is received
   * @param {Function} [callbacks.onClose] - Called when the connection is closed
   * @param {Function} [callbacks.onError] - Called when an error occurs
   */
  constructor(config, callbacks = {}) {
    // WebSocket URL
    this.url = config.url;
    
    // Configuration options with defaults
    this.options = {
      maxRetries: config.options?.maxRetries ?? WS_CONFIG.MAX_RETRIES,
      retryDelayMs: config.options?.retryDelayMs ?? WS_CONFIG.RETRY_DELAY_MS,
      connectionTimeoutMs: config.options?.connectionTimeoutMs ?? WS_CONFIG.CONNECTION_TIMEOUT_MS,
      autoReconnect: config.options?.autoReconnect ?? true,
      binaryType: config.options?.binaryType ?? 'blob'
    };
    
    // Event callbacks
    this.callbacks = {
      onOpen: callbacks.onOpen || (() => {}),
      onMessage: callbacks.onMessage || (() => {}),
      onClose: callbacks.onClose || (() => {}),
      onError: callbacks.onError || (() => {})
    };
    
    // WebSocket instance
    this.socket = null;
    
    // Connection state
    this.isConnecting = false;
    this.isConnected = false;
    this.retryCount = 0;
    this.shouldReconnect = this.options.autoReconnect;
    
    // Timers
    this.connectionTimeoutId = null;
    this.reconnectTimeoutId = null;
    
    // Bind event handlers to maintain 'this' context
    this.handleOpen = this.handleOpen.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);
  }
  
  /**
   * Connect to the WebSocket server
   * @returns {Promise<WebSocket>} A promise that resolves when the connection is established
   */
  connect() {
    // Don't attempt to connect if already connecting or connected
    if (this.isConnecting || (this.socket && this.socket.readyState === WS_STATES.OPEN)) {
      return Promise.resolve(this.socket);
    }
    
    this.isConnecting = true;
    this.shouldReconnect = this.options.autoReconnect;
    
    return new Promise((resolve, reject) => {
      try {
        // Clean up any existing socket
        this.cleanup();
        
        // Create a new WebSocket
        this.socket = new WebSocket(this.url);
        this.socket.binaryType = this.options.binaryType;
        
        // Set up connection timeout
        this.connectionTimeoutId = setTimeout(() => {
          if (this.socket && this.socket.readyState !== WS_STATES.OPEN) {
            const timeoutError = new Error('WebSocket connection timeout');
            this.handleError({ error: timeoutError });
            
            // Force close the socket
            if (this.socket) {
              this.socket.close();
            }
            
            reject(timeoutError);
          }
        }, this.options.connectionTimeoutMs);
        
        // Set up event listeners
        this.socket.addEventListener(WS_EVENTS.OPEN, (event) => {
          clearTimeout(this.connectionTimeoutId);
          this.handleOpen(event);
          resolve(this.socket);
        });
        
        this.socket.addEventListener(WS_EVENTS.MESSAGE, this.handleMessage);
        this.socket.addEventListener(WS_EVENTS.CLOSE, (event) => {
          clearTimeout(this.connectionTimeoutId);
          this.handleClose(event);
          
          if (!this.isConnected) {
            reject(new Error('WebSocket connection closed before it was established'));
          }
        });
        
        this.socket.addEventListener(WS_EVENTS.ERROR, (event) => {
          clearTimeout(this.connectionTimeoutId);
          this.handleError(event);
          
          if (!this.isConnected) {
            reject(event.error || new Error('WebSocket connection error'));
          }
        });
      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }
  
  /**
   * Disconnect from the WebSocket server
   * @param {number} [code=1000] - Close code
   * @param {string} [reason=''] - Close reason
   */
  disconnect(code = WS_CLOSE_CODES.NORMAL_CLOSURE, reason = '') {
    this.shouldReconnect = false;
    
    if (this.socket) {
      try {
        this.socket.close(code, reason);
      } catch (error) {
        console.error('Error closing WebSocket:', error);
      }
    }
    
    this.cleanup();
  }
  
  /**
   * Send data through the WebSocket
   * @param {string|ArrayBuffer|Blob} data - Data to send
   * @returns {boolean} Whether the data was sent successfully
   */
  send(data) {
    if (!this.socket || this.socket.readyState !== WS_STATES.OPEN) {
      console.error('Cannot send data: WebSocket is not open');
      return false;
    }
    
    try {
      this.socket.send(data);
      return true;
    } catch (error) {
      console.error('Error sending data:', error);
      return false;
    }
  }
  
  /**
   * Update the event callbacks
   * @param {Object} callbacks - New callback functions
   */
  updateCallbacks(callbacks) {
    this.callbacks = {
      ...this.callbacks,
      ...callbacks
    };
  }
  
  /**
   * Handle WebSocket open event
   * @private
   * @param {Event} event - WebSocket open event
   */
  handleOpen(event) {
    this.isConnecting = false;
    this.isConnected = true;
    this.retryCount = 0;
    
    // Call the onOpen callback
    this.callbacks.onOpen(event);
  }
  
  /**
   * Handle WebSocket message event
   * @private
   * @param {MessageEvent} event - WebSocket message event
   */
  handleMessage(event) {
    // Call the onMessage callback
    this.callbacks.onMessage(event);
  }
  
  /**
   * Handle WebSocket close event
   * @private
   * @param {CloseEvent} event - WebSocket close event
   */
  handleClose(event) {
    this.isConnecting = false;
    this.isConnected = false;
    
    // Determine if we should reconnect
    const isNormalClosure = event.code === WS_CLOSE_CODES.NORMAL_CLOSURE;
    const isAbnormalClosure = event.code === WS_CLOSE_CODES.ABNORMAL_CLOSURE;
    const isMaxRetriesReached = this.retryCount >= this.options.maxRetries;
    const isConversationNotFound = event.reason && event.reason.includes('Conversation not found');
    const isInsufficientResources = event.reason && event.reason.includes('Insufficient resources');
    
    // Don't reconnect if:
    // 1. shouldReconnect is false (manual disconnect)
    // 2. Normal closure
    // 3. Max retries reached
    // 4. Specific error conditions
    const shouldAttemptReconnect = this.shouldReconnect && 
                                  !isNormalClosure && 
                                  !isMaxRetriesReached &&
                                  !isConversationNotFound &&
                                  !isInsufficientResources;
    
    // Call the onClose callback with detailed information
    this.callbacks.onClose({
      ...event,
      retryCount: this.retryCount,
      maxRetries: this.options.maxRetries,
      willReconnect: shouldAttemptReconnect
    });
    
    // Attempt to reconnect if needed
    if (shouldAttemptReconnect) {
      // For abnormal closures (code 1006), limit retries more strictly
      if (isAbnormalClosure && this.retryCount >= 2) {
        return;
      }
      
      // Calculate backoff delay with exponential increase
      const delay = this.options.retryDelayMs * Math.pow(1.5, this.retryCount);
      
      
      this.reconnectTimeoutId = setTimeout(() => {
        this.retryCount++;
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, delay);
    }
  }
  
  /**
   * Handle WebSocket error event
   * @private
   * @param {Event} event - WebSocket error event
   */
  handleError(event) {
    // Call the onError callback
    this.callbacks.onError(event);
  }
  
  /**
   * Clean up resources
   * @private
   */
  cleanup() {
    // Clear timers
    if (this.connectionTimeoutId) {
      clearTimeout(this.connectionTimeoutId);
      this.connectionTimeoutId = null;
    }
    
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }
    
    // Remove event listeners and close the socket
    if (this.socket) {
      try {
        this.socket.removeEventListener(WS_EVENTS.OPEN, this.handleOpen);
        this.socket.removeEventListener(WS_EVENTS.MESSAGE, this.handleMessage);
        this.socket.removeEventListener(WS_EVENTS.CLOSE, this.handleClose);
        this.socket.removeEventListener(WS_EVENTS.ERROR, this.handleError);
        
        // Only close if not already closed
        if (this.socket.readyState !== WS_STATES.CLOSED && this.socket.readyState !== WS_STATES.CLOSING) {
          this.socket.close();
        }
      } catch (error) {
        console.error('Error during WebSocket cleanup:', error);
      }
      
      this.socket = null;
    }
    
    this.isConnecting = false;
    this.isConnected = false;
  }
  
  /**
   * Get the current state of the WebSocket connection
   * @returns {Object} Current state information
   */
  getState() {
    return {
      url: this.url,
      isConnecting: this.isConnecting,
      isConnected: this.isConnected,
      readyState: this.socket ? this.socket.readyState : WS_STATES.CLOSED,
      retryCount: this.retryCount,
      maxRetries: this.options.maxRetries
    };
  }
}

export default WebSocketService;
