import { useEffect, useState } from 'react';

import CustomizeReview from '@/components/Chatbot/Create/CustomizeReview';
import { StepEnum } from '@/components/Chatbot/Create/StepEnum';
import PowerUps from '@/components/Chatbot/PowerUps';
import Setup from '@/components/Chatbot/Setup';
import Styling from '@/components/Chatbot/Styling';
import Tabs from '@/components/Chatbot/Tabs';
import useCustomizationData from '@/hooks/useCustomization';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { useNavigate } from 'react-router-dom';
import * as tabService from '@/services/tabs.service';

const Customization = ({
  tempCustomizationData,
  setTempCustomizationData,
  customizationSaved,
  setCustomizationSaved,
  chatbotId,
  personalities,
  slots,
  setSlots,
  enableHomeTab,
  setEnableHomeTab,
  enableAvatarTab,
  setEnableAvatarTab,
}) => {
  const navigate = useNavigate();
  const currentStep = useChatbotStore((state) => state.currentStep);
  const [selectedPersonality, setSelectedPersonality] = useState(null);

  const { customizationData, patchCustomizationData } = useCustomizationData(
    true,
    chatbotId
  );

  const handleSaveCustomization = async () => {
    try {
      const quick_links = slots.filter((slot) => slot.type === 'quick_link');
      const meta_links = slots.filter((slot) => slot.type === 'meta_link');
      const link_groups = slots.filter((slot) => slot.type === 'link_group');
      const videos = slots.filter((slot) => slot.type === 'video');

      const payload = {
        home_tab_enabled: enableHomeTab,
        avatar_enabled: enableAvatarTab,
        slots: {
          quick_links: [...quick_links],
          meta_links: [...meta_links],
          link_groups: [...link_groups],
          videos: [...videos],
        },
      };

      const tabPageResponse = await tabService.saveTabs(chatbotId, payload);

      const response = await patchCustomizationData(tempCustomizationData);
      if (response) {
        navigate(`/chatbot/${chatbotId}`);
      }
    } catch (error) {
      console.log('Error saving customization data:', error);
    }
  };

  useEffect(() => {
    if (customizationSaved) {
      handleSaveCustomization();
      setCustomizationSaved(false);
    }
  }, [customizationSaved]);

  useEffect(() => {
    setTempCustomizationData(customizationData);
  }, [customizationData]);

  return (
    <div className="h-full w-full">
      {currentStep === StepEnum.SETUP && (
        <Setup
          customizationData={tempCustomizationData}
          updateCustomizationData={(key, value) =>
            setTempCustomizationData((prev) => ({ ...prev, [key]: value }))
          }
        />
      )}
      {currentStep === StepEnum.STYLING && (
        <Styling
          customizationData={tempCustomizationData}
          updateCustomizationData={(key, value) =>
            setTempCustomizationData((prev) => ({ ...prev, [key]: value }))
          }
          updateCustomizationDataBatch={(data) => {
            setTempCustomizationData((prev) => ({ ...prev, ...data }));
          }}
          canEditTemplate={true}
          personalities={personalities}
          selectedPersonality={selectedPersonality}
          setSelectedPersonality={setSelectedPersonality}
        />
      )}
      {currentStep === StepEnum.POWER_UPS && (
        <PowerUps
          customizationData={tempCustomizationData}
          updateCustomizationData={(key, value) =>
            setTempCustomizationData((prev) => ({ ...prev, [key]: value }))
          }
        />
      )}
      {currentStep === StepEnum.TABS && (
        <Tabs
          customizationData={tempCustomizationData}
          updateCustomizationData={(key, value) =>
            setTempCustomizationData((prev) => ({ ...prev, [key]: value }))
          }
          slots={slots}
          setSlots={setSlots}
          chatbotId={chatbotId}
          enableHomeTab={enableHomeTab}
          enableAvatarTab={enableAvatarTab}
          setEnableAvatarTab={setEnableAvatarTab}
          setEnableHomeTab={setEnableHomeTab}
        />
      )}
      {currentStep === StepEnum.REVIEW_CUSTOMIZE && (
        <CustomizeReview
          customizationData={tempCustomizationData}
          updateCustomizationData={(key, value) =>
            setTempCustomizationData((prev) => ({ ...prev, [key]: value }))
          }
        />
      )}
    </div>
  );
};

export default Customization;
