const ConciseIcon = (props) => {
  return (
    <svg
      width="22"
      height="20"
      viewBox="0 0 22 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8 15.5H2.5M5.5 10H1M8 4.5H3M15.9999 1L9.40386 10.235C9.11186 10.644 8.96586 10.848 8.97186 11.019C8.9746 11.0922 8.99339 11.1639 9.02692 11.2291C9.06044 11.2942 9.10787 11.3512 9.16586 11.396C9.30086 11.5 9.55186 11.5 10.0549 11.5H14.9999L13.9999 19L20.5959 9.765C20.8879 9.356 21.0339 9.152 21.0279 8.981C21.0251 8.90778 21.0063 8.83607 20.9728 8.77092C20.9393 8.70577 20.8918 8.64879 20.8339 8.604C20.6989 8.5 20.4479 8.5 19.9449 8.5H14.9999L15.9999 1Z"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ConciseIcon;
