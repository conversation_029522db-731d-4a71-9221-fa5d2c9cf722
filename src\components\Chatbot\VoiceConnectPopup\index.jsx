import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Transition } from '@headlessui/react';
import DButton from '@/components/Global/DButton';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import AiVoiceIcon from '@/components/Global/Icons/AiVoiceIcon';

/**
 * A popup that appears in the bottom-right corner after a user creates a new chatbot,
 * asking if they want to connect it with an AI voice agent.
 * 
 * @param {Object} props
 * @param {boolean} props.show - Whether to show the popup
 * @param {Function} props.onClose - Function to call when the popup is closed
 * @param {string} props.chatbotId - The ID of the newly created chatbot
 * @param {string} props.chatbotName - The name of the newly created chatbot
 */
const VoiceConnectPopup = ({ show, onClose, chatbotId, chatbotName }) => {
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Add a small delay before showing the popup for a smoother experience
    if (show) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [show]);

  const handleConnectNow = () => {
    // Navigate to the CreateVoice form with the chatbot pre-selected
    navigate('/voice/create', { 
      state: { 
        preSelectedChatbot: {
          value: chatbotId,
          label: chatbotName
        } 
      } 
    });
    onClose();
  };

  const handleMaybeLater = () => {
    onClose();
  };

  return (
    <Transition
      show={isVisible}
      enter="transition-opacity duration-300"
      enterFrom="opacity-0"
      enterTo="opacity-100"
      leave="transition-opacity duration-200"
      leaveFrom="opacity-100"
      leaveTo="opacity-0"
    >
      <div className="fixed bottom-5 right-5 z-50 w-80 bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="relative p-4">
          {/* Close button */}
          <button 
            onClick={handleMaybeLater}
            className="absolute top-2 right-2 text-grey-50 hover:text-grey-75"
          >
            <CloseIcon className="w-4 h-4" />
          </button>
          
          {/* Content */}
          <div className="flex items-start mb-3">
            <div className="bg-purple-50 p-2 rounded-full mr-3">
              <AiVoiceIcon className="w-5 h-5 text-purple-500" />
            </div>
            <div>
              <h3 className="font-medium text-base">Connect with AI Voice</h3>
              <p className="text-sm text-grey-75 mt-1">
                Now that you've created your chatbot, would you like to connect it with an AI voice agent?
              </p>
            </div>
          </div>
          
          {/* Buttons */}
          <div className="flex gap-2 mt-4">
            <DButton
              variant="outlined"
              size="sm"
              fullWidth
              onClick={handleMaybeLater}
            >
              Maybe later
            </DButton>
            <DButton
              variant="dark"
              size="sm"
              fullWidth
              onClick={handleConnectNow}
            >
              Yes, connect now
            </DButton>
          </div>
        </div>
      </div>
    </Transition>
  );
};

export default VoiceConnectPopup;
