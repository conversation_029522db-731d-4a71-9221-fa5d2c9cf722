import { useEffect, useState } from 'react';
import { Doughnut } from 'react-chartjs-2';

import 'chart.js/auto';

const defaultDataChart = {
  labels: [],
  datasets: [],
};

const DDoughnutChart = ({ options = {}, data = [] }) => {
  const [correctData, setCorrectData] = useState(defaultDataChart);

  useEffect(() => {
    const labels = data.map((item) => item.label);
    const datasets = [
      { label: '', data: [], backgroundColor: [], hoverOffset: 4 },
    ];

    data.forEach((item) => {
      datasets[0].data.push(item.value);
      datasets[0].backgroundColor.push(item.color);
    });

    setCorrectData({
      labels,
      datasets,
    });
  }, [data]);

  return (
    <div className="w-full h-full">
      <Doughnut
        options={{
          responsive: true,
          cutout: '80%',
          elements: {
            center: {
              text: 'Red is 2/3 the total numbers',
              color: '#FF6384', // Default is #000000
              fontStyle: 'Arial', // Default is Arial
              sidePadding: 20, // Default is 20 (as a percentage)
              minFontSize: 20, // Default is 20 (in px), set to false and text will not wrap.
              lineHeight: 25, // Default is 25 (in px), used for when text wraps
            },
          },
          plugins: {
            text: {
              display: true,
            },
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  family: '\'DM Sans\', sans-serif',
                  size: 16,
                },
              },
            },
            title: {
              display: false,
            },
          },
        }}
        data={correctData}
      />
    </div>
  );
};

export default DDoughnutChart;
