@keyframes glow {
  0% {
    box-shadow: 0 0 10px rgba(128, 112, 242, 0.2),
      0 0 20px rgba(147, 112, 219, 0.15), 0 0 30px rgba(138, 43, 226, 0.1);
  }
  25% {
    box-shadow: 0 0 15px rgba(106, 90, 205, 0.25),
      0 0 30px rgba(123, 104, 238, 0.2), 0 0 45px rgba(147, 112, 219, 0.15);
  }
  50% {
    box-shadow: 0 0 15px rgba(72, 61, 139, 0.25),
      0 0 30px rgba(65, 105, 225, 0.2), 0 0 45px rgba(0, 0, 255, 0.15);
  }
  75% {
    box-shadow: 0 0 15px rgba(138, 43, 226, 0.25),
      0 0 30px rgba(148, 0, 211, 0.2), 0 0 45px rgba(153, 50, 204, 0.15);
  }
  100% {
    box-shadow: 0 0 10px rgba(128, 112, 242, 0.2),
      0 0 20px rgba(147, 112, 219, 0.15), 0 0 30px rgba(138, 43, 226, 0.1);
  }
}

.glow-effect-name {
  animation: glow 5s ease-in-out infinite;
}

.typewriter-input::-webkit-input-placeholder {
  color: transparent;
}

.typewriter-container {
  position: relative;
}

.typewriter-placeholder {
  position: absolute;
  left: 16px; /* Matches the input padding */
  top: 50%;
  transform: translateY(-50%);
  color: rgba(var(--color-element), 0.2);
  pointer-events: none;
  white-space: nowrap;
  overflow: hidden;
  display: inline-block;
}

.typewriter-placeholder.cursor-blink {
  border-right: 2px solid rgba(var(--color-element), 0.3);
}

@keyframes blink {
  0%,
  100% {
    border-color: transparent;
  }
  50% {
    border-color: rgba(var(--color-element), 0.3);
  }
}

.cursor-blink {
  animation: blink 0.75s steps(2) infinite;
}
