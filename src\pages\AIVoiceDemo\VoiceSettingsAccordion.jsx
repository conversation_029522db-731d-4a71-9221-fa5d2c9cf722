import React, { useState, useRef, useEffect } from 'react';
import { defaultSettings } from './config';

const VoiceSettingsAccordion = ({ settings, setSettings, isStartButtonDisabled, isStopButtonDisabled }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentlyPlayingId, setCurrentlyPlayingId] = useState(null);
  const [isKbDropdownOpen, setIsKbDropdownOpen] = useState(false);
  const [voiceInstructions, setVoiceInstructions] = useState(settings.voice_instructions || '');
  const [welcomeMessage, setWelcomeMessage] = useState(settings.initialMessage || defaultSettings.initialMessage || '');
  const audioRef = useRef(null);
  const kbDropdownRef = useRef(null);
  const panelRef = useRef(null);
  const welcomeMessageRef = useRef(null);

  // Update local welcome message state when settings change
  useEffect(() => {
    // Only update if different to prevent unnecessary rerenders
    if (settings.initialMessage && settings.initialMessage !== welcomeMessage) {
      setWelcomeMessage(settings.initialMessage);
    }

    if (settings.voice_instructions !== undefined && settings.voice_instructions !== voiceInstructions) {
      setVoiceInstructions(settings.voice_instructions);
    }
  }, [settings.initialMessage, settings.voice_instructions]);

  // Determine if audio is active (to disable settings changes)
  const isAudioActive = !isStartButtonDisabled && !isStopButtonDisabled;

  // Check if selected voice is an OpenAI voice
  const isOpenAIVoice = settings.selectedVoice?.startsWith('open-ai-');

  // Use the dynamic voice options and kb options from settings
  const voiceOptions = settings.voiceOptions || { openai: [], cartesia: [], elevenlabs: [] };
  const kbOptions = settings.kbOptions || [];

  // Handle accordion animation
  const toggleAccordion = () => {
    setIsAnimating(true);
    setIsExpanded(!isExpanded);
  };

  // Handle animation end
  useEffect(() => {
    const handleAnimationEnd = () => {
      setIsAnimating(false);
    };

    const panel = panelRef.current;
    if (panel) {
      panel.addEventListener('transitionend', handleAnimationEnd);
      return () => {
        panel.removeEventListener('transitionend', handleAnimationEnd);
      };
    }
  }, [panelRef]);

  // Close KB dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (kbDropdownRef.current && !kbDropdownRef.current.contains(event.target)) {
        setIsKbDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleVoiceChange = (voiceId) => {
    // Only prevent changes if audio is active
    if (isAudioActive) return;

    setSettings(prev => {
      const newSettings = {
        ...prev,
        selectedVoice: voiceId,
        voice_id: voiceId  // Add both for compatibility
      };
      return newSettings;
    });
  };

  const handleKnowledgeBaseChange = (event) => {
    // Only prevent changes if audio is active
    if (isAudioActive) return;

    const kbId = event.target.value;

    // Find the selected KB item to get its initialMessage
    const selectedKb = kbOptions.find(kb => kb.id === kbId);

    // Update both kbId and initialMessage in settings
    setSettings(prev => ({
      ...prev,
      kbId: kbId,
      initialMessage: selectedKb?.initialMessage || prev.initialMessage || defaultSettings.initialMessage || ''
    }));

    // Also update the local state for welcome message
    if (selectedKb?.initialMessage) {
      setWelcomeMessage(selectedKb.initialMessage);
    } else {
      setWelcomeMessage(settings.initialMessage || defaultSettings.initialMessage || '');
    }
  };

  const handleWelcomeMessageChange = (e) => {
    if (isAudioActive) return;

    const value = e.target.value;
    setWelcomeMessage(value);
    setSettings(prev => ({
      ...prev,
      initialMessage: value
    }));

    // Auto-resize logic will be handled by the useEffect below
  };

  // Auto-resize welcome message textarea
  useEffect(() => {
    const textarea = welcomeMessageRef.current;
    if (!textarea) return;

    // Reset height to auto to get correct scrollHeight
    textarea.style.height = 'auto';

    // Calculate dimensions based on the current font and styling
    const computedStyle = window.getComputedStyle(textarea);
    const lineHeightValue = parseFloat(computedStyle.lineHeight);
    const paddingTop = parseFloat(computedStyle.paddingTop);
    const paddingBottom = parseFloat(computedStyle.paddingBottom);
    const borderTop = parseFloat(computedStyle.borderTopWidth);
    const borderBottom = parseFloat(computedStyle.borderBottomWidth);

    // Total padding and border height
    const paddingAndBorderHeight = paddingTop + paddingBottom + borderTop + borderBottom;

    // Get the content height
    const contentHeight = textarea.scrollHeight - paddingAndBorderHeight;

    // Determine if we need 1 or 2 rows
    if (contentHeight > lineHeightValue * 1.2) { // If content is more than ~1.2 lines
      textarea.rows = 2;
      textarea.style.height = `${(lineHeightValue * 2) + paddingAndBorderHeight}px`;
    } else {
      textarea.rows = 1;
      textarea.style.height = `${lineHeightValue + paddingAndBorderHeight}px`;
    }
  }, [welcomeMessage]);

  const togglePreview = async (voiceId, e) => {
    e.preventDefault();
    e.stopPropagation();

    // If we have an audio playing, stop it
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;

      // If we're clicking the same voice that's playing, just stop it
      if (currentlyPlayingId === voiceId) {
        setCurrentlyPlayingId(null);
        return;
      }
    }

    // Find the voice data from all categories
    const voice = [
      ...(voiceOptions.openai || []),
      ...(voiceOptions.cartesia || []),
      ...(voiceOptions.elevenlabs || [])
    ].find(v => v.id === voiceId);

    if (voice?.preview_url) {
      try {
        // Create new audio instance if needed
        if (!audioRef.current) {
          audioRef.current = new Audio();
        }

        audioRef.current.src = voice.preview_url;
        audioRef.current.onended = () => setCurrentlyPlayingId(null);
        await audioRef.current.play();
        setCurrentlyPlayingId(voiceId);
      } catch (error) {
        console.error('Error playing audio preview:', error);
        setCurrentlyPlayingId(null);
      }
    }
  };

  const renderVoiceSection = (title, voices) => {
    // If voices array is empty or undefined, show placeholder
    if (!voices || voices.length === 0) {
      return (
        <div className="pb-0.5">
          <h3 className="text-md font-semibold mb-1 text-gray-700">{title}</h3>
          <div className="p-2 text-sm text-gray-500 bg-gray-50 rounded-lg">
            Loading voice options...
          </div>
        </div>
      );
    }

    return (
      <div className="pb-0.5">
        <h3 className="text-md font-semibold mb-1 text-gray-700">{title}</h3>
        <div className="grid grid-cols-2 gap-1.5">
          {voices.map(voice => (
            <div
              key={voice.id}
              onClick={() => handleVoiceChange(voice.id)}
              className={`p-1.5 rounded-lg transition-all cursor-pointer ${
                settings.selectedVoice === voice.id
                  ? 'bg-gray-100 text-gray-900'
                  : 'hover:bg-gray-50'
              }`}
            >
              <div className="flex justify-between items-center">
                <span className="font-medium text-sm">{voice.name}</span>
                {voice.preview_url && (
                  <div
                    role="button"
                    tabIndex={0}
                    onClick={(e) => togglePreview(voice.id, e)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        togglePreview(voice.id, e);
                      }
                    }}
                    className={`p-1 rounded-full transition-colors ${
                      currentlyPlayingId === voice.id
                        ? 'text-red-500 hover:text-red-600 hover:bg-red-50'
                        : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    {currentlyPlayingId === voice.id ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                )}
              </div>
              <div className="text-xs text-gray-500 text-left">{voice.description}</div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Get the current voice name for display
  const getCurrentVoiceName = () => {
    // Check if we have voice options loaded
    if (!voiceOptions.openai?.length && !voiceOptions.cartesia?.length && !voiceOptions.elevenlabs?.length) {
      return 'Loading voices...';
    }

    // Combine all voice options
    const allVoices = [
      ...(voiceOptions.openai || []),
      ...(voiceOptions.cartesia || []),
      ...(voiceOptions.elevenlabs || [])
    ];

    const voice = allVoices.find(v => v.id === settings.selectedVoice);
    return voice?.name || 'Select a voice';
  };

  // Get the current KB name for display
  const getCurrentKbName = () => {
    // Check if KB options are loaded
    if (!kbOptions?.length) {
      return 'Loading knowledge bases...';
    }

    const kb = kbOptions.find(kb => kb.id === settings.kbId);
    return kb?.name || 'Select a knowledge base';
  };

  // Handle voice instructions change
  const handleVoiceInstructionsChange = (e) => {
    if (isAudioActive) return;

    const value = e.target.value;
    setVoiceInstructions(value);
    setSettings(prev => ({
      ...prev,
      voice_instructions: value
    }));
  };

  // Reset voice instructions when switching away from OpenAI voices
  useEffect(() => {
    if (!isOpenAIVoice && settings.voice_instructions) {
      setSettings(prev => {
        const newSettings = { ...prev };
        delete newSettings.voice_instructions;
        return newSettings;
      });
      setVoiceInstructions('');
    }
  }, [isOpenAIVoice, setSettings]);

  return (
    <div className="relative w-full">
      {/* Background overlay */}
      {(isExpanded || isAnimating) && (
        <div
          className={`fixed inset-0 z-10 transition-opacity duration-300 ease-in-out ${
            isExpanded ? 'opacity-100' : 'opacity-0'
          }`}
          onClick={() => !isAnimating && toggleAccordion()}
          aria-hidden="true"
        />
      )}

      {/* Settings Panel - Absolutely positioned to open upwards */}
      <div
        ref={panelRef}
        className={`absolute bottom-full left-0 right-0 mb-2 flex justify-center transform transition-all duration-300 ease-in-out origin-bottom z-30 ${
          isExpanded ? 'opacity-100 scale-y-100' : 'opacity-0 scale-y-0 pointer-events-none'
        }`}
        style={{ maxHeight: 'calc(95vh - 70px)' }}
      >
        <div className="bg-white rounded-lg shadow-md p-4 w-full max-w-lg relative overflow-y-auto">
          {/* Close button */}
          <button
            className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 focus:outline-none z-10"
            onClick={toggleAccordion}
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>

          <div className="space-y-2.5 pt-2">
            {/* Knowledge Base section */}
            <div>
              <h3 className="text-md font-semibold mb-1 text-gray-700">AI Voice Agent Memory</h3>
              <div className="relative" ref={kbDropdownRef}>
                <div
                  className={`flex items-center justify-between p-1.5 bg-white border border-gray-200 rounded-lg transition-colors ${
                    isAudioActive
                      ? 'opacity-70 cursor-not-allowed'
                      : 'cursor-pointer hover:border-gray-400'
                  }`}
                  onClick={() => !isAudioActive && setIsKbDropdownOpen(!isKbDropdownOpen)}
                >
                  <div className="font-medium text-sm text-gray-800">
                    {getCurrentKbName()}
                  </div>
                  <svg
                    className={`w-4 h-4 text-gray-500 transition-transform ${isKbDropdownOpen ? 'transform rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>

                {/* Dropdown menu */}
                {isKbDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden">
                    {kbOptions && kbOptions.length > 0 ? (
                      kbOptions.map((kb) => (
                        <div
                          key={kb.id}
                          className={`p-2 cursor-pointer hover:bg-gray-100 transition-colors ${
                            kb.id === settings.kbId ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                          }`}
                          onClick={() => {
                            if (!isAudioActive) {
                              // Update kb selection
                              handleKnowledgeBaseChange({ target: { value: kb.id } });
                              setIsKbDropdownOpen(false);
                            }
                          }}
                        >
                          <span className={`text-sm ${kb.id === settings.kbId ? 'font-medium' : ''}`}>{kb.name}</span>
                        </div>
                      ))
                    ) : (
                      <div className="p-2 text-sm text-gray-500">
                        Loading knowledge bases...
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Welcome Message section */}
            <div>
              <h3 className="text-md font-semibold mb-1 text-gray-700">
                Welcome Message
              </h3>
              <textarea
                ref={welcomeMessageRef}
                value={welcomeMessage}
                onChange={handleWelcomeMessageChange}
                placeholder="Enter a welcome message to greet users"
                className={`w-full p-2 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-gray-400 text-sm text-min-safe-input ${
                  isAudioActive
                    ? 'opacity-70 bg-gray-50 cursor-not-allowed'
                    : ''
                }`}
                rows={1}
                maxLength={200}
                disabled={isAudioActive}
                style={{
                  overflowY: 'hidden',
                  lineHeight: '1.3',
                  wordBreak: 'break-word',
                }}
              />
            </div>

            {/* Voice sections */}
            {renderVoiceSection('OpenAI', voiceOptions.openai)}

            {/* Voice Instructions (only for OpenAI voices) */}
            <div
              className={`overflow-hidden transition-all duration-300 ease-in-out ${
                isOpenAIVoice
                  ? 'max-h-16 opacity-100 my-0.5'
                  : 'max-h-0 opacity-0 my-0'
              }`}
            >
              <div className="flex items-center justify-between mb-0.5">
                <h4 className="text-sm font-medium text-gray-700">
                  Voice Instructions
                </h4>
              </div>
              <textarea
                value={voiceInstructions}
                onChange={handleVoiceInstructionsChange}
                placeholder="Example: Speak in a friendly tone. Keep responses concise."
                className={`w-full p-1.5 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-gray-400 text-xs text-min-safe-input ${
                  isAudioActive
                    ? 'opacity-70 bg-gray-50 cursor-not-allowed'
                    : ''
                }`}
                rows={1}
                maxLength={200}
                disabled={isAudioActive || !isOpenAIVoice}
                style={{ overflowY: 'auto' }}
              />
            </div>

            {renderVoiceSection('Cartesia', voiceOptions.cartesia)}
            {renderVoiceSection('ElevenLabs', voiceOptions.elevenlabs)}
          </div>
        </div>
      </div>

      {/* Accordion Button - Fixed at bottom */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          toggleAccordion();
        }}
        className="w-full max-w-lg mx-auto bg-white rounded-lg p-3 hover:bg-gray-50 transition-colors flex justify-between items-center z-30 relative"
      >
        <div className="flex items-center space-x-2">
          <svg
            className={`w-5 h-5 transform transition-transform duration-300 ${isExpanded ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
          <span className="font-medium text-gray-700">Voice Settings</span>
        </div>
        <div className="text-sm text-gray-700 font-medium">
          {getCurrentVoiceName()} | {getCurrentKbName()}
        </div>
      </button>
    </div>
  );
};

export default VoiceSettingsAccordion;
