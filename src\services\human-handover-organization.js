import generateApiEndpoint from '@/helpers/generateApiEndpoint';
import http from './http';
import { DEFAULT_HEADERS } from './constants.service';

export const getOrganizations = () => {
  return http.get(
    generateApiEndpoint('human-handover/organizations'),
    {
      headers: DEFAULT_HEADERS
    });
};
export const createOrganization = (params) => {
  return http.post(
    generateApiEndpoint('human-handover/organizations'),
    params,
    { headers: DEFAULT_HEADERS }
  );
};

export const getOrganizationById = (organization_id) => {
  return http.get(
    generateApiEndpoint(`human-handover/organizations/${organization_id}`),
    { headers: DEFAULT_HEADERS }
  );
};

export const updateOrganization = (organization_id, params) => {
  return http.patch(
    generateApiEndpoint(`human-handover/organizations/${organization_id}`),
    params,
    { headers: DEFAULT_HEADERS }
  );
};

export const addOrganizationMember = (organization_id, params) => {
  return http.post(
    generateApiEndpoint(
      `human-handover/user-organization/${organization_id}/members`
    ),
    params,
    { headers: DEFAULT_HEADERS }
  );
};

export const updateOrganizationMember = (organization_id, user_id, params) => {
  return http.patch(
    generateApiEndpoint(`human-handover/user-organization/${organization_id}/members/${user_id}}`),
    params,
    { headers: DEFAULT_HEADERS }
  );
};

export const deleteOrganizationMember = (organization_id, user_id) => {
  return http.delete(
    generateApiEndpoint(`human-handover/user-organization/${organization_id}/members/${user_id}}`),
    { headers: DEFAULT_HEADERS }
  );
};

export const cancelInvitation = (organization_id, user_id) => {
  return http.post(
    generateApiEndpoint(`human-handover/user-organization/${organization_id}/members/${user_id}/cancel`),
    { headers: DEFAULT_HEADERS }
  );
};

export const acceptInvitationMemberOrganization = (organization_id, token) => {
  return http.post(
    generateApiEndpoint(`human-handover/user-organization/${organization_id}/members/accept?token=${token}`),
    { headers: DEFAULT_HEADERS }
  );
};

export const getCurrentUserInfo = (organization_id) => {
  return http.get(
    generateApiEndpoint(`human-handover/user-organization/${organization_id}/me`),
    { headers: DEFAULT_HEADERS }
  );
};

export const updateUserOrganization = (organization_id, params) => {
  return http.patch(
    generateApiEndpoint(`human-handover/user-organization/${organization_id}/me`),
    params,
    { headers: DEFAULT_HEADERS }
  );
};