const CameraIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.3328 0C16.6891 0 16.9779 0.300145 16.9779 0.670393V2.96386H19.1851C19.5414 2.96386 19.8303 3.264 19.8303 3.63425C19.8303 4.0045 19.5414 4.30464 19.1851 4.30464H16.9779V6.59811C16.9779 6.96836 16.6891 7.2685 16.3328 7.2685C15.9765 7.2685 15.6876 6.96836 15.6876 6.59811V4.30464H13.4805C13.1242 4.30464 12.8353 4.0045 12.8353 3.63425C12.8353 3.264 13.1242 2.96386 13.4805 2.96386H15.6876V0.670393C15.6876 0.300145 15.9765 0 16.3328 0ZM9.67743 7.26843C8.8399 7.26843 8.03667 7.61415 7.44445 8.22953C6.85223 8.84492 6.51952 9.67956 6.51952 10.5498C6.51952 11.4201 6.85223 12.2548 7.44445 12.8702C8.03667 13.4855 8.8399 13.8313 9.67743 13.8313C10.515 13.8313 11.3182 13.4855 11.9104 12.8702C12.5026 12.2548 12.8353 11.4201 12.8353 10.5498C12.8353 9.67956 12.5026 8.84492 11.9104 8.22953C11.3182 7.61415 10.515 7.26843 9.67743 7.26843ZM6.53206 7.28145C7.36626 6.41462 8.49769 5.92764 9.67743 5.92764C10.8572 5.92764 11.9886 6.41462 12.8228 7.28145C13.657 8.14828 14.1257 9.32396 14.1257 10.5498C14.1257 11.7757 13.657 12.9514 12.8228 13.8182C11.9886 14.6851 10.8572 15.172 9.67743 15.172C8.49769 15.172 7.36626 14.6851 6.53206 13.8182C5.69785 12.9514 5.2292 11.7757 5.2292 10.5498C5.2292 9.32396 5.69785 8.14828 6.53206 7.28145ZM4.51613 1.229C2.02194 1.229 0 3.33001 0 5.92175V15.3072C0 17.899 2.02194 20 4.51613 20H15.4839C17.9781 20 20 17.899 20 15.3072V9.94411C20 9.57386 19.7112 9.27371 19.3548 9.27371C18.9985 9.27371 18.7097 9.57386 18.7097 9.94411V15.3072C18.7097 17.1585 17.2654 18.6592 15.4839 18.6592H4.51613C2.73457 18.6592 1.29032 17.1585 1.29032 15.3072V5.92175C1.29032 4.07051 2.73457 2.56978 4.51613 2.56978H10.3226C10.6789 2.56978 10.9677 2.26964 10.9677 1.89939C10.9677 1.52914 10.6789 1.229 10.3226 1.229H4.51613Z"
      fill="currentColor"
      fillOpacity={0.2}
    />
  </svg>
);
export default CameraIcon;
