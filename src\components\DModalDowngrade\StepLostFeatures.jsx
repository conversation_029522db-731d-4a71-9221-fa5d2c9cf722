import { useUserStore } from '@/stores/user/userStore';
import { downgradeStepsEnum } from './stepDowngrade';
import { useEffect } from 'react';
import useDanteApi from '@/hooks/useDanteApi';
import { getDowngradeFeatures } from '@/services/plans.service';
import CloseIcon from '../Global/Icons/CloseIcon';
import DButton from '../Global/DButton';
import clsx from 'clsx';
import DSpinner from '../Global/DSpinner';

const StepLostFeatures = ({
  changesBetweenTiers,
  handleNextStep,
  currentStep,
  setStep,
  setTitle,
  new_tier,
  handleClose,
  isLoading,
}) => {
  const { user } = useUserStore();

  const handleNext = () => {
    handleNextStep(downgradeStepsEnum.REASONS);
  };

  useEffect(() => {
    const firstName = user?.first_name || user?.full_name?.split(' ')[0];
    setTitle('');
  }, []);

  return (
    <div className="flex flex-col gap-size4 relative">
      {/* Header with title and close button */}
      <div className="absolute top-[-45px] left-0 right-0 -mt-14 bg-grey-5 rounded-t-size1 border border-grey-5 border-b-0 flex items-center py-size1 px-size3">
        <div className="flex items-center gap-size2">
          <div className="bg-purple-5 rounded-full p-[6px] flex items-center justify-center">
            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-purple-200">
              <CloseIcon className="w-4 h-4 text-white" />
            </div>
          </div>
          <span className="font-medium text-sm">Features You'll Lose</span>
        </div>
        <button
          onClick={handleClose}
          className="ml-auto text-grey-50 hover:text-grey-75 text-xl font-bold"
          aria-label="Close"
        >
          ×
        </button>
      </div>

      {/* Existing content */}
      <div className={clsx(
        'transition-all duration-300 flex flex-col gap-size3 p-size4 rounded-size1 bg-grey-2 border border-grey-5 overflow-hidden overflow-y-auto',
        'max-h-[356px] h-[356px]'
      )}>
        <h3 className="text-lg font-semibold">
          Don't lose the features you love
        </h3>
        {isLoading ? (
          <div className="flex justify-center items-center h-full">
            <DSpinner />
          </div>
        ) : (
          changesBetweenTiers?.features?.length > 0 && (
            <ul className="flex flex-col gap-size2 list-none">
              {changesBetweenTiers?.features?.map((feature) => (
                <li key={feature.id} className="flex items-center gap-size1">
                  <CloseIcon className="text-purple-200" /> {feature}
                </li>
              ))}
            </ul>
          )
        )}
      </div>
      <footer className="flex flex-col gap-size2">
        <DButton variant="dark" size="lg" onClick={handleClose} fullWidth>
          Never mind, I will stay
        </DButton>
        <DButton variant="outlined" size="lg" onClick={handleNext} fullWidth>
          I still want to cancel
        </DButton>
      </footer>
    </div>
  );
};

export default StepLostFeatures;
