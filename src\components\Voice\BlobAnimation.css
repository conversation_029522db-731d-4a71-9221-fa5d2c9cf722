/* BlobAnimation.css */
:root {
  --shape-1: "M 100 600 q 0 -500, 500 -500 t 500 500 t -500 500 T 100 600 z";
  --shape-2: "M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z";
  --shape-3: "M 100 600 q 0 -400, 500 -500 t 400 500 t -500 500 T 100 600 z";
  --shape-4: "M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z";
  --shape-5: "M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z";
  --shape-6: "M 100 600 q 100 -600, 500 -500 t 400 500 t -500 500 T 100 600 z";

  /* Purple theme - matches our app */
  --bg-0: #101030;
  --bg-1: #050515;
  --blob-1: #984ddf; /* Primary purple */
  --blob-2: #6200ee; /* Darker purple */
  --blob-3: #a78bfa; /* Light purple */
  --blob-4: #050515; /* Dark background */
  
  /* User theme - orange */
  --user-blob-1: #ff7e5f; /* Primary orange */
  --user-blob-2: #e04e1d; /* Darker orange */
  --user-blob-3: #ffb38a; /* Light orange */
  
  /* Bot theme - blue */
  --bot-blob-1: #4a8cff; /* Primary blue */
  --bot-blob-2: #0066ff; /* Darker blue */
  --bot-blob-3: #a4c8ff; /* Light blue */
  
  /* Calling theme - lighter purple */
  --calling-blob-1: #b85aff; /* Primary calling purple */
  --calling-blob-2: #9724ff; /* Darker calling purple */
  --calling-blob-3: #d8a9ff; /* Light calling purple */

  /* Default/idle colors - purple theme */
  --circle-inner: rgba(152, 77, 223, 0.9);      /* Primary purple */
  --circle-middle: rgba(152, 77, 223, 0.5);     /* Semi-transparent purple */
  --circle-outer: rgba(152, 77, 223, 0.2);      /* Very transparent purple */
  
  /* User theme - orange */
  --user-inner: rgba(255, 126, 95, 0.9);        /* Primary orange */
  --user-middle: rgba(255, 126, 95, 0.5);       /* Semi-transparent orange */
  --user-outer: rgba(255, 126, 95, 0.2);        /* Very transparent orange */
  
  /* Bot theme - blue */
  --bot-inner: rgba(74, 140, 255, 0.9);         /* Primary blue */
  --bot-middle: rgba(74, 140, 255, 0.5);        /* Semi-transparent blue */
  --bot-outer: rgba(74, 140, 255, 0.2);         /* Very transparent blue */
  
  /* Calling theme - light purple */
  --calling-inner: rgba(184, 90, 255, 0.9);     /* Primary purple */
  --calling-middle: rgba(184, 90, 255, 0.5);    /* Semi-transparent purple */
  --calling-outer: rgba(184, 90, 255, 0.2);     /* Very transparent purple */
}

.blob-animation-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: visible;
}

.blobs {
  width: min(60vw, 60vh);
  height: min(60vw, 60vh);
  max-height: 220px;
  max-width: 220px;
  will-change: transform;
  overflow: visible;
}

.blob-animation-container.shortcuts-blob .blobs {
  width: min(40vw, 40vh);
  height: min(40vw, 40vh);
  max-height: 180px;
  max-width: 200px;
}

/* Responsive sizing for different screen sizes */
@media (min-width: 640px) {
  .blobs {
    width: min(50vw, 50vh);
    height: min(50vw, 50vh);
    max-height: 250px;
    max-width: 250px;
  }
}

@media (min-width: 768px) {
  .blobs {
    width: min(40vw, 40vh);
    height: min(40vw, 40vh);
    max-height: 280px;
    max-width: 280px;
  }
}

.blobs svg {
  position: relative;
  height: 100%;
  z-index: 2;
}

/* Blob wrapper - this is what animates as a whole */
.blobs .blob-wrapper {
  animation: rotate 25s infinite alternate ease-in-out;
  transform-origin: 50% 50%;
  will-change: transform;
  transition: transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Blob wrapper in speaking state */
.blobs .blob-wrapper.speaking {
  animation: rotate 30s infinite alternate ease-in-out;
}

/* User speaking state */
.blobs .blob-wrapper.user-speaking .blob-1 path {
  fill: var(--user-blob-1);
}

.blobs .blob-wrapper.user-speaking .blob-2 path {
  fill: var(--user-blob-2);
}

.blobs .blob-wrapper.user-speaking .blob-3 path {
  fill: var(--user-blob-3);
}

/* Bot speaking state */
.blobs .blob-wrapper.bot-speaking .blob-1 path {
  fill: var(--bot-blob-1);
}

.blobs .blob-wrapper.bot-speaking .blob-2 path {
  fill: var(--bot-blob-2);
}

.blobs .blob-wrapper.bot-speaking .blob-3 path {
  fill: var(--bot-blob-3);
}

/* Calling state */
.blobs .blob-wrapper.calling .blob-1 path {
  fill: var(--calling-blob-1);
}

.blobs .blob-wrapper.calling .blob-2 path {
  fill: var(--calling-blob-2);
}

.blobs .blob-wrapper.calling .blob-3 path {
  fill: var(--calling-blob-3);
}

/* Individual blobs don't rotate relative to each other */
.blobs .blob {
  opacity: 0.7;
  will-change: transform;
  transform-origin: 50% 50%;
}

.blobs .blob path {
  animation: blob-anim-1 var(--animation-duration, 5s) infinite alternate
    cubic-bezier(0.4, 0.0, 0.2, 1);
  transform-origin: 50% 50%;
  transform: scale(0.8);
  transition: fill 800ms ease, d 800ms cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: d, transform;
}

.blobs .blob.alt {
  opacity: 0.3;
}

.blobs .blob-1 path {
  fill: var(--blob-1);
  filter: blur(0.4rem);
}

.blobs .blob-2 path {
  fill: var(--blob-2);
  animation-name: blob-anim-2;
  animation-duration: var(--animation-duration, 7s);
  filter: blur(0.35rem);
  transform: scale(0.78);
}

.blobs .blob-3 path {
  fill: var(--blob-3);
  animation-name: blob-anim-3;
  animation-duration: var(--animation-duration, 6s);
  filter: blur(0.25rem);
  transform: scale(0.76);
}

.blobs .blob-4 {
  opacity: 0.9;
}

.blobs .blob-4 path {
  fill: var(--blob-4);
  animation-name: blob-anim-4;
  animation-duration: var(--animation-duration, 10s);
  filter: blur(4rem);
  transform: scale(0.5);
}

/* Responsive blur filters for different screen sizes */
@media (min-width: 640px) {
  .blobs .blob-1 path {
    filter: blur(0.5rem);
  }

  .blobs .blob-2 path {
    filter: blur(0.4rem);
  }

  .blobs .blob-3 path {
    filter: blur(0.28rem);
  }

  .blobs .blob-4 path {
    filter: blur(5rem);
  }
}

@media (min-width: 768px) {
  .blobs .blob-1 path {
    filter: blur(0.6rem);
  }

  .blobs .blob-2 path {
    filter: blur(0.5rem);
  }

  .blobs .blob-3 path {
    filter: blur(0.3rem);
  }

  .blobs .blob-4 path {
    filter: blur(6rem);
  }
}

.blobs .blob-4.alt {
  opacity: 0.8;
}

.phone-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

@keyframes blob-anim-1 {
  0% {
    d: path(var(--shape-1));
  }
  33% {
    d: path(var(--shape-2));
  }
  66% {
    d: path(var(--shape-3));
  }
  100% {
    d: path(var(--shape-4));
  }
}

@keyframes blob-anim-2 {
  0% {
    d: path(var(--shape-3));
  }
  33% {
    d: path(var(--shape-5));
  }
  66% {
    d: path(var(--shape-2));
  }
  100% {
    d: path(var(--shape-6));
  }
}

@keyframes blob-anim-3 {
  0% {
    d: path(var(--shape-2));
  }
  33% {
    d: path(var(--shape-4));
  }
  66% {
    d: path(var(--shape-6));
  }
  100% {
    d: path(var(--shape-3));
  }
}

@keyframes blob-anim-4 {
  0% {
    d: path(var(--shape-4));
  }
  33% {
    d: path(var(--shape-6));
  }
  66% {
    d: path(var(--shape-2));
  }
  100% {
    d: path(var(--shape-5));
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Circle Animation CSS */
.circle-animation-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: visible;
}

.circles {
  width: min(60vw, 60vh);
  height: min(60vw, 60vh);
  max-height: 220px;
  max-width: 220px;
  will-change: transform;
  overflow: visible;
}

.circle-animation-container.shortcuts-blob .circles {
  width: min(40vw, 40vh);
  height: min(40vw, 40vh);
  max-height: 180px;
  max-width: 200px;
}

/* Responsive sizing for different screen sizes */
@media (min-width: 640px) {
  .circles {
    width: min(50vw, 50vh);
    height: min(50vw, 50vh);
    max-height: 250px;
    max-width: 250px;
  }
}

@media (min-width: 768px) {
  .circles {
    width: min(40vw, 40vh);
    height: min(40vw, 40vh);
    max-height: 280px;
    max-width: 280px;
  }
}

/* Circle styling */
.circles circle {
  transform-origin: center;
  transition: r 0.4s ease-out, fill 0.5s ease;
}

/* Default color theme */
.circles .circle-inner {
  fill: var(--circle-inner);
  filter: drop-shadow(0 0 8px rgba(152, 77, 223, 0.3));
}

.circles .circle-middle {
  fill: var(--circle-middle);
  animation: pulse-middle 3s infinite alternate ease-in-out;
}

.circles .circle-outer {
  fill: var(--circle-outer);
  animation: pulse-outer 4s infinite alternate ease-in-out;
}

/* User speaking theme */
.circles.user-speaking .circle-inner {
  fill: var(--user-inner);
  filter: drop-shadow(0 0 10px rgba(255, 126, 95, 0.4));
}

.circles.user-speaking .circle-middle {
  fill: var(--user-middle);
}

.circles.user-speaking .circle-outer {
  fill: var(--user-outer);
}

/* Bot speaking theme */
.circles.bot-speaking .circle-inner {
  fill: var(--bot-inner);
  filter: drop-shadow(0 0 10px rgba(74, 140, 255, 0.4));
}

.circles.bot-speaking .circle-middle {
  fill: var(--bot-middle);
}

.circles.bot-speaking .circle-outer {
  fill: var(--bot-outer);
}

/* Calling theme */
.circles.calling .circle-inner {
  fill: var(--calling-inner);
  filter: drop-shadow(0 0 10px rgba(184, 90, 255, 0.4));
  animation: pulse-calling 1.5s infinite alternate ease-in-out;
}

.circles.calling .circle-middle {
  fill: var(--calling-middle);
  animation: pulse-middle 2s infinite alternate ease-in-out;
}

.circles.calling .circle-outer {
  fill: var(--calling-outer);
  animation: pulse-outer 2.5s infinite alternate ease-in-out;
}

/* Smooth pulse animations */
@keyframes pulse-middle {
  0% {
    opacity: 0.6;
    transform: scale(0.96);
  }
  100% {
    opacity: 1;
    transform: scale(1.04);
  }
}

@keyframes pulse-outer {
  0% {
    opacity: 0.3;
    transform: scale(0.97);
  }
  100% {
    opacity: 0.7;
    transform: scale(1.03);
  }
}

@keyframes pulse-calling {
  0% {
    opacity: 0.7;
    transform: scale(0.94);
  }
  100% {
    opacity: 1;
    transform: scale(1.06);
  }
}
