import { useEffect, useState } from 'react';

import validatePassword from '@/helpers/validatePassword';

import <PERSON><PERSON><PERSON><PERSON> from '../../Global/DAlert';
import DInput from '../../Global/DInput/DInput';
import DInputBlock from '../../Global/DInput/DInputBlock';

/**
 * ChangePasswordForm component renders a form for changing passwords, with validation for password complexity.
 *
 * @param {Object} props - Component props.
 * @param {Function} props.setCanSubmit - Callback to set the form's submission state.
 * @param {boolean} props.loading - Loading state to control submission UI.
 * @param {string} props.error - Error message to display.
 * @param {boolean} [props.showCurrentPassword=false] - Whether to display the current password input field.
 *
 * @returns {JSX.Element} The rendered ChangePasswordForm component.
 */
const ChangePasswordForm = ({
  setCanSubmit,
  setPasswords,
  loading,
  error,
  showCurrentPassword = false,
}) => {
  const [form, setForm] = useState({
    current_password: '',
    new_password: '',
    confirm_new_password: '',
  });

  const [errorNewPassword, setErrorNewPassword] = useState([]);
  const [errorConfirmPassword, setErrorConfirmPassword] = useState('');
  const [hasError, setHasError] = useState(false);

  /**
   * Handles input field changes and updates the form state.
   *
   * @param {Object} e - The event object from the input field.
   */
  const handleChange = (e) => {
    const field = e.target.name;
    const value = e.target.value;

    let canSubmit = true;

    // Create updated form first so we can use it for validation
    const updatedForm = { ...form, [field]: value };
    setForm(updatedForm);

    // Only validate password if it's not empty
    if (field === 'new_password') {
      if (value === '') {
        // Don't show errors for empty password
        setErrorNewPassword([]);
        setHasError(false);
        canSubmit = false;
      } else {
        const validate = validatePassword(value);
        setErrorNewPassword(validate.errorMessages);
        setHasError(!validate.isValid);
        canSubmit = validate.isValid && canSubmit;
      }

      // Check if confirm password needs to be updated
      if (updatedForm.confirm_new_password) {
        if (value !== updatedForm.confirm_new_password) {
          setErrorConfirmPassword('Passwords don\'t match');
          canSubmit = false;
        } else {
          setErrorConfirmPassword('');
        }
      }
    }

    // Only validate confirm password if it's not empty
    if (field === 'confirm_new_password') {
      if (value === '') {
        setErrorConfirmPassword('');
        canSubmit = false;
      } else if (value !== updatedForm.new_password) {
        setErrorConfirmPassword('Passwords don\'t match');
        canSubmit = false;
      } else {
        setErrorConfirmPassword('');
      }
    }

    // Check if both password fields are filled
    if (!updatedForm.new_password || !updatedForm.confirm_new_password) {
      canSubmit = false;
    }

    setCanSubmit(canSubmit);
    setPasswords({
      current_password: updatedForm.current_password,
      new_password: updatedForm.new_password,
      canSubmit,
    });
  };

  useEffect(() => {
    setHasError(error);
  }, [error]);

  return (
    <div className="flex flex-col gap-size3">
      {showCurrentPassword && (
        <DInputBlock label="Current password" name="current_password">
          <DInput
            name="current_password"
            type="password"
            onChange={handleChange}
            disabled={loading}
          />
        </DInputBlock>
      )}
      <DInputBlock label="Password" name="new_password">
        <>
          <DInput
            name="new_password"
            type="password"
            autoComplete="new-password"
            error={form.new_password !== '' && hasError}
            placeholder="Enter your password"
            onChange={handleChange}
            disabled={loading}
            hiddenError
          />
          <div className="flex flex-col">
            {errorNewPassword.map((error, index) => (
              <DAlert
                key={index}
                type="input"
                state={error.status ? 'negative' : 'positive'}
              >
                {error.case}
              </DAlert>
            ))}
          </div>
        </>
      </DInputBlock>
      <DInputBlock label="Confirm password" name="confirm_new_password">
        <DInput
          name="confirm_new_password"
          type="password"
          autoComplete="new-password"
          error={form.confirm_new_password !== '' && errorConfirmPassword}
          placeholder="Re-enter password"
          onChange={handleChange}
          disabled={loading}
        />
      </DInputBlock>
    </div>
  );
};

export default ChangePasswordForm;
