import { useState } from 'react';

import DAlert from '@/components/Global/DAlert';
import DButton from '@/components/Global/DButton';
import DCheckbox from '@/components/Global/DCheckbox';
import DInput from '@/components/Global/DInput/DInput';
import DModal from '@/components/Global/DModal';
import * as teamManagementService from '@/services/teamManagement.service';
import DTooltip from '@/components/Global/DTooltip';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
const AddRole = ({
  open,
  onClose,
  onBack,
  rolePermissions,
  selectedPermissions = [],
  setSelectedPermissions,
  createTeamRole,
  roleName,
  setRoleName,
  error,
  customRole,
  setCustomRole,
  setError
}) => {
  const addPermissions = (name, value) => {
    if(selectedPermissions.length > 0 && selectedPermissions.includes(name)) {
      setSelectedPermissions(selectedPermissions.filter((permission) => permission !== name));
    } else {
      setSelectedPermissions([...selectedPermissions, name]);
    }
    setCustomRole({
      ...customRole,
      [name]: value
    });
  };

  return (
    <DModal
      title="Add custom role"
      isOpen={open}
      onClose={onClose}
      backBtn
      onBack={onBack}
      className='!min-w-[550px]'
      footer={
        <div className="flex items-center gap-size1 w-full">
          <DButton onClick={onClose} variant="grey" size="md" fullWidth>
            Cancel
          </DButton>
          <DButton onClick={createTeamRole} variant="dark" fullWidth size="md">
            Create role
          </DButton>
        </div>
      }
    >
      <div className="flex flex-col gap-size5">
        <div className="flex flex-col gap-size0">
          <p className="text-base font-medium tracking-tight">Role name</p>
          <DInput
            placeholder="Enter role name"
            value={roleName}
            onChange={(e) => setRoleName(e.target.value)}
            error={error.roleName}
          />
        </div>
        <div className="flex flex-col gap-size1">
          <div className="flex flex-col gap-size0">
            <p className="text-base font-medium tracking-tight">Set role permissions</p>
            <p className="text-grey-50 text-xs font-light tracking-tight">
              Start by selecting a Primary permission
            </p>
          </div>
          <div className='flex flex-col gap-size1 max-h-[300px] overflow-y-auto no-scrollbar'>
            {rolePermissions.map((permission, index) => {
              const isSelected = selectedPermissions?.includes(permission.name);

              return (
                  <div className="flex items-center py-size0 gap-size1" key={index}>
                  <DCheckbox
                    key={permission.name}
                    checked={isSelected}
                    label={permission.label}
                    style={{width: 'fit-content'}}
                    onChange={(value) => addPermissions(permission.name, value)}
                    hideError
                  />
                  <DTooltip content={permission.help_text}>
                    <InfoIcon className="size-3 text-grey-50" />
                  </DTooltip>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </DModal>
  );
};

export default AddRole;
