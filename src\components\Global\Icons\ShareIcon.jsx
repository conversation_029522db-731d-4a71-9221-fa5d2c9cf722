import * as React from 'react';
const ShareIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.914 3.163a.9.9 0 0 1 .717-.33c.23.006.411.122.526.203.119.085.258.205.41.336l.017.014 6.713 5.753.01.01c.***************.243.219a.9.9 0 0 1 .217.329.9.9 0 0 1 0 .606.9.9 0 0 1-.217.33c-.068.07-.155.144-.242.218l-.011.01-6.713 5.754-.016.014c-.153.13-.292.25-.411.335-.115.081-.297.197-.526.202a.9.9 0 0 1-.717-.33c-.145-.177-.176-.39-.189-.53-.013-.146-.013-.329-.013-.53v-2.79c-.178.011.178-.024 0 0a8.53 8.53 0 0 0-5.454 3.01l-.266.32a.35.35 0 0 1-.62-.224v-.9a8.16 8.16 0 0 1 6.34-7.95c-.138.023.137-.032 0 0v-3.02c0-.2 0-.383.013-.528.013-.14.044-.354.189-.532m.8.795-.002.288V8.07l-.413.073a7.16 7.16 0 0 0-5.88 6.241 9.53 9.53 0 0 1 5.76-2.45l.533-.036v3.856q0 .176.002.288l.22-.187 6.712-5.753.118-.102-.118-.102-6.713-5.753z"
      fill="currentColor"
    />
  </svg>
);
export default ShareIcon;
