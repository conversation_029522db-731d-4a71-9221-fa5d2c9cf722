const CuriousIcon = (props) => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8 6.50184C8.17654 6.00118 8.52449 5.57911 8.98229 5.31032C9.44008 5.04153 9.9782 4.94335 10.5014 5.03314C11.0246 5.12294 11.4992 5.39493 11.8412 5.80098C12.1832 6.20703 12.3705 6.72097 12.37 7.25184C12.37 8.75084 10.12 9.49984 10.12 9.49984M10.15 12.4998H10.16M1 5.79976C1 4.11976 1 3.27976 1.327 2.63776C1.61462 2.07329 2.07354 1.61437 2.638 1.32676C3.28 0.999756 4.12 0.999756 5.8 0.999756H14.2C15.88 0.999756 16.72 0.999756 17.362 1.32676C17.9265 1.61437 18.3854 2.07329 18.673 2.63776C19 3.27976 19 4.11976 19 5.79976V11.4998C19 12.8978 19 13.5968 18.772 14.1478C18.6212 14.5118 18.4002 14.8427 18.1216 15.1213C17.8429 15.4 17.5121 15.621 17.148 15.7718C16.597 15.9998 15.898 15.9998 14.5 15.9998C14.011 15.9998 13.767 15.9998 13.54 16.0528C13.2381 16.1245 12.957 16.2656 12.719 16.4648C12.539 16.6138 12.393 16.8088 12.1 17.1998L10.64 19.1468C10.423 19.4368 10.314 19.5808 10.181 19.6328C10.0646 19.678 9.93543 19.678 9.819 19.6328C9.686 19.5808 9.577 19.4358 9.36 19.1468L7.9 17.1998C7.607 16.8098 7.46 16.6138 7.281 16.4648C7.043 16.2656 6.76193 16.1245 6.46 16.0528C6.233 15.9998 5.989 15.9998 5.5 15.9998C4.102 15.9998 3.403 15.9998 2.852 15.7718C2.48791 15.621 2.15709 15.4 1.87844 15.1213C1.59979 14.8427 1.37877 14.5118 1.228 14.1478C1 13.5968 1 12.8978 1 11.4998V5.79976Z"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default CuriousIcon;
