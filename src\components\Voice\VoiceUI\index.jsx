import { useState, useEffect, useRef } from 'react';
import './index.css';
import PlayIcon from '@/components/Global/Icons/PlayIcon';
import waves from './Waves.json';
import Lottie from 'lottie-react';
import StopIcon from '@/components/Global/Icons/StopIcon';

const VoiceUI = () => {

    const [isPlaying, setIsPlaying] = useState(false);
    const [audio, setAudio] = useState(null);
    const lottieRef = useRef(null);

    useEffect(() => {
        // Create audio instance
        const audioElement = new Audio();
        setAudio(audioElement);

        // Clean up on unmount
        return () => {
            if (audioElement) {
                audioElement.pause();
                audioElement.src = '';
            }
        };
    }, []);

    const togglePlayback = () => {
        if (!audio) return;

        if (isPlaying) {
            audio.pause();
            // Stop animation
            if (lottieRef.current) {
                lottieRef.current.pause();
            }
            setIsPlaying(false);
        } else {
            // Set audio source if needed
            if (!audio.src) {
                audio.src = 'https://commondatastorage.googleapis.com/codeskulptor-demos/riceracer_assets/music/lose.ogg'; // Replace with your actual audio URL
            }
            
            audio.play().then(() => {
                // Start animation
                if (lottieRef.current) {
                    lottieRef.current.play();
                }
                setIsPlaying(true);
            }).catch(error => {
                console.error('Error playing audio:', error);
            });
        }
    };

    return <div className="w-full h-full bg-black rounded-size1">
        <div className="voice-ui w-full h-full rounded-size1 flex flex-col justify-center overflow-hidden">
            <Lottie 
                animationData={waves} 
                loop={true} 
                autoplay={true}
                lottieRef={lottieRef}
                style={{ 
                    height: 'auto', 
                    width: '100%', 
                    maxWidth: 500,
                    margin: '0 auto'
                }} 
                rendererSettings={{
                    preserveAspectRatio: 'xMidYMid slice',
                    clearCanvas: true
                }}
            />
            {/* <div className='flex flex-col gap-size5 p-size3'>
                <p className='text-white text-xl font-medium text-center'>
                    {isPlaying ? 'Listening...' : 'Press play to start'}
                </p>
                <button 
                    className='bg-[#FFFFFF33] rounded-full p-size1 size-10 flex items-center justify-center'
                    onClick={togglePlayback}
                >
                    {isPlaying ? (
                        <StopIcon className="text-white"/>
                    ) : (
                        <PlayIcon className="text-white"/>
                    )}
                </button>
            </div> */}
        </div>
    </div>
}

export default VoiceUI;