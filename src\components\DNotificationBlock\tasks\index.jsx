import { DateTime } from 'luxon';
import { <PERSON> } from 'react-router-dom';

import useNotificationsStore from '@/stores/notifications/notifications';

import DBadge from '../../Global/DBadge';
import NotificationIcon from '../../Global/Icons/NotificationIcon';

/**
 * DNotificationBlockTasks component displays a list of recent task notifications.
 * Shows the three most recent tasks with their status, last update time, and a link to the activity log.
 *
 * @returns {JSX.Element} The rendered tasks notification block component.
 */
const DNotificationBlockTasks = () => {
  const {
    notifications: { tasks },
  } = useNotificationsStore();

  return (
    <div className="transition-all duration-300 flex flex-col gap-size2 py-size1">
      {tasks.length > 0 ? (
        <>
          <div className="flex flex-col items-center gap-size1 divide-y">
            {tasks.slice(0, 3).map((task) => (
              <div
                key={task.id}
                className="flex flex-col w-full items-start gap-size1 text-xs py-size1"
              >
                <header className="flex items-center gap-size1">
                  <DBadge type={task.status} label={task.status} size="sm" />
                  <p className="text-xs text-grey-20">
                    {DateTime.fromJSDate(task.updated_at).toRelative()}
                  </p>
                </header>
                <p>{task.title}</p>
              </div>
            ))}
          </div>
          <Link
            className="text-sm bg-grey-5 rounded-size0 text-black hover:text-grey-50 h-6 flex items-center justify-center"
            to="/activity-log"
          >
            See Activity Log
          </Link>
        </>
      ) : (
        <div className="min-h-36 flex flex-col items-center justify-center gap-size1">
          <div className="h-28 flex justify-center items-center">
            <NotificationIcon />
          </div>
          <div className="flex flex-col gap-size0 text-center">
            <p className="text-xs text-grey-50">No tasks</p>
            <p className="text-2xs text-grey-50">
              New tasks will be shown here
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default DNotificationBlockTasks;
