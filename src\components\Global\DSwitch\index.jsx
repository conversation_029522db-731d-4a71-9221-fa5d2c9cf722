import { Field, Label, Switch } from '@headlessui/react';
import clsx from 'clsx';

export default function DSwitch({
  onChange,
  checked,
  disabled = false,
  variant = 'default',
  label,
  className,
  size,
  id,
}) {
  const fontSize = size === 'sm' ? 'text-sm' : 'text-base';

  return (
    <Field
      disabled={disabled}
      className={clsx('flex items-center gap-2', className)}
      data-testid={`d-switch-${id || ''}`}
    >
      <Switch
        checked={checked}
        onChange={(newChecked) => {
          onChange(newChecked);
        }}
        onClick={(e) => e.stopPropagation()}
        data-testid={`d-switch-toggle-${id || ''}`}
        className={clsx(
          'group relative flex h-5 w-8 cursor-pointer rounded-full items-center transition-all duration-200 ease-in-out',
          'focus:outline-none',
          {
            'bg-grey-5': !checked,
            'outline-1 outline-white': !disabled,
            'bg-purple-200/10': checked && variant === 'default',
            'bg-green-100/10': checked && variant === 'positive',
            'bg-negative-100/10': checked && variant === 'negative',
            'opacity-50': disabled,
          }
        )}
      >
        <span
          aria-hidden="true"
          className={clsx(
            'pointer-events-none inline-block size-4 rounded-full shadow-lg transition-all duration-200 ease-in-out',
            {
              'translate-x-0 bg-grey-20': !checked,
              'translate-x-3': checked,
              'bg-purple-200': checked && variant === 'default',
              'bg-green-100': checked && variant === 'positive',
              'bg-negative-100': checked && variant === 'negative',
              'opacity-50': disabled,
            }
          )}
        />
      </Switch>
      <Label className={clsx('text-sm', fontSize, { 'opacity-50': disabled })}>
        {label}
      </Label>
    </Field>
  );
}
