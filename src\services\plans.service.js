import generateApiEndpoint from '@/helpers/generateApiEndpoint';
import { DEFAULT_HEADERS } from './constants.service';
import http from './http';


export const getPlans = () => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tiers/all', {
    headers: DEFAULT_HEADERS
  });
};

export const getTiers = () => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tiers/info', {
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

export const getAddOns = () => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tiers/add-ons', {
    headers: DEFAULT_HEADERS
  });
}

export const getCheckoutSession = (id, campaign, old_to_new) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'stripe/checkout_session',
    {},
    {
      params: {
        tier_id: id,
        campaign: campaign,
        old_to_new: old_to_new
      },
      headers: DEFAULT_HEADERS
    }
  );
};

export const getCheckoutSessionAddon = (id, quantity) => {

  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'stripe/add_on_checkout_session',
    {},
    {
      params: {
        add_on_id: id,
        quantity
      },
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const getCheckoutSessionExtraMessages = (id, campaign) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'stripe/checkout_session_topup',
    {},
    {
      params: {
        add_on_id: id,
      },
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const getCheckoutSessionCredits = (id) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'stripe/checkout_session_topup',
    {},
    {
      params: {
        price_id: id
      },
      headers: DEFAULT_HEADERS
    }
  );
};


export const getCredits = () => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'topup', {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const getDowngradeFeatures = (new_tier) => {
  return http.get(generateApiEndpoint('tiers/downgrade/features-lost-new'), {
    params: {

      new_tier
    },
    headers: DEFAULT_HEADERS
  });
};

export const sendDowngradeFeedback = ({ current_tier, new_tier, user_id, feedback }) => {

  return http.post(generateApiEndpoint('tiers/downgrade/feedback'), {}, {
    params: {
      current_tier,
      new_tier,
      user_id,
      feedback
    },
    headers: DEFAULT_HEADERS
  });
};

export const removeTeamMembers = ({ current_tier, new_tier, remove_user_ids, check }) => {
  return http.post(generateApiEndpoint('tiers/downgrade/team-management'), {
    current_tier,
    new_tier,
    remove_user_ids,
    check
  }, {

    headers: DEFAULT_HEADERS
  });
};

export const finishCancelDowngrade = () => {
  return http.post(generateApiEndpoint('stripe/cancel_trial'), {}, {
    headers: DEFAULT_HEADERS
  });
};

export const finishDowngrade = (tier_id) => {
  return http.post(generateApiEndpoint('stripe/checkout_session'), {}, {
    params: {
      tier_id
    },
    headers: DEFAULT_HEADERS
  });
};

export const getOnboardingTiers = () => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tiers/onboarding-tiers', {
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

export const applyDiscountOffer = (user_id) => {
  return http.post(generateApiEndpoint(`tiers/discount?user_id=${user_id}`), {}, {
    headers: DEFAULT_HEADERS
  });
}
