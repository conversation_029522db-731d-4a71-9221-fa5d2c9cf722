import { useState } from 'react';
import DButton from '../Global/DButton';
import DModal from '../Global/DModal';
import DSelect from '../Global/DSelect';
import { exportStatistics } from '@/services/conversations.service';
import { useParams } from 'react-router-dom';
import { DateTime } from 'luxon';
import generateFilename from '@/helpers/generateFileName';

const DModalExportInsights = ({ open, onClose, date_created_from, date_created_to, selectedChatbot }) => {
  const params = useParams();
  const [loading, setLoading] = useState(false);
  const [exportType, setExportType] = useState('credits_used');

  const handleExport = async () => {
    try{
      setLoading(true);
      const response = await exportStatistics(params.id, DateTime.fromISO(date_created_from).toFormat('yyyy-MM-dd'), DateTime.fromISO(date_created_to).toFormat('yyyy-MM-dd'), exportType);
      if (response.status === 200) {
        const download_url = response.data.download_url;
        window.open(download_url, '_blank');
      }
    }catch(error){
      console.log(error);
    }finally{
      setLoading(false);
    }
  }

  return (
    <div>
      <DModal
        isOpen={open}
        onClose={onClose}
        title="Export Insights"
        footer={
          <div className="flex justify-end w-full">
            <DButton variant="dark" fullWidth onClick={handleExport} loading={loading}>Export</DButton>
          </div>
        }
      >
        <div className="flex flex-col gap-size1">
          <p className="text-sm font-medium tracking-tight">Select data</p>
          <DSelect
            options={[
              {
                label: 'Credits Used',
                value: 'credits_used'
              },
              {
                label: 'Unique Users',
                value: 'unique_users'
              },
              {
                label: 'Conversations',
                value: 'conversations'
              },
              {
                label: 'Messages',
                value: 'messages'
              }
            ]}
            value={exportType}
            onChange={(value) => setExportType(value)}
          />
        </div>
      </DModal>
    </div>
  )
}

export default DModalExportInsights;