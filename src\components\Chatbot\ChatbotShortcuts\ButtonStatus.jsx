import React from 'react';

import { Checkbox, Field, Label } from '@headlessui/react';

import NoZapIcon from '../../Global/Icons/NoZapIcon';
import ZapIcon from '../../Global/Icons/ZapIcon';

const ButtonStatus = ({ onChange, checked, disabled = false, status, className, size }) => {
  return (
    <Field disabled={disabled} className={`flex items-center gap-2 ${className}`}>
      <Checkbox
        as="div"
        checked={status === 'Active'}
        onChange={onChange}
        className="group flex justify-center items-center gap-size0 px-size1 py-size0 w-full rounded-full text-xs text-center text-orange-300 bg-orange-5 data-[checked]:bg-green-5 data-[checked]:text-green-300"
      >
        {status === 'Active' ? (
          <ZapIcon width={12} height={12} />
        ) : (
          <NoZapIcon width={12} height={12} />
        )}
        <Label>{status}</Label>
      </Checkbox>
    </Field>
  );
};

export default ButtonStatus;
