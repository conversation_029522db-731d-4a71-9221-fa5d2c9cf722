.avatar_dashboard_wrapper .avatar_dashboard-grid_content {
  max-height: calc(100vh - 230px);
}

.avatar_dashboard-grid_content {
  grid-template-columns: repeat(auto-fit, minmax(200px, 258px));
}

.avatar_dashboard_wrapper::-webkit-scrollbar {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  width: 8px;
}

.avatar_dashboard_wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.avatar_dashboard-create_btn {
  max-width: 352px;
  width: 100%;
  height: 100%;
  flex-grow: 1;
}
.avatar_dashboard-create_btn button {
  background-color: #fff;
}

@media (max-width: 640px) {
  .avatar_dashboard-grid_content {
    grid-template-columns: repeat(auto-fit, minmax(200px, auto));
  }
}
