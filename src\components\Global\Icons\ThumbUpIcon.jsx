import * as React from 'react';
const ThumbUpIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.707 3.614c.166-.373.536-.614.945-.614 1.161 0 2.103.941 2.103 2.103V7.4c0 .*************.15h2.277a2.45 2.45 0 0 1 2.421 2.823l-.7 4.55A2.45 2.45 0 0 1 13.482 17H4.755a1.8 1.8 0 0 1-1.8-1.8v-4.55a1.8 1.8 0 0 1 1.8-1.8h1.527a.15.15 0 0 0 .137-.09zM7.205 16h6.277a1.45 1.45 0 0 0 1.433-1.23l.7-4.55a1.45 1.45 0 0 0-1.433-1.67h-2.277a1.15 1.15 0 0 1-1.15-1.15V5.103c0-.61-.494-1.103-1.103-1.103a.03.03 0 0 0-.032.02L7.333 9.167a1.2 1.2 0 0 1-.128.22zm-1-6.15V16h-1.45a.8.8 0 0 1-.8-.8v-4.55a.8.8 0 0 1 .8-.8z"
      fill="currentColor"
    />
  </svg>
);
export default ThumbUpIcon;
