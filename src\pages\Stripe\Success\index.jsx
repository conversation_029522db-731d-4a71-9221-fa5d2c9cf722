import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Confetti from 'react-confetti';
import DButton from '@/components/Global/DButton';
import CheckIcon from '@/components/Global/Icons/CheckIcon';
import { useUserStore } from '@/stores/user/userStore';
import { trackSubscription } from '@/helpers/analytics';
import { getUserProfile } from '@/services/user.service';

const Success = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const urlParams = new URLSearchParams(window.location.search);
  const price = urlParams.get('price');
  const { user, auth } = useUserStore();

  const getUpdatedUser = async () => {
    try{
      const response = await getUserProfile();
      if(response.status === 200){
        useUserStore.getState().setUser(response.data);
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  }
  useEffect(() => {
    getUpdatedUser();

    // Track subscription event for Amplitude
    if (typeof window !== 'undefined') {
      const tier = urlParams.get('tier') || '';
      const period = urlParams.get('period') || 'monthly';
      const email = urlParams.get('email') || user.email || '';
      const sessionId = urlParams.get('session_id') || '';

      // Format date for next payment (1 month or 1 year from now)
      const currentDate = new Date();
      const nextPaymentDate = new Date(currentDate);
      if (period === 'yearly') {
        nextPaymentDate.setFullYear(nextPaymentDate.getFullYear() + 1);
      } else {
        nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1);
      }

      const formattedPaymentDate = `${nextPaymentDate.getMonth() + 1}/${nextPaymentDate.getDate()}/${nextPaymentDate.getFullYear()}`;
      const formattedSubscriptionDate = `${currentDate.getMonth() + 1}/${currentDate.getDate()}/${currentDate.getFullYear()}`;

      // Determine subscription method (direct, upgrade, downgrade)
      let subscriptionMethod = 'direct';

      // If user had a previous tier, determine if this is an upgrade or downgrade
      if (user.tier_type && user.tier_type !== tier) {
        const tierRanking = {
          'free': 0,
          'starter': 1,
          'advanced': 2,
          'professional': 3,
          'enterprise': 4
        };

        const prevTierRank = tierRanking[user.tier_type.toLowerCase()] || 0;
        const newTierRank = tierRanking[tier.toLowerCase()] || 0;

        if (newTierRank > prevTierRank) {
          subscriptionMethod = 'upgrade';
        } else if (newTierRank < prevTierRank) {
          subscriptionMethod = 'downgrade';
        }
      }

      // Get user ID from either user.id or auth.user_id
      const userId = user.id || auth?.user_id || '';

      // Track the subscription
      trackSubscription({
        subscription_id: sessionId,
        user_id: userId,
        email: email,
        tier_name: tier,
        billing_cycle: period,
        prev_tier_name: user.tier_type || '',
        prev_billing_cycle: user.billing_cycle || '',
        billing_price: price || 0,
        next_payment_date: formattedPaymentDate,
        payment_method: 'credit card',
        subscription_date: formattedSubscriptionDate,
        last_subscription_date: '',
        subscription_method: subscriptionMethod
      });
    }

    // Redirect after a delay
    setTimeout(() => {
      navigate('/');
    }, 5000);
  }, [navigate, urlParams, user, auth, price]);

  return (
    <>
      <Confetti width={window.innerWidth} height={window.innerHeight} />
      <div className="flex flex-col justify-center items-center min-h-screen bg-white text-black">
        <CheckIcon className="w-12 h-12 text-green-600" />
        <div className="text-center flex flex-col items-center space-y-4">
          <p className="text-2xl">${urlParams.get('price')} charge was successful, your subscription is now active.</p>
          <p className="text-sm text-gray-500">
            It may take up to 5 minutes for your account to reflect the changes
          </p>
          <DButton
            loading={loading}
            disableRipple
            className="!w-36"
            onClick={() => {
              setLoading(true);
              setTimeout(() => {
                setLoading(false);
                navigate('/');
              }, 1500);
            }}
            variant="contained"
          >
            Return to Dante AI
          </DButton>
          <p className="text-sm text-gray-500">
            <small>You'll be automatically redirected in a few seconds</small>
          </p>
        </div>
      </div>
    </>
  );
};

export default Success;
