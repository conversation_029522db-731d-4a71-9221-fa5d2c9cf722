import { useEffect, useState } from 'react';

import DButton from '@/components/Global/DButton';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import OptionsIcon from '@/components/Global/Icons/OptionsIcon';
import ZapIcon from '@/components/Global/Icons/ZapIcon';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import useDanteApi from '@/hooks/useDanteApi';
import * as userService from '@/services/user.service';
import LayoutMain from '@/layouts/LayoutMain';
import { DateTime } from 'luxon';
import CheckIcon from '@/components/Global/Icons/CheckIcon';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import findCardIcon from './listCards';
import { useUserStore } from '@/stores/user/userStore';
import { useNavigate } from 'react-router-dom';
import DAddCreditsModal from '@/components/DAddCreditsModal';
import DModalDowngrade from '@/components/DModalDowngrade';
import DAddSeatsModal from '@/components/DAddSeatsModal';
import EditIcon from '@/components/Global/Icons/EditIcon';
import DLoading from '@/components/DLoading';
import DBadge from '@/components/Global/DBadge';
import useLayoutStore from '@/stores/layout/layoutStore';

const CreditCardItem = ({
  brand,
  type,
  endNumber,
  expiresIn,
  isDefault = false,
}) => {
  const flagData = { Icon: findCardIcon(brand), name: brand };
  return (
    <div className="flex justify-between">
      <div className="flex gap-size1">
        <div className="cardicon flex items-center justify-center size-6 bg-[#000] rounded-size0 text-white">
          <flagData.Icon className="w-6" />
        </div>
        <div className="flex flex-col ">
          <div className="flex gap-size1 items-center">
            <p className="">{flagData.name}</p>
            <p className="text-grey-50">ending in {endNumber}</p>
            {isDefault && (
              <div className="text-2xs bg-grey-5 px-size1 text-grey-50 rounded-size0">
                Default
              </div>
            )}
          </div>
          <p className="text-xs text-grey-20">Expires in {expiresIn}</p>
        </div>
      </div>
      {/* <Menu>
        <MenuButton>
          <OptionsIcon />
        </MenuButton>
        <MenuItems
          transition
          anchor="bottom end"
          className=" origin-top-right rounded-size0 border border-grey-5  p-1  bg-grey-5 transition duration-100 ease-out [--anchor-gap:8px] focus:outline-none data-[closed]:scale-95 data-[closed]:opacity-0 text-xs"
        >
          {!isDefault && (
            <MenuItem>
              <button className=" group flex w-full items-center gap-2 rounded-lg py-1.5 px-3 data-[focus]:bg-white/10">
                Make default
              </button>
            </MenuItem>
          )}

          <MenuItem>
            <button className="group flex w-full text-negative-100  items-center gap-2 rounded-lg py-1.5 px-3 data-[focus]:bg-white/10">
              Remove
            </button>
          </MenuItem>
        </MenuItems>
      </Menu> */}
    </div>
  );
};

const MembershipPage = () => {
  const { user } = useUserStore((state) => state);
  const { setPlanModal } = useLayoutStore((state) => state);
  const navigate = useNavigate();

  const [openAddCreditsModal, setOpenAddCreditsModal] = useState(false);
  const [openCancelModal, setOpenCancelModal] = useState(false);
  const [openAddSeatsModal, setOpenAddSeatsModal] = useState(false);

  const [billingDetailsInfo, setBillingDetailsInfo] = useState({
    full_name: 'string',
    email: 'string',
    address: 'string',
    cards: [],
  });
  const [invoicesInfo, setInvoicesInfo] = useState(null);

  const { data: billingDetails, isLoading: isLoadingBillingDetails } =
    useDanteApi(userService.getBillingDetails);
  const { data: invoices, isLoading: isLoadingInvoices } = useDanteApi(
    userService.getInvoices
  );

  useEffect(() => {
    if (billingDetails) {
      setBillingDetailsInfo(billingDetails);
    }
  }, [billingDetails]);

  if (isLoadingBillingDetails || isLoadingInvoices) {
    return <DLoading show={true} />;
  }

  return (
    <LayoutMain title="Membership">
      <div className="3xl:max-w-[1200px] 3xl:mx-auto">
        <div className="flex flex-col gap-size5 w-full">
          <div className="flex flex-col  bg-grey-2 rounded-size5 p-size5 gap-size5">
            <header className="flex justify-between gap-size1 w-full md:items-center">
              <div className="flex flex-col md:flex-row gap-size1 items-baseline">
                <h1 className="test-lg md:text-xl font-medium">Current plan</h1>
                <span className="text-grey-50">
                  {user?.tier_key?.renew_date
                    ? `Renews ${DateTime.fromISO(
                        user?.tier_key?.renew_date
                      ).toLocaleString(DateTime.DATE_MED)}`
                    : ''}
                </span>
              </div>
              <div className="flex gap-size1">
                <DButton
                  variant={'dark'}
                  className="hidden md:flex"
                  onClick={() =>
                    setPlanModal({
                      isOpen: true,
                    })
                  }
                >
                  Compare plans
                </DButton>
                <Menu>
                  <MenuButton>
                    <OptionsIcon />
                  </MenuButton>
                  <MenuItems
                    transition
                    anchor="bottom end"
                    className=" origin-top-right rounded-size0 border border-grey-5 bg-white transition duration-100 ease-out [--anchor-gap:8px] focus:outline-none data-[closed]:scale-95 data-[closed]:opacity-0 text-xs"
                  >
                    <MenuItem className="bg-grey-5">
                      <button
                        className="dbutton md:hidden group flex w-full items-center gap-2 py-1.5 px-3 data-[focus]:bg-white/10"
                        onClick={() =>
                          setPlanModal({
                            isOpen: true,
                          })
                        }
                      >
                        Compare plans
                      </button>
                    </MenuItem>
                    <MenuItem className="bg-grey-5">
                      <button
                        className="dbutton group flex w-full items-center gap-2 py-1.5 px-3 data-[focus]:bg-white/10"
                        onClick={() => setOpenAddCreditsModal(true)}
                      >
                        <ZapIcon />
                        Purchase credits
                      </button>
                    </MenuItem>
                    <MenuItem className="bg-grey-5">
                      <button
                        className="dbutton group flex w-full text-negative-100  items-center gap-1.5 py-1.5 px-3 data-[focus]:bg-white/10"
                        onClick={() => setOpenCancelModal(true)}
                      >
                        <DeleteIcon className="w-[18px] h-[18px]" />
                        Cancel subscription
                      </button>
                    </MenuItem>
                  </MenuItems>
                </Menu>
              </div>
            </header>
            <div className="w-full  h-px bg-grey-5"></div>
            <div className="flex flex-col md:flex-row">
              <div className="flex flex-col w-full w-1/2 gap-size5">
                <div className="flex flex-col gap-size1">
                  <div className="flex items-center gap-size1">
                    <p className="text-grey-75 capitalize">{user?.tier_type}</p>
                    {user?.tier_on_trial && (
                      <DBadge type="SUCCESS" label="On trial" />
                    )}
                  </div>
                  <p className="">
                    <span className="text-black font-medium text-3xl">
                      ${user?.tier_key?.price_in_dollars || 0}
                    </span>
                    <span className="text-grey-20 text-2xl">
                      {' '}
                      {user?.tier_key?.period === 'yearly' ? '/year' : '/month'}
                    </span>
                  </p>
                </div>
                {user?.tier_key?.period !== 'yearly' && (
                  <div className="flex gap-size2 items-center">
                    <DButton
                      variant="green"
                      size="md"
                      onClick={() => {
                        if (user?.tier_key?.upgrade_to_annual_url) {
                          navigate(user?.tier_key?.upgrade_to_annual_url);
                        } else {
                          setPlanModal({
                            isOpen: true,
                          });
                        }
                      }}
                    >
                      Upgrade to annual
                    </DButton>
                    <span className="text-orange-300 text-sm">Save 20%</span>
                  </div>
                )}
              </div>
              {user?.team_key && (
                <div className="flex flex-col w-full w-1/2 gap-size5">
                  <div className="flex flex-col gap-size1">
                    <p className="text-grey-75">Teams members</p>
                    <p className="text-grey-50 text-xl">
                      <span className="text-black">
                        {Math.round(
                          user?.team_key?.total_seats - user?.team_key?.used_seats
                        )}
                      </span>
                      /{user?.team_key?.total_seats || 1} free seats left
                    </p>
                  </div>
                  <div className="flex gap-size2 items-center">
                    <DButton
                      variant="dark"
                      onClick={() => setOpenAddSeatsModal(true)}
                    >
                      Add more seats
                    </DButton>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col md:flex-row gap-size1">
            <div className="flex flex-col border border-grey-5 rounded-size5 gap-size5 p-size5 w-full md:w-1/2">
              <header className="flex justify-between gap-size1 w-full md:items-center">
                <div className="flex flex-col md:flex-row gap-size1">
                  <h1 className="test-lg md:text-xl font-medium">
                    Billing details
                  </h1>
                </div>
                {/* <div className="flex gap-size1">
                  <DButton variant={'grey'} className="">
                    Edit
                  </DButton>
                </div> */}
              </header>
              <div className="w-full  h-px bg-grey-5"></div>
              <p className="text-lg text-medium">
                {billingDetailsInfo.full_name}
              </p>
              <p className="text-grey-50 ">{billingDetailsInfo.email}</p>
              <p className="text-lg text-medium">{billingDetailsInfo.address}</p>
            </div>
            <div className="flex flex-col border border-grey-5 rounded-size5 gap-size5 p-size5 w-full md:w-1/2">
              <header className="flex justify-between gap-size1 w-full md:items-center">
                <div className="flex flex-col md:flex-row gap-size1">
                  <h1 className="test-lg md:text-xl font-medium">Card details</h1>
                </div>
                <div className="flex gap-size1"></div>
              </header>
              <div className="w-full  h-px bg-grey-5"></div>
              {billingDetailsInfo.cards.length > 0 ? (
                billingDetailsInfo.cards
                  .filter((card) => card.card_type?.toLowerCase() !== 'unknown')
                  .map((card) => (
                    <CreditCardItem
                      key={card.last_four_digits}
                      brand={card.card_type}
                      endNumber={card.last_four_digits}
                      expiresIn={card.expire_date}
                      isDefault={card.default}
                    />
                  ))
              ) : (
                <DButton
                  variant="grey"
                  onClick={() =>
                    window.open(
                      'https://billing.stripe.com/p/login/9AQ3cQ88NaZ2f8k144',
                      '_blank'
                    )
                  }
                >
                  <EditIcon /> Manage subscription
                </DButton>
              )}
            </div>
          </div>
          {/* <div className="flex flex-col border border-grey-5 rounded-size5 gap-size5 p-size5 w-full "></div> */}
        </div>
        <div className="border border-grey-5 rounded-size5 p-size5 mt-size5">
          <h1 className="text-lg font-medium">Invoices</h1>
          <div className="w-full overflow-x-auto">
            <table className="border-collapse table-auto w-full">
              <thead className="bg-grey-5 text-left rounded-size2">
                <tr>
                  <th className="pl-size1 py-size2">Date</th>
                  <th className="pl-size1 py-size2">Amount</th>
                  <th className="pl-size1 py-size2">Status</th>
                  <th className="pl-size1 py-size2"></th>
                </tr>
              </thead>
              <tbody>
                {invoices?.results?.map((invoice) => (
                  <tr key={invoice.invoice_id}>
                    <td className="pl-size1 py-size2">
                      {DateTime.fromISO(invoice.date).toLocaleString()}
                    </td>
                    <td className="pl-size1 py-size2">
                      ${invoice.amount.toFixed(2)}
                    </td>
                    <td className="pl-size1 py-size2 flex items-center gap-size1">
                      {invoice.status === 'paid' ? (
                        <div className="rounded-full size-7 bg-green-5 text-green-100 flex items-center justify-center">
                          <CheckIcon />
                        </div>
                      ) : (
                        <div className="rounded-full size-7 bg-negative-5 text-negative-100 flex items-center justify-center">
                          <CloseIcon />
                        </div>
                      )}

                      {invoice.status === 'paid' ? (
                        <span className="">Paid</span>
                      ) : (
                        <span className="">Unpaid</span>
                      )}
                    </td>
                    <td className="pl-size1 py-size2">
                      <Menu>
                        <MenuButton>
                          <OptionsIcon />
                        </MenuButton>
                        <MenuItems
                          transition
                          anchor="bottom end"
                          className=" origin-top-right rounded-size0 border border-grey-5 bg-white transition duration-100 ease-out [--anchor-gap:8px] focus:outline-none data-[closed]:scale-95 data-[closed]:opacity-0 text-xs"
                        >
                          <MenuItem>
                            <button
                              className="group flex w-full items-center gap-2 rounded-size0 py-1.5 px-3 data-[focus]:bg-white/10 bg-grey-5"
                              onClick={() => {
                                window.open(invoice.download_url, '_blank');
                              }}
                            >
                              Download
                            </button>
                          </MenuItem>
                        </MenuItems>
                      </Menu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <DAddCreditsModal
        open={openAddCreditsModal}
        onClose={() => setOpenAddCreditsModal(false)}
      />
      <DModalDowngrade
        isOpen={openCancelModal}
        onClose={() => setOpenCancelModal(false)}
        operation="cancel"
      />
      <DAddSeatsModal
        open={openAddSeatsModal}
        onClose={() => setOpenAddSeatsModal(false)}
      />
    </LayoutMain>
  );
};

export default MembershipPage;
