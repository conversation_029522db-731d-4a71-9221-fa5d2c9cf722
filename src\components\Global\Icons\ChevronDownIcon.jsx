const ChevronDownIcon = (props) => {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 8 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.35843 1.67137L4.30061 4.90907C4.26158 4.94945 4.21445 4.98198 4.16276 5.00395C4.11091 5.02599 4.05514 5.03735 3.99879 5.03735C3.94244 5.03735 3.88667 5.02599 3.83482 5.00395C3.78313 4.98198 3.73639 4.94986 3.69737 4.90948L0.642416 1.67482L0.641646 1.67402C0.56461 1.59395 0.522515 1.48657 0.524611 1.37547C0.526706 1.26442 0.572793 1.15874 0.652747 1.08163C0.732817 1.0046 0.840196 0.962504 0.951288 0.9646C1.06217 0.966692 1.16769 1.01263 1.24476 1.09235L3.99879 4.01564L6.75334 1.09184L6.75486 1.09016C6.79255 1.04861 6.8382 1.01504 6.8891 0.991457C6.94 0.96787 6.99512 0.95474 7.05119 0.952846C7.10726 0.950951 7.16314 0.960331 7.21552 0.980428C7.26789 1.00053 7.3157 1.03093 7.35611 1.06985C7.39652 1.10876 7.42871 1.15539 7.45077 1.20697C7.47283 1.25855 7.48431 1.31404 7.48453 1.37014C7.48475 1.42624 7.47371 1.48181 7.45206 1.53357C7.43041 1.58532 7.39853 1.63214 7.35843 1.67137Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default ChevronDownIcon;
