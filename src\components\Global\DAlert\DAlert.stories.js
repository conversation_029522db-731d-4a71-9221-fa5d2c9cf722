import { fn } from '@storybook/test';

import D<PERSON>lert from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Global/DAlert',
  component: DAlert,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    type: {
      options: ['attentive', 'input', 'explainer'],
      control: { type: 'radio' }
    },
    state: {
      options: ['default', 'positive', 'negative', 'alert'],
      control: { type: 'radio' }
    }
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {
    state: 'default',
    type: 'explainer',
    children:
      'Your AI Chatbot is now fully interactive. Feel free to test its quality before deciding to move forward or retrain it.'
  }
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {}
};
