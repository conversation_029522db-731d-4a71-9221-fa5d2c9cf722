import React from 'react';
import DSwitch from '@/components/Global/DSwitch';
import DButton from '@/components/Global/DButton';
import DTransition from '../Global/DTransition';
import CheckmarkIcon from '../Global/Icons/CheckmarkIcon';
import CloseIcon from '../Global/Icons/CloseIcon';
import AddOnRow from '../AddOnRow';
import { useUserStore } from '@/stores/user/userStore';
import { EMAILS } from '@/constants';

const MobilePlans = ({
  plans,
  onUpgrade,
  onDowngrade,
  period,
  setPeriod,
  tiers,
  selectedPlan,
  setSelectedPlan,
  selectedMessages,
  setSelectedMessages,
}) => {
  const user = useUserStore((state) => state.user);
  return (
    <>
      <div className="flex flex-col gap-size1 bg-purple-5 border border-gray-2 rounded-size1 w-full">
        <div className="flex gap-size1 px-size3 py-size2 border-b border-gray-2 items-center justify-start">
          <DSwitch
            checked={period === 'yearly'}
            onChange={(value) => setPeriod(value ? 'yearly' : 'monthly')}
            label="Annual"
          />
          <div className="flex p-size1 items-center bg-purple-10 text-purple-300 rounded-size1">
            <p className="text-sm tracking-tight">2 Months Free</p>
          </div>
        </div>
        {tiers?.map((tier, tierIndex) => (
          <button
            key={tierIndex}
            onClick={() => setSelectedPlan(tier.name)}
            className="dbutton"
          >
            <div className="flex flex-col gap-size2 py-size2 px-size3 border-b border-gray-2 items-start w-full">
              <p className="text-base font-medium tracking-tight">
                <span className="text-grey-50">{tier.name} </span>
                {tier.name !== 'Enterprise' && (
                  <>
                    ${tier.price}{' '}
                    <span className="text-grey-20">
                      {period === 'yearly' ? '/year' : '/month'}
                    </span>
                  </>
                )}
              </p>
              <div className="w-full">
                {user?.tier_rank === tier.rank && !user?.tier_on_trial && (
                  <DButton
                    variant="light"
                    size="sm"
                    fullWidth
                    className="!mt-auto"
                  >
                    Current Plan
                  </DButton>
                )}
                {tier?.name === 'Enterprise' && !user?.tier_on_trial && (
                  <DButton
                    variant="dark"
                    size="sm"
                    fullWidth
                    onClick={() => {
                      window.open(`mailto:${EMAILS.SALES}`, '_blank');
                    }}
                  >
                    Contact us
                  </DButton>
                )}
                {user?.tier_rank > tier.rank &&
                  tier.name !== 'Enterprise' &&
                  !user?.tier_on_trial && (
                    <DButton
                      variant="grey"
                      size="sm"
                      fullWidth
                      onClick={() => onDowngrade(tier)}
                    >
                      Downgrade
                    </DButton>
                  )}
                {(user?.tier_rank || 0) < tier.rank &&
                  tier.name !== 'Enterprise' &&
                  !user?.tier_on_trial && (
                    <DButton
                      variant="green"
                      size="sm"
                      fullWidth
                      onClick={() => onUpgrade(tier.id, tier.name)}
                    >
                      Upgrade
                    </DButton>
                  )}
                {tier?.name !== 'Enterprise' && user?.tier_on_trial && (
                  <DButton
                    variant="green"
                    size="sm"
                    fullWidth
                    onClick={() => onUpgrade(tier.id, tier.name)}
                  >
                    {user?.tier_rank === tier.rank && user?.tier_on_trial
                      ? 'Activate Now'
                      : 'Select Plan'}{' '}
                    {user?.tier_rank === tier.rank && user?.tier_on_trial && (
                      <span className="text-[10px]">(Current Trial)</span>
                    )}
                  </DButton>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>
      <div>
        {plans?.map((category, categoryIndex) =>
          category?.type && category.type === 'addon' ? (
            <React.Fragment key={categoryIndex}>
              <div className="bg-black rounded-size2 p-size3 text-white">
                <AddOnRow
                  category={category}
                  selectedMessages={selectedMessages}
                  setSelectedMessages={setSelectedMessages}
                  addons={plans.filter((plan) => plan.type === 'addon')}
                />
              </div>
            </React.Fragment>
          ) : (
            <React.Fragment key={categoryIndex}>
              {/* Category Header */}
              <tr>
                <td
                  colSpan="5"
                  className="text-lg font-medium text-left px-1 py-3"
                >
                  {category.name}
                </td>
              </tr>

              {/* Features */}
              {category?.features?.map((feature, featureIndex) => (
                <tr key={featureIndex} className="hover:bg-grey-2">
                  <td className="border-b border-grey-5 px-1 py-2 text-left text-grey-50 text-sm tracking-tight w-full">
                    {feature.title}
                  </td>
                  {feature.value
                    .filter((tier) => tier.tier === selectedPlan)
                    .map((tier, tierIndex) => {
                      return (
                        <td
                          key={tierIndex}
                          className="relative px-1 py-3 text-center w-full min-w-[120px]"
                        >
                          <div className="flex justify-center items-center gap-size1 w-full">
                            {typeof tier.value === 'boolean' ? (
                              tier.value ? (
                                <CheckmarkIcon />
                              ) : (
                                <CloseIcon className="text-grey-20" />
                              )
                            ) : (
                              <p className="text-grey-50 text-sm tracking-tight">
                                {tier.value}
                              </p>
                            )}
                          </div>
                          <div className="absolute bottom-0 left-1/2 w-[92%] border-b border-grey-5 transform -translate-x-1/2"></div>
                        </td>
                      );
                    })}
                </tr>
              ))}
            </React.Fragment>
          )
        )}
      </div>
    </>
  );
};

export default MobilePlans;
