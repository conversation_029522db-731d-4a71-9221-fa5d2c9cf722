import DLoading from '@/components/DLoading';
import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import * as plansService from '@/services/plans.service';


const Subscribe = () => {
  const [urlParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    if (urlParams.get('tier_id')) {
      getCheckoutSession(urlParams.get('tier_id'), urlParams.get('campaign'));
    }
    localStorage.removeItem('save_url');
  }, [urlParams]);

  const getCheckoutSession = async (id, campaign) => {
    try {
      const { data } = await plansService.getCheckoutSession(id, campaign);
      window.location.replace(data.checkout_session.url);
    } catch (error) {
      navigate('/');
    }
  };

  return <DLoading show={true} />;
};
export default Subscribe;
