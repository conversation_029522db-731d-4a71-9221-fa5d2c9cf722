import { useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import WordpressColorIcon from '@/components/Global/Icons/WordpressColorIcon';
import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import IntegrationApps from '@/components/IntegrationApps';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import { generateWordpressApiKey, deleteWordpressApiKey, getWordpressApiKey } from '@/services/integration.service';
import useDanteApi from '@/hooks/useDanteApi';

const WordPressIntegration = () => {
    const navigate = useNavigate();
    const [apiKey, setApiKey] = useState('');
    const [isGenerated, setIsGenerated] = useState(false);
    const [isCopying, setIsCopying] = useState(false);
    const { data: wordpressApiKey } = useDanteApi(getWordpressApiKey)

    const generateApiKey = async () => {
        try {
            const response = await generateWordpressApiKey();
            setApiKey(response.data.secret_key);
            setIsGenerated(true);
        } catch (error) {
            console.error('Error generating API key:', error);
        }
    };

    const copyApiKey = () => {
        navigator.clipboard.writeText(apiKey);
        setIsCopying(true);
        setTimeout(() => {
            setIsCopying(false);
        }, 2000);
    };

    const deleteApiKey = async () => {
        try {
            await deleteWordpressApiKey();
            setApiKey('');
            setIsGenerated(false);
        } catch (error) {
            console.error('Error deleting API key:', error);
        }
    };

    useEffect(() => {
        if (wordpressApiKey && wordpressApiKey.secret_key) {
            setApiKey(wordpressApiKey.secret_key);
            setIsGenerated(true);
        }
    }, [wordpressApiKey]);

    return (
        <LayoutRightSidebar
          RightSidebar={() => {
            return <IntegrationApps row={false} />;
          }}
        >
          {() => {
            return (
              <div className="w-full h-[1px] grow overflow-y-auto bg-white rounded-size1 p-size5 flex flex-col gap-size5">
                <div className="flex text-black text-base">
                  <a className="text-grey-50" onClick={() => navigate(-1)}>
                    Integrations
                  </a>{' '}
                  /{' '}
                  <a href="" className="text-black">
                    WordPress
                  </a>
                </div>
                <div className="w-full h-px bg-grey-5"></div>
                <div className="flex gap-size5">
                  <div className="flex flex-col py-size4 px-size7 rounded-size3 border border-grey-5 items-center justify-center size-24">
                    <WordpressColorIcon />
                  </div>
                  <div className="flex flex-col gap-size3">
                    <p className="text-xl font-medium tracking-tight">WordPress</p>
                    <p className="text-sm text-grey-50">
                      Connect your AI Chatbot instantly to WordPress using our Dante plugin. 
                      Seamlessly integrate AI-powered chat capabilities into your WordPress 
                      website to enhance visitor engagement and provide automated support.
                    </p>
                  </div>
                </div>
                <div className="flex gap-size3 items-end w-full">
                    <div className="flex flex-col gap-size1 w-full">
                        <p className="text-base font-medium tracking-tight">API Key</p>
                        <DInput
                            placeholder="Your API key will appear here"
                            value={apiKey}
                            disabled={true}
                        />
                    </div>
                    <div className="flex gap-size1">
                    {!isGenerated ? (
                        <DButton variant="dark" onClick={generateApiKey} className="!h-[44px] !min-w-[208px]">
                            Generate API Key
                        </DButton>
                    ) : (
                        <div className="flex gap-size1">
                            <DButton variant="dark" onClick={copyApiKey} className="!h-[44px] !min-w-[100px]">
                                <CopyIcon />
                                {isCopying ? 'Copied' : 'Copy'}
                            </DButton>
                            <DButton variant="outlined" onClick={deleteApiKey} className="!h-[44px] !min-w-[100px]">
                                <DeleteIcon />
                                Delete
                            </DButton>
                        </div>
                    )}
                    </div>
                </div>
              </div>
            );
          }}
        </LayoutRightSidebar>
      );
};

export default WordPressIntegration;
