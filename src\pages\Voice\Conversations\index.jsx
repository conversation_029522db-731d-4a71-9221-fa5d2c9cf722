import DButton from '@/components/Global/DButton';
import DTable from '@/components/Global/DTable';
import UpRightIcon from '@/components/Global/Icons/UpRightIcon';
import useDanteApi from '@/hooks/useDanteApi';
import { getVoiceConversations } from '@/services/voice.service';
import { useEffect, useState, useRef } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { DateTime } from 'luxon';
import DBadge from '@/components/Global/DBadge';
import { STATUS } from '@/constants';
import DSwitch from '@/components/Global/DSwitch';
import useLayoutStore from '@/stores/layout/layoutStore';
import DLoading from '@/components/DLoading';

const VoiceConversations = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();
    const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
    const switchRef = useRef(null);

    // Check if we're coming from the onboarding popup
    const searchParams = new URLSearchParams(location.search);
    const fromOnboarding = searchParams.get('source') === 'onboarding-popup';

    const { data: conversations, isLoading, error, refetch } = useDanteApi(getVoiceConversations, [], { }, params.id);

    const [tableData, setTableData] = useState([]);
    const [liveConversations, setLiveConversations] = useState(fromOnboarding);

    const tableColumns = [
        {
            label: 'Caller Number',
            key: 'caller_number',
            showName: true,
            minWidth: 'min-w-10',
        },
        {
            label: 'Date started',
            key: 'date_started',
            showName: true,
            minWidth: 'min-w-10',
        },
        {
            label: 'Date ended',
            key: 'date_ended',
            showName: true,
            minWidth: 'min-w-10',
        },
        {
            label: 'Status',
            key: 'status',
            showName: true,
            minWidth: 'min-w-10',
        },
        {
            label: 'Duration',
            key: 'duration',
            showName: true,
            minWidth: 'min-w-10',
        },

        {
            label: 'Actions',
            key: 'actions',
            showName: true,
            minWidth: 'min-w-10',
        }
    ]

    useEffect(() => {
        if (conversations?.results?.length > 0) {
            setTableData(conversations?.results?.map((item) => ({
                caller_number: item.caller_number,
                date_started: item.call_started_at ? DateTime.fromISO(item.call_started_at).toLocaleString(DateTime.DATETIME_SHORT) : '',
                date_ended: item.call_ended_at ? DateTime.fromISO(item.call_ended_at).toLocaleString(DateTime.DATETIME_SHORT) : '',
                status: <DBadge
                    label={item.status.replace(/_/g, ' ').toUpperCase()}
                    type={item.status === 'done' ? STATUS.SUCCESS : STATUS.WORKING}
                    showIcon={false}
                />,
                duration: item?.duration ? `${Math.floor(item.duration / 60)}m ${item.duration % 60}s` : '0m 0s',
                actions: <DButton variant="outlined" size="sm" onClick={() => navigate(`${item.id}`)}>
                    View
                </DButton>,
            })));
        }
    }, [conversations]);

    useEffect(() => {
        if (liveConversations) {
            const interval = setInterval(() => {
                refetch();
            }, 3000);
            return () => clearInterval(interval);
        }
    }, [liveConversations]);

    useEffect(() => {
        setSidebarOpen(false);
    }, []);

    // Add glow effect if coming from onboarding
    useEffect(() => {
        if (fromOnboarding && switchRef.current) {
            // Clear the source parameter from the URL
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('source');
            window.history.replaceState({}, '', newUrl.toString());
        }
    }, [fromOnboarding]);

    // if(isLoading) {
    //     return <DLoading show={true} />
    // }

    return (
        <div className="flex flex-col gap-size5 bg-white rounded-size1 p-size5 h-[1px] grow overflow-y-auto no-scrollbar">
            <div className="flex items-center justify-end w-full">
                {/* <h1 className="text-xl font-medium">AI Voice Conversations</h1>
                 */}
                 <div
                    ref={switchRef}
                    className={`self-end ${fromOnboarding ? 'animate-pulse shadow-md shadow-purple-20 rounded-lg p-2' : ''}`}
                 >
                <DSwitch
                    label="Enable live conversations"
                    checked={liveConversations}
                    onChange={(checked) => setLiveConversations(checked)}
                />
                </div>
            </div>
            <div className="flex flex-col gap-size1 w-full h-full">
                {tableData && tableData.length > 0 ? (
                    <DTable
                        columns={tableColumns}
                        data={tableData}
                    />
                ) : (
                    <div className="flex flex-col items-center justify-center w-full h-full py-size5">
                        <p className="text-lg text-grey-50">No conversations found</p>
                    </div>
                )}
            </div>
        </div>
    )
}

export default VoiceConversations;