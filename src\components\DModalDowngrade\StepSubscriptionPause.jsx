import { useUserStore } from '@/stores/user/userStore';
import { downgradeStepsEnum } from './stepDowngrade';
import { useEffect, useState } from 'react';
import DButton from '../Global/DButton';
import DSelect from '../Global/DSelect';
import PauseIcon from '../Global/Icons/PauseIcon';

const StepSubscriptionPause = ({
  handleNextStep,
  handleClose,
  setTitle,
}) => {
  const { user } = useUserStore();
  const [pauseDuration, setPauseDuration] = useState('1 month');

  const handlePauseSubscription = () => {
    // In a real implementation, this would trigger the subscription pause
    handleClose();
  };

  const handleDecline = () => {
    handleNextStep(downgradeStepsEnum.TEAM);
  };

  useEffect(() => {
    setTitle('');
  }, []);

  // Calculate renewal and pause dates
  const startDate = new Date();
  const endDate = new Date(startDate);

  if (pauseDuration === '1 month') {
    endDate.setMonth(startDate.getMonth() + 1);
  } else if (pauseDuration === '2 months') {
    endDate.setMonth(startDate.getMonth() + 2);
  }

  const formatDate = (date) => {
    return `${date.getDate()} ${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
  };

  return (
    <div className="flex flex-col gap-size5 relative">
      {/* Header with title and close button */}
      <div className="absolute top-[-38px] left-0 right-0 -mt-14 bg-grey-2 rounded-t-size1 border border-grey-5 border-b-0 flex items-center py-size1 px-size3">
        <div className="flex items-center gap-size2">
          <div className="bg-purple-5 rounded-full p-[6px] flex items-center justify-center">
            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-purple-200">
              <PauseIcon className="w-3 h-3 text-white" />
            </div>
          </div>
          <span className="font-medium text-sm">Subscription Pause</span>
        </div>
        <button
          onClick={handleClose}
          className="ml-auto text-grey-50 hover:text-grey-75 text-xl font-bold"
          aria-label="Close"
        >
          ×
        </button>
      </div>

      {/* Main content */}
      <div className="flex flex-col gap-size3">
        <div className="flex flex-col gap-size1">
          <h1 className="text-xl font-bold">Keep your data and templates!</h1>
          <p className="text-grey-50 text-xs leading-4 font-medium">
            We can pause your account for up to two months. This will store all your projects, templates, and data as usual. Without paying anything.
          </p>
        </div>

        {/* Duration selector */}
        <DSelect
          options={[
            { value: '1 month', label: '1 month' },
            { value: '2 months', label: '2 months' },
          ]}
          value={pauseDuration}
          onChange={(value) => setPauseDuration(value)}
          className="text-sm"
        />

        {/* Pause button */}
        <DButton
          variant="dark"
          size="md"
          className="text-sm h-10"
          onClick={handlePauseSubscription}
          fullWidth
        >
          Pause Subscription
        </DButton>

        {/* Date information */}
        <div className="text-xs text-grey-50 pt-size2 border-t border-grey-5 leading-4">
          <p className="mb-size1">Your subscription will renew on {formatDate(endDate)}</p>
          <p>Your subscription will be paused from {formatDate(startDate)} until {formatDate(endDate)}</p>
        </div>

        {/* Bottom buttons */}
        <div className="flex gap-size3 pt-size2">
          <DButton
            variant="outlined"
            size="md"
            className="text-sm h-10"
            onClick={handleClose}
            fullWidth
          >
            Back
          </DButton>
          <DButton
            variant="dark"
            size="md"
            className="text-sm h-10"
            onClick={handleDecline}
            fullWidth
          >
            Decline Offer
          </DButton>
        </div>
      </div>
    </div>
  );
};

export default StepSubscriptionPause;
