
import { fn } from '@storybook/test';

import DInputEditable from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Global/DInputEditable',
  component: DInputEditable,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    value: {
      default: '',
      control: { type: 'text' }
    },
    disabled: {
      default: false,
      control: { type: 'boolean' }
    },
    size: {
      options: ['sm', 'xl'],
      control: { type: 'radio' }
    }
  },
  args: {
    value: '',
    size: 'sm',
    disabled: false
  }
};

export const Draggable = {
  args: {
    value: 'Default',
    placeholder: 'Default',
    disabled: false,
    is_draggable: true,
    is_editable: false,
    is_deletable: false
  }
};

export const Editable = {
  args: {
    value: 'Default',
    placeholder: 'Default',
    disabled: false,
    is_draggable: false,
    is_editable: true,
    is_deletable: false
  }
};

export const Deletable = {
  args: {
    value: 'Default',
    placeholder: 'Default',
    disabled: false,
    is_draggable: false,
    is_editable: false,
    is_deletable: true
  }
};

export const All = {
  args: {
    value: 'Default',
    placeholder: 'Default',
    disabled: false,
    is_draggable: true,
    is_editable: true,
    is_deletable: true
  }
};
