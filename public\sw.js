// Global scope for the service worker
const NOTIFICATION_AUDIO_URL = 'https://dante-public-files.lon1.cdn.digitaloceanspaces.com/notification_sound.mp3';

self.addEventListener('install', (event) => {
  event.waitUntil(
    // Cache the notification sound for offline use
    caches.open('notification-assets').then(cache => {
      return cache.add(NOTIFICATION_AUDIO_URL);
    }).catch(error => {
      console.error('Error caching notification sound:', error);
    })
  );
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  // Take control of all pages immediately
  event.waitUntil(
    Promise.all([
      self.clients.claim(),
      // Clear old caches if needed
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.filter(cacheName => {
            return cacheName !== 'notification-assets';
          }).map(cacheName => {
            return caches.delete(cacheName);
          })
        );
      })
    ])
  );
});

self.addEventListener('push', (event) => {
  const options = {
    body: event.data ? event.data.text() : 'Notification',
    icon: 'https://chat.dante-ai.com/btn-embed-dark.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1,
      soundUrl: NOTIFICATION_AUDIO_URL
    },
    requireInteraction: false, // Keep notification visible until user interacts
    tag: 'notification-' + Date.now() // Unique tag for each notification
  };

  event.waitUntil(
    Promise.all([
      self.registration.showNotification('Notification', options),
      // Play notification sound using the audio API
      playNotificationSound()
    ])
  );
});

self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  // Focus on existing window or open a new one
  event.waitUntil(
    self.clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then(clientList => {
        for (let client of clientList) {
          if (client.url.includes('/human-handover') && 'focus' in client) {
            return client.focus();
          }
        }
        return self.clients.openWindow('/human-handover');
      })
  );
});

// Helper function to play notification sound
async function playNotificationSound() {
  try {
    const cache = await caches.open('notification-assets');
    const audioResponse = await cache.match(NOTIFICATION_AUDIO_URL);
    if (audioResponse) {
      const blob = await audioResponse.blob();
      const audioUrl = URL.createObjectURL(blob);
      // Send message to all clients to play the sound
      const clients = await self.clients.matchAll();
      clients.forEach(client => {
        client.postMessage({
          type: 'PLAY_NOTIFICATION_SOUND',
          audioUrl: audioUrl,
          timestamp: Date.now() // Add timestamp to retry later if needed
        });
      });
    } else {
      const clients = await self.clients.matchAll();
      clients.forEach(client => {
        client.postMessage({
          type: 'PLAY_NOTIFICATION_SOUND',
          audioUrl: NOTIFICATION_AUDIO_URL,
          timestamp: Date.now()
        });
      });
    }
  } catch (error) {
    console.error('Error playing notification sound:', error);
  }
}