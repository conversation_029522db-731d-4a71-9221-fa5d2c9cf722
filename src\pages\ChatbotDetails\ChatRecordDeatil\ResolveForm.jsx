import { useState } from 'react';

import DAlert from '@/components/Global/DAlert';
import DButton from '@/components/Global/DButton';
import DTextArea from '@/components/Global/DInput/DTextArea';
import DModal from '@/components/Global/DModal';

const ResolveForm = ({ open, onClose }) => {
  const [answer, setAnswer] = useState('');

  return (
    <DModal title="Resolve" isOpen={open} onClose={onClose}>
      <div className="flex flex-col gap-size5">
        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Flagged message</p>
          <div className="flex flex-col py-size3 px-size1 border border-grey-5 rounded-size2">
            <p className="text-sm tracking-tight border border-negative-2 rounded-size1 p-size2 bg-negative-5  w-max">
              please give me more?
            </p>
          </div>
        </div>
        <div className="flex flex-col gap-size1">
          <div className="flex flex-col gap-siz0">
            <p className="text-base font-medium tracking-tight">Provide answer</p>
            <span className="text-xs tracking-tight text-grey-50">
              Give a direct answer to question asked or explain how AI Chatbot should act when asked
              same or similar question.
            </span>
          </div>
          <DTextArea
            placeholder="Type your answer here..."
            className="border border-grey-5 rounded-size2"
            value={answer}
            onChange={(e) => setAnswer(e.target.value)}
          />
          <DAlert state="info">
            <p className="text-sm tracking-tight">
              Provided information will be added to AI Chatbot’s knowledge library
            </p>
          </DAlert>
        </div>
        <DButton variant="dark">Update Knowladge</DButton>
      </div>
    </DModal>
  );
};

export default ResolveForm;
