import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const initialCurrentConversation = {
  id: null,
  type: null
};

const initialState = {
  currentConversation: { ...initialCurrentConversation },
  takeoverId: null,
};

export const useConversationStore = create(
  persist(
    (set) => ({
      ...initialState,
      setCurrentConversation: (conversation) => set({ currentConversation: conversation }),
      setTakeoverId: (id) => set({ takeoverId: id }),
      resetCurrentConversation: () =>
        set({ currentConversation: { ...initialCurrentConversation } }),
      reset: () => set({ ...initialState })
    }),
    {
      name: 'conversation-store',
      storage: createJSONStorage(() => localStorage)
    }
  )
);
