import React, { useState } from 'react';
import ExpandIcon from '../Global/Icons/ExpandIcon';
import clsx from 'clsx';

const DFilterTable = ({ columns, data }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  const filteredData = data?.filter((row) => {
    if (!searchTerm) return true;

    return columns.some((col) => {
      const cellValue = row[col.key] ? row[col.key].toString() : '';
      return cellValue.toLowerCase().includes(searchTerm.toLowerCase());
    });
  });

  const sortedData = [...filteredData].sort((a, b) => {
    const { key, direction } = sortConfig;
    if (!key) return 0;

    const aVal = a[key] ? a[key].toString() : '';
    const bVal = b[key] ? b[key].toString() : '';

    if (direction === 'asc') return aVal.localeCompare(bVal);
    return bVal.localeCompare(aVal);
  });

  const handleSort = (columnKey) => {
    if (sortConfig.key === columnKey) {
      setSortConfig((prev) => ({
        key: columnKey,
        direction: prev.direction === 'asc' ? 'desc' : 'asc',
      }));
    } else {
      setSortConfig({ key: columnKey, direction: 'asc' });
    }
  };

  return (
    <div className="overflow-y-auto h-full">
      <table className="min-w-full border-collapse overflow-auto">
        <thead className="bg-white border-b border-grey-5 p-size1 sticky top-0 z-10">
          <tr>
            {columns.map((col) => (
              <th
                key={col.key}
                className={clsx(
                  'border-b border-grey-5 p-2 cursor-pointer',
                  'text-sm font-medium text-grey-50',
                  'px-4 py-2',
                  col.hideHeader ? 'hidden sm:table-cell' : ''
                )}
              >
                <div
                  className={clsx(
                    'flex items-center gap-2',
                    col.hideHeader ? 'hidden sm:flex' : 'flex'
                  )}
                >
                  <p>{col.label}</p>
                  {col.sortable && (
                    <button onClick={() => handleSort(col.key)}>
                      <ExpandIcon
                        className={clsx(
                          'w-3 h-3',
                          'text-grey-50',
                          sortConfig.key === col.key ? 'text-black' : ''
                        )}
                      />
                    </button>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {sortedData.length > 0 ? (
            sortedData.map((row, rowIndex) => (
              <tr key={rowIndex} className="border-b border-grey-5">
                {columns.map((col) => (
                  <td
                    key={col.key}
                    className={clsx(
                      'p-2',
                      'text-xs font-regular',
                      'px-4 py-2',
                      'whitespace-nowrap',
                      col.hideHeader ? 'hidden sm:table-cell' : ''
                    )}
                  >
                    {row[col.key]}
                  </td>
                ))}
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={columns.length} className="px-4 py-2 text-center">
                No data found
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default DFilterTable;
