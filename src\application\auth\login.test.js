import { describe, expect, it, vi } from 'vitest';

import * as authService from '@/services/auth.service';
import { useUserStore } from '@/stores/user/userStore';

import getUserInfo from '../user/getUserInfo';

import loginUseCase from './login';

// Mock functions in useUserStore, authService, and getUserInfo
vi.mock('@/stores/user/userStore', () => ({
  useUserStore: {
    getState: vi.fn()
  }
}));

vi.mock('@/services/auth.service', () => ({
  login: vi.fn()
}));

vi.mock('../user/getUserInfo', () => ({
  default: vi.fn()
}));

describe('loginUseCase', () => {
  it('should save auth details and user info on successful login', async () => {
    const mockSaveAuthDetail = vi.fn();
    const mockSetUser = vi.fn();

    // Mock user store functions
    useUserStore.getState.mockReturnValue({
      saveAuthDetail: mockSaveAuthDetail,
      setUser: mockSetUser
    });

    // Mock the response from authService and getUserInfo
    authService.login.mockResolvedValue({
      status: 200,
      data: { access_token: 'test_access_token' }
    });

    const mockUserInfo = {
      id: 1,
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      date_created: '2023-01-01',
      login_count: 5
    };
    getUserInfo.mockResolvedValue(mockUserInfo);

    const result = await loginUseCase({
      username: 'john',
      password: 'password123',
      otp_code: '123456'
    });

    expect(mockSaveAuthDetail).toHaveBeenCalledWith({
      access_token: 'test_access_token',
      user_id: mockUserInfo.id,
      first_name: mockUserInfo.first_name,
      last_name: mockUserInfo.last_name,
      email: mockUserInfo.email,
      date_created: mockUserInfo.date_created,
      login_count: mockUserInfo.login_count
    });
    expect(mockSetUser).toHaveBeenCalledWith(mockUserInfo);
    expect(result.data).toEqual({ access_token: 'test_access_token', ...mockUserInfo });
  });

  it('should return status 428 for OTP request on login', async () => {
    authService.login.mockResolvedValue({
      status: 428,
      data: {}
    });

    const result = await loginUseCase({
      username: 'john',
      password: 'password123',
      otp_code: '123456'
    });

    expect(result).toEqual({ status: 428 });
  });

  it('should return status 428 for OTP request if error response status is 428', async () => {
    const error = { response: { status: 428 } };
    authService.login.mockRejectedValue(error);

    const result = await loginUseCase({
      username: 'john',
      password: 'password123',
      otp_code: '123456'
    });

    expect(result).toEqual({ status: 428 });
  });

  it('should return an error object if the API call fails', async () => {
    const error = new Error('Login failed');
    authService.login.mockRejectedValue(error);

    const result = await loginUseCase({
      username: 'john',
      password: 'password123',
      otp_code: '123456'
    });

    expect(result).toEqual(error);
  });

  it('should return an empty object if the response status is not 200 or 428', async () => {
    authService.login.mockResolvedValue({
      status: 400,
      data: {}
    });

    const result = await loginUseCase({
      username: 'john',
      password: 'password123',
      otp_code: '123456'
    });

    expect(result).toEqual({});
  });
});
