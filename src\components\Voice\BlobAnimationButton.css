/* BlobAnimationButton.css */
.white-blob-4 .blob-4 path {
  fill: white !important;
}

.white-blob-4 .blob-4.alt path {
  fill: white !important;
}

/* Make the button container appropriate size */
.dashboard-create_btn {
  min-height: 160px;
  overflow: visible !important;
}

/* Set animation size */
.blob-animation-button .blobs {
  transform: scale(0.94); /* 0.78 * 1.2 = 0.936 (20% larger) */
  max-height: 216px; /* 180px * 1.2 = 216px (20% larger) */
  max-width: 216px; /* 180px * 1.2 = 216px (20% larger) */
}

/* Ensure the blobs container is properly sized */
.dashboard-create_btn .blobs {
  transform: scale(0.94); /* 0.78 * 1.2 = 0.936 (20% larger) */
}

/* Adjust scale for the dashboard button */
.dashboard-create_btn .blob-animation-container {
  transform: scale(1.2); /* 1.0 * 1.2 = 1.2 (20% larger) */
}

.dashboard_voice_create_btn .blob-animation-button .blobs {
  max-width: 500px;
  max-height: 300px;
  transform: scale(1.2);
}