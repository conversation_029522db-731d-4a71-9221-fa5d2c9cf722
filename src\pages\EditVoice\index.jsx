import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import useLayoutStore from '@/stores/layout/layoutStore';
import { voiceSteps } from '@/components/Chatbot/Create/steps';
import { useEffect, useState } from 'react';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DSelect from '@/components/Global/DSelect';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import * as chatbotService from '@/services/chatbot.service';
import DMultiselect from '@/components/Global/DMultiselect';
import DAlert from '@/components/Global/DAlert';
import DLoaderTraining from '@/components/Global/DLoaderTraining';
import { COMMON_CLASSNAMES } from '@/constants';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import { useParams, useNavigate } from 'react-router-dom';
import { getLoadingSounds, getTextToVoiceOptions, getVoice, getAllVoiceSettings, updateVoice, deleteVoice } from '@/services/voice.service';
import DTextArea from '@/components/Global/DInput/DTextArea';
import { getPhoneNumbers } from '@/services/phoneNumber.service';
import useToast from '@/hooks/useToast';
import DSwitchAccordion from '@/components/Global/DSwitchAccordion';
import DTransition from '@/components/Global/DTransition';
import DTooltip from '@/components/Global/DTooltip';
import DButtonIcon from '@/components/Global/DButtonIcon';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
import { getChatbotCustomizationById } from '@/services/customization.service';
import VoicePreview from '@/components/Voice/VoicePreview';
import VoiceSelector from '@/components/Voice/VoiceSelector';

const EditVoice = () => {
    const params = useParams();
    const navigate = useNavigate();
    const setProgressBar = useLayoutStore((state) => state.setProgressBar);
    const { addSuccessToast, addErrorToast } = useToast();

    const { data, isLoading: isLoadingChatbots } = useDanteApi(chatbotService.getChatbots);
    const { data: voiceData, isLoading: isLoadingVoice } = useDanteApi(getVoice, [], {}, params.id);
    const { data: phoneNumbers, isLoading: isLoadingPhoneNumbers } = useDanteApi(getPhoneNumbers);
    const { data: loadingSounds, isLoading: isLoadingLoadingSounds } = useDanteApi(getLoadingSounds);

    const [voiceDataModified, setVoiceDataModified] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);

    // Determine if we're still loading initial data
    const isLoadingInitialData = isLoadingChatbots || isLoadingVoice || isLoadingPhoneNumbers || isLoadingLoadingSounds || !voiceData;
    const [confirmExit, setConfirmExit] = useState(false);
    const [confirmDelete, setConfirmDelete] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [providerVoices, setProviderVoices] = useState([]);
    const [errors, setErrors] = useState({});
    const [voiceSettings, setVoiceSettings] = useState({});
    const [initialVoiceData, setInitialVoiceData] = useState({});

    useEffect(() => {
        if (voiceData) {
            // Store the original text_to_voice_settings_id for later use
            const originalVoiceSettingsId = voiceData.text_to_voice_settings_id;

            const list = {
                name: voiceData.name,
                mode: voiceData?.mode_key,
                kb_id: {
                    label: voiceData.knowledge_base?.knowledge_base_name,
                    value: voiceData.knowledge_base?.id
                },
                phone_number_ids: voiceData.phone_numbers.map((item) => ({
                    label: item.number,
                    value: item.id
                })),
                voice_to_text: voiceData?.voice_to_text_key,
                llm: voiceData?.llm_key,
                text_to_voice: voiceData?.text_to_voice_key,
                // Store both the database ID and the voice value
                text_to_voice_settings_id: {
                    label: voiceData?.text_to_voice_settings_key?.label,
                    value: originalVoiceSettingsId, // This is the database ID
                    voice_value: voiceData?.text_to_voice_settings_key?.value // This is the value to use for voice preview
                },
                welcome_message: voiceData.welcome_message,
                personality_prompt: voiceData.personality_prompt,
                loading_sound_enabled: !!voiceData.loading_sound_id,
                loading_sound: voiceData.loading_sound_id
                    ? voiceData?.loading_sound_key
                    : null
            };
            setVoiceDataModified(list);
            setInitialVoiceData(list);
        }
    }, [voiceData]);

    useEffect(() => {
        const getVoiceOptions = async () => {
            try {
              const res = await getAllVoiceSettings();
              if(res.status === 200) {
                setVoiceSettings({
                  mode: res.data.modes,
                  ...res.data.results
                });
              }
            } catch (err) {
              console.error('Error fetching voice settings:', err);
            }
          };
        getVoiceOptions();
    }, []);


    // Fetch all voice providers without filtering
    useEffect(() => {
        const getTextToVoice = async () => {
            try {
                // Call the API without specifying a provider to get all voices
                const res = await getTextToVoiceOptions();
                if (res.status === 200) {

                    // Process the voices and add provider information
                    const processedVoices = res.data.results.map((voice) => {
                        // Determine provider from the voice_type field
                        let provider = 'Other';

                        // Map voice_type to provider name with proper capitalization
                        if (voice.voice_type) {
                            switch(voice.voice_type.toLowerCase()) {
                                case 'openai':
                                    provider = 'OpenAI';
                                    break;
                                case 'elevenlabs':
                                    provider = 'ElevenLabs';
                                    break;
                                case 'cartesia':
                                    provider = 'Cartesia';
                                    break;
                                default:
                                    provider = 'Other';
                            }
                        }

                        return {
                            label: voice.name,
                            value: voice.id, // Database ID - used for selection and tab switching
                            voice_value: voice.id, // The value to use for the voice preview
                            soundUrl: voice.preview_url,
                            provider: provider,
                            description: voice.description || '',
                            voice_type: voice.voice_type // Store the original voice_type for reference
                        };
                    });

                    setProviderVoices(processedVoices);
                }
            } catch (err) {
                console.error('Error fetching provider voices:', err);
                setProviderVoices([]);
            }
        };

        // Call the function to fetch all voices when the component mounts or when voiceData is loaded
        getTextToVoice();
    }, [voiceData]);

    const validateFields = () => {
        const newErrors = {};

        if (!voiceDataModified.name?.trim())
            newErrors.name = 'AI Voice Agent Name is required.';
        if (!voiceDataModified.kb_id)
            newErrors.kb_id = 'Chatbot selection is required.';
        if (!voiceDataModified.voice_to_text)
            newErrors.voice_to_text = 'Voice-to-text option is required.';
        if (!voiceDataModified.llm)
            newErrors.llm = 'LLM selection is required.';
        if (!voiceDataModified.text_to_voice)
            newErrors.text_to_voice = 'Text-to-voice provider is required.';
        if (!voiceDataModified.text_to_voice_settings_id)
            newErrors.voice = 'AI Voice Agent selection is required.';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const getChangedFields = () => {
        const diff = {};
        for (const key in voiceDataModified) {
          if (JSON.stringify(voiceDataModified[key]) !== JSON.stringify(initialVoiceData[key])) {
            if (Array.isArray(voiceDataModified[key]) && voiceDataModified[key].length > 0 && typeof voiceDataModified[key][0] === 'object' && 'value' in voiceDataModified[key][0]) {
              // Handle array of objects with value property
              diff[key] = voiceDataModified[key].map(item => item.value);
            } else if (voiceDataModified[key] && typeof voiceDataModified[key] === 'object' && 'value' in voiceDataModified[key]) {
              // Handle single object with value property
              diff[key] = voiceDataModified[key].value;
            } else {
              diff[key] = voiceDataModified[key];
            }
          }
        }
        return diff;
      };

      const getPersonalityPromptFromChatbot = async () => {
        if(!voiceDataModified.kb_id) {
          addErrorToast({ message: 'Please select a chatbot first.' });
          return;
        }

        try {
          const res = await getChatbotCustomizationById({ kb_id: voiceDataModified.kb_id.value });
          if(res.status === 200) {
            setVoiceDataModified({ ...voiceDataModified, personality_prompt: res.data.base_system_prompt });
          }
        } catch (err) {
          console.error('Error fetching personality prompt from chatbot:', err);
        }
      }

    const handleEditVoice = async () => {
        if (!validateFields()) return;

        // Set updating state to true to show loading indicator on the button
        setIsUpdating(true);

        const changedFields = getChangedFields();

        // If no fields have changed, show a message and return
        if (Object.keys(changedFields).length === 0) {
            setIsUpdating(false);
            addSuccessToast({ message: 'No changes to update' });
            return;
        }

        try {
            // Call the update endpoint
            const res = await updateVoice(params.id, changedFields);

            if (res.status === 200) {
                // Show success message
                addSuccessToast({ message: 'Voice updated successfully' });

                // Update the local state with the response data
                // This ensures we have the latest data without having to reload the page
                const updatedVoiceData = res.data;

                const updatedList = {
                    name: updatedVoiceData.name,
                    mode: updatedVoiceData?.mode_key,
                    kb_id: {
                        label: updatedVoiceData.knowledge_base?.knowledge_base_name,
                        value: updatedVoiceData.knowledge_base?.id
                    },
                    phone_number_ids: updatedVoiceData.phone_numbers.map((item) => ({
                        label: item.number,
                        value: item.id
                    })),
                    voice_to_text: updatedVoiceData?.voice_to_text_key,
                    llm: updatedVoiceData?.llm_key,
                    text_to_voice: updatedVoiceData?.text_to_voice_key,
                    text_to_voice_settings_id: {
                        label: updatedVoiceData?.text_to_voice_settings_key?.label,
                        value: updatedVoiceData.text_to_voice_settings_id,
                        voice_value: updatedVoiceData?.text_to_voice_settings_key?.value
                    },
                    welcome_message: updatedVoiceData.welcome_message,
                    personality_prompt: updatedVoiceData.personality_prompt,
                    loading_sound_enabled: !!updatedVoiceData.loading_sound_id,
                    loading_sound: updatedVoiceData.loading_sound_id
                        ? updatedVoiceData?.loading_sound_key
                        : null
                };

                // Update both the modified and initial data
                setVoiceDataModified(updatedList);
                setInitialVoiceData(updatedList);
            }

            // Reset updating state
            setIsUpdating(false);
        } catch (err) {
            console.error('Error updating voice:', err);
            addErrorToast({ message: err.response?.data?.detail || 'Failed to update voice' });

            // Reset updating state
            setIsUpdating(false);
        }
    };

    const handleDeleteVoice = async () => {
        setIsDeleting(true);
        try {
            const response = await deleteVoice(params.id);
            if (response.status === 200) {
                addSuccessToast({ message: 'AI Voice Agent deleted successfully' });
                navigate('/voice');
            }
        } catch (error) {
            console.error('Error deleting voice:', error);
            addErrorToast({ message: error.response?.data?.detail || 'Failed to delete AI Voice Agent' });
        } finally {
            setIsDeleting(false);
            setConfirmDelete(false);
        }
    };

    useEffect(() => {
        setProgressBar([]);
    }, []);

    if (isLoadingInitialData) {
        return (
            <div className="flex justify-center items-center h-full w-full">
                <DLoaderTraining />
            </div>
        );
    }

    return (
        <div className="flex flex-col md:flex-row gap-size3 h-full">
            <LayoutRightSidebar
                RightSidebar={() => (
                    <div className={COMMON_CLASSNAMES.previewBubble}>
                        <VoicePreview
                            chatbotId={voiceDataModified.kb_id?.value}
                            voiceId={voiceDataModified.text_to_voice_settings_id?.value}
                            welcomeMessage={voiceDataModified.welcome_message}
                            personalityPrompt={voiceDataModified.personality_prompt}
                            hideCloseButton={true}
                        />
                    </div>
                )}
            >
                {() => (
                    <LayoutWithButtons
                        className="pb-size8"
                        footer={null}
                        >
                            {!isLoading && (
                                <div className="flex flex-col gap-size5 scrollbar max-w-[650px] mx-auto w-full">
                                <div className="flex gap-size2 justify-end">
                                    <DButton
                                        variant="outlined"
                                        size="sm"
                                        onClick={() => {
                                            // Check if there are any changes
                                            const changedFields = getChangedFields();
                                            if (Object.keys(changedFields).length === 0) {
                                                // No changes, navigate directly
                                                navigate('/voice');
                                            } else {
                                                // Changes exist, show confirmation dialog
                                                setConfirmExit(true);
                                            }
                                        }}
                                        className="!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3"
                                    >
                                        Exit
                                    </DButton>
                                    <DButton
                                        variant="contained"
                                        size="sm"
                                        onClick={handleEditVoice}
                                        loading={isUpdating}
                                        disabled={isUpdating || Object.keys(getChangedFields()).length === 0}
                                        className="!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3"
                                    >
                                        {isUpdating ? 'Updating...' : 'Update'}
                                    </DButton>
                                </div>
                                <p className="text-xl font-medium tracking-tight">Edit AI Voice Agent</p>
                                <div className="flex flex-col gap-size1">
                                    <div className="flex flex-col gap-size0">
                                        <div className="flex items-center gap-size1">
                                            <p className="text-base font-medium tracking-tight">AI Voice Agent Name</p>
                                            <DTooltip content="Set a unique name to easily recognize this AI Voice Agent. This is private and will not be visible anywhere outside of your Dante AI dashboard.">
                                                <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                                            </DTooltip>
                                        </div>
                                    </div>
                                    <DInput
                                        value={voiceDataModified.name}
                                        onChange={(e) =>
                                            setVoiceDataModified({
                                                ...voiceDataModified,
                                                name: e.target.value
                                            })
                                        }
                                        placeholder="eg. John Doe"
                                        error={errors.name}
                                    />
                                </div>
                                <div className="flex flex-col gap-size1">
                                    <div className="flex flex-col gap-size0">
                                        <div className="flex items-center gap-size1">
                                            <p className="text-base font-medium tracking-tight">AI Chatbot</p>
                                            <DTooltip content="Select an AI Chatbot's as knowledge source for this AI Voice Agent.">
                                                <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                                            </DTooltip>
                                        </div>
                                    </div>
                                    <DSelect
                                        options={data?.results?.map((chatbot) => ({
                                            label: chatbot?.knowledge_base.knowledge_base_name,
                                            value: chatbot?.knowledge_base.id
                                        }))}
                                        value={voiceDataModified.kb_id?.value}
                                        onChange={(value) => {
                                            setVoiceDataModified({
                                                ...voiceDataModified,
                                                kb_id: { label: data?.results?.find((chatbot) => chatbot?.knowledge_base.id === value)?.knowledge_base.knowledge_base_name, value: value }
                                            })
                                        }}
                                        error={errors.kb_id}
                                    />
                                </div>
                                <div className="flex flex-col gap-size1">
                                    <div className="flex flex-col gap-size0">
                                        <div className="flex items-center gap-size1">
                                            <p className="text-base font-medium tracking-tight">Phone numbers</p>
                                            <DTooltip content="Add one or multiple verified phone numbers you would like this AI Voice Agent to be connected to.">
                                                <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                                            </DTooltip>
                                        </div>
                                        {/* <p className="text-sm text-grey-50 tracking-tight font-light">
                                            <a className="text-black underline font-medium" href="https://www.dante-ai.com/guides/step-1-set-up-a-twilio-phone-number" target="_blank" rel="noopener noreferrer">Read more here.</a>
                                        </p> */}
                                    </div>
                                    <DMultiselect
                                        options={phoneNumbers?.results?.map((item) => ({
                                            label: item.number,
                                            value: item.id
                                        }))}
                                        selected={voiceDataModified.phone_number_ids}
                                        skipSelectAllCheck
                                        setSelected={(value) => {
                                            setVoiceDataModified({
                                                ...voiceDataModified,
                                                phone_number_ids: value
                                            });
                                        }}
                                        error={errors.phone_number_ids}
                                    />
                                </div>
                                {/* <div className="flex flex-col gap-size1">
                                    <p className="text-base font-medium tracking-tight">AI Voice Agent mode</p> */}
                                    {/* <DSelect
                                        options={voiceSettings.mode}
                                        selectedChild={voiceDataModified.mode?.label || 'Select a method'}
                                        value={voiceDataModified.mode}
                                        onChange={(value) =>
                                            setVoiceDataModified({ ...voiceDataModified,
                                                mode: { label: voiceSettings.mode.find((opt) => opt.value === value)?.label, value },
                                                voice_to_text: null,
                                                llm: null,
                                                text_to_voice: null,
                                                text_to_voice_settings_id: null
                                            })
                                        }
                                        error={errors.mode}
                                    /> */}
                                    {/* <DInput
                                        value={'Flexible'}
                                        disabled
                                        onChange={(e) => {
                                            setVoiceDataModified({ ...voiceDataModified, mode: e.target.value });
                                        }}
                                    />
                                </div> */}
                                {/* {voiceDataModified.mode && (() => {
                                    const selectedModeSettings = Object.keys(voiceSettings)
                                    .filter(key => key !== "mode")
                                    .map(key => voiceSettings[key])
                                    .find(item => item.mode === 'flexible');

                                return (<DTransition show={voiceDataModified.mode} className="flex flex-col gap-size5">
                                    <div className="flex flex-col gap-size1">
                                        <p className="text-base font-medium tracking-tight">AI Voice to Text</p>
                                        <DSelect
                                            options={selectedModeSettings?.voice_to_text}
                                            selectedChild={
                                                voiceDataModified.voice_to_text?.label || "Select a mode"
                                            }
                                            value={voiceDataModified.voice_to_text}
                                            onChange={(value) =>
                                                setVoiceDataModified({
                                                    ...voiceDataModified,
                                                    voice_to_text: {
                                                        label: selectedModeSettings?.voice_to_text.find((opt) => opt.value === value)?.label,
                                                        value: value
                                                    }
                                                })
                                            }
                                            error={errors.voice_to_text}
                                        />
                                    </div>
                                    <div className="flex flex-col gap-size1">
                                        <p className="text-base font-medium tracking-tight">LLM</p>
                                        <DSelect
                                            options={selectedModeSettings?.llm}
                                            selectedChild={
                                                voiceDataModified.llm?.label || "Select a LLM"
                                            }
                                            value={voiceDataModified.llm}
                                            onChange={(value) =>
                                                setVoiceDataModified({
                                                    ...voiceDataModified,
                                                    llm: {
                                                        label: selectedModeSettings.llm.find((opt) => opt.value === value)?.label,
                                                        value: value
                                                    }
                                                })
                                            }
                                            error={errors.llm}
                                        />
                                    </div>
                                    <div className="flex flex-col gap-size1">
                                        <p className="text-base font-medium tracking-tight">Text to Voice Provider</p>
                                        <DSelect
                                            options={selectedModeSettings?.text_to_voice}
                                            selectedChild={
                                                voiceDataModified.text_to_voice?.label || "Select a voice"
                                            }
                                            value={voiceDataModified.text_to_voice}
                                            onChange={(value) =>
                                                setVoiceDataModified({
                                                    ...voiceDataModified,
                                                    text_to_voice: {
                                                        label: selectedModeSettings?.text_to_voice.find((opt) => opt.value === value)?.label,
                                                        value: value
                                                    }
                                                })
                                            }
                                            error={errors.text_to_voice}
                                        />
                                    </div>
                                </DTransition>)
                                })()} */}
                                {/* <DTransition show={voiceDataModified.text_to_voice}> */}
                                <div className="flex flex-col gap-size1">
                                    <div className="flex flex-col gap-size0">
                                        <div className="flex items-center gap-size1">
                                            <p className="text-base font-medium tracking-tight">AI Voice</p>
                                            <DTooltip content="Select the AI Voice you would like this AI Voice Agent to use for delivering responses.">
                                                <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                                            </DTooltip>
                                        </div>
                                    </div>
                                    <VoiceSelector
                                        voices={providerVoices}
                                        selectedVoice={voiceDataModified.text_to_voice_settings_id?.value}
                                        onVoiceChange={(value) => {
                                            const selectedVoice = providerVoices.find((opt) => opt.value === value);
                                            setVoiceDataModified({
                                                ...voiceDataModified,
                                                text_to_voice_settings_id: {
                                                    label: selectedVoice?.label,
                                                    value: selectedVoice?.value,
                                                    voice_value: selectedVoice?.voice_value
                                                }
                                            });
                                        }}
                                        disabled={isLoading}
                                        error={errors.text_to_voice_settings_id ? 'AI Voice selection is required.' : null}
                                    />
                                </div>
                                {/* </DTransition> */}
                                <div className="flex flex-col gap-size1">
                                    <div className="flex flex-col gap-size0">
                                        <div className="flex items-center gap-size1">
                                            <p className="text-base font-medium tracking-tight">Greeting message</p>
                                            <DTooltip content="Set a message you would like this voice to welcome the caller with e.g. &quot;Hello, thank you for calling Dante AI. What can I help you with today?&quot;">
                                                <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                                            </DTooltip>
                                        </div>
                                    </div>
                                    <DInput
                                        value={voiceDataModified.welcome_message}
                                        onChange={(e) =>
                                            setVoiceDataModified({
                                                ...voiceDataModified,
                                                welcome_message: e.target.value
                                            })
                                        }
                                        placeholder="Write your message "
                                        error={errors.welcome_message}
                                    />
                                </div>
                                <div className="flex flex-col gap-size0">
                                    <div className='flex items-center justify-between'>
                                        <div className='flex items-center gap-size1'>
                                            <p className="text-base font-medium tracking-tight">AI Voice Agent Personality prompt</p>
                                            <DTooltip content="Set a personality prompt for this AI Voice Agent. This will be used to guide the AI Voice Agent's responses. You can either use the personality prompt from the AI Chatbot by clicking the button on the right, or create your own.">
                                                <InfoIcon className="text-grey-50 cursor-help w-[11px] h-[11px] my-auto" />
                                            </DTooltip>
                                        </div>
                                        <DButton size="sm" onClick={getPersonalityPromptFromChatbot} className='!gap-size0 '>
                                            <CopyIcon className="w-4 h-4 text-grey-50" />
                                            <span className="text-xs whitespace-nowrap text-grey-50 leading-none">Copy from chatbot</span>
                                        </DButton>
                                    </div>
                                    <DTextArea
                                        value={voiceDataModified.personality_prompt}
                                        onChange={(e) =>
                                            setVoiceDataModified({
                                                ...voiceDataModified,
                                                personality_prompt: e.target.value
                                            })
                                        }
                                        placeholder="Write your message "
                                        error={errors.personality_prompt}
                                        resize
                                        autoExpand
                                        maxHeight="300px"
                                        minRows={3}
                                    />
                                </div>
                                {/* <div className="flex flex-col gap-size1">
                                    <DSwitchAccordion
                                        title="Set a Loading Sound"
                                        switchOpen={voiceDataModified.loading_sound_enabled}
                                        onToggle={(value) =>
                                            setVoiceDataModified({ ...voiceDataModified, loading_sound_enabled: value })
                                        }
                                    >
                                        <DSelect
                                            options={loadingSounds?.results?.map((item) => ({
                                                label: item.name,
                                                value: item.id,
                                                soundUrl: item.url
                                            }))}
                                            selectedChild={
                                                voiceDataModified.loading_sound?.label || 'Select a loading sound'
                                            }
                                            value={voiceDataModified.loading_sound}
                                            onChange={(value) =>
                                            setVoiceDataModified({ ...voiceDataModified, loading_sound: { label: value, value } })
                                            }
                                        />
                                    </DSwitchAccordion>
                                </div> */}
                                <DAlert state="alert" className="!w-full items-center">
                                    <p className="text-sm font-regular tracking-tight mt-1">
                                        Please review all the details added. Once you're happy with the set up, simply click 'Update' to save your changes.
                                    </p>
                                </DAlert>

                                <div className="w-full h-px bg-grey-5 my-size2"></div>

                                <div className="flex justify-start">
                                    <DButton
                                        variant="grey"
                                        size="md"
                                        onClick={() => setConfirmDelete(true)}
                                        disabled={isDeleting}
                                        fullWidth
                                    >
                                        Delete AI Voice Agent
                                    </DButton>
                                </div>
                            </div>
                        )}
                        {isLoading && (
                            <div className="flex flex-col justify-center items-center h-full gap-size1">
                                <DLoaderTraining />
                                <p className="text-xl font-regular tracking-tight">
                                    Updating AI Voice...
                                </p>
                            </div>
                        )}
                    </LayoutWithButtons>
                )}
            </LayoutRightSidebar>
            <DConfirmationModal
                open={confirmExit}
                onClose={() => setConfirmExit(false)}
                onConfirm={() => navigate('/voice')}
                title="Leave without saving?"
                description="You have unsaved changes. If you leave, your changes will be lost."
                confirmText="Leave"
                cancelText="Cancel"
                variantConfirm="danger"
            />
            <DConfirmationModal
                open={confirmDelete}
                onClose={() => setConfirmDelete(false)}
                onConfirm={handleDeleteVoice}
                title="Delete AI Voice Agent"
                description="Are you sure you want to delete this AI Voice Agent? This action cannot be undone."
                confirmText="Delete"
                cancelText="Cancel"
                variantConfirm="danger"
                loading={isDeleting}
            />
        </div>
    );
};

export default EditVoice;
