import { openDB } from 'idb';

const dbPromise = openDB('ConversationsDB', 1, {
  upgrade(db) {
    if (!db.objectStoreNames.contains('conversations')) {
      const store = db.createObjectStore('conversations', { keyPath: 'id', autoIncrement: true });
      store.createIndex('kb_id', 'kb_id');
    }
  },
});

// Save a conversation to IndexedDB
export const saveConversationToDB = async (conversation) => {
  const db = await dbPromise;
  // Check if the conversation already exists
  const tx = db.transaction('conversations', 'readwrite');
  const store = tx.objectStore('conversations');
  const existing = await store.getAll();
  const match = existing.find((item) => item.kb_id === conversation.kb_id && item.id === conversation.id);

  if (match) {
    // Update the existing conversation
    await store.put({ ...match, ...conversation, updatedAt: Date.now() });
  } else {
    // Save new conversation
    await store.put({ ...conversation, createdAt: Date.now(), updatedAt: Date.now() });
  }
};

// Fetch all conversations from IndexedDB
export const getConversationsFromDB = async () => {
  const db = await dbPromise;
  const conversations = await db.getAll('conversations');
  return conversations.filter((conv) => Date.now() - conv.createdAt < 2 * 24 * 60 * 60 * 1000); // 2 days
};

// Remove expired conversations from IndexedDB
export const removeExpiredConversations = async () => {
  const db = await dbPromise;
  const tx = db.transaction('conversations', 'readwrite');
  const store = tx.objectStore('conversations');
  const conversations = await store.getAll();
  const expiredIds = conversations
    .filter((conv) => Date.now() - conv.createdAt >= 2 * 24 * 60 * 60 * 1000) // 2 days
    .map((conv) => conv.id);

  for (const id of expiredIds) {
    await store.delete(id);
  }
  await tx.done;
};