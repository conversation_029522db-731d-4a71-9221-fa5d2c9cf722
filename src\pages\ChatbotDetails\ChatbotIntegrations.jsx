import { useEffect, useState } from 'react';

import DChatbotSidebar from '@/components/Chatbot/Details/ChatbotSidebar';
import DButton from '@/components/Global/DButton';
import DButtonIcon from '@/components/Global/DButtonIcon';
import DInput from '@/components/Global/DInput/DInput';
import DIntegrationApp from '@/components/Global/DIntegrationApp';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import KeyIcon from '@/components/Global/Icons/KeyIcon';
import SearchIcon from '@/components/Global/Icons/SearchIcon';
import useDanteApi from '@/hooks/useDanteApi';
import * as userService from '@/services/user.service';
import * as integrationService from '@/services/integration.service';
import useLayoutStore from '@/stores/layout/layoutStore';
import IntegrationApps from '@/components/IntegrationApps';
import featureCheck from '@/helpers/tier/featureCheck';
import useToast from '@/hooks/useToast';
import BlurredOverlay from '@/components/BlurredOverlay';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import CheckmarkIcon from '@/components/Global/Icons/CheckmarkIcon';
import DLoading from '@/components/DLoading';
import DTransition from '@/components/Global/DTransition';
import { Transition, TransitionChild } from '@headlessui/react';
import GlobalModals from '@/components/GlobalModals';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';

const ChatbotIntegrations = () => {
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const teamSelected = useTeamManagementStore((state) => state.selectedTeam?.id);
  const [showInput, setShowInput] = useState(false);
  const { data: apps, loading: appsLoading } = useDanteApi(
    integrationService.getAllIntegrations,
    [],
    {
      skip: !checkTeamManagementPermission('integrations'),
    }
  );
  const { data: apiKey, loading: apiKeyLoading } = useDanteApi(
    userService.getUserAPIKey,
    [],
    {
      skip: !checkTeamManagementPermission('api_key'),
    }
  );
  const { addWarningToast } = useToast();
  const [filteredApps, setFilteredApps] = useState(apps);
  const [generatedApiKey, setGeneratedApiKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCopying, setIsCopying] = useState(false);

  const copyApiKey = () => {
    navigator.clipboard.writeText(generatedApiKey);
    setIsCopying(true);
    setTimeout(() => {
      setIsCopying(false);
    }, 2000);
  };

  const deleteApiKey = async () => {
    setGeneratedApiKey('');
    try {
      await userService.deleteUserAPIKey();
    } catch (error) {
      console.log(error);
    }
  };

  const searchApps = (search) => {
    setFilteredApps(
      apps.filter((app) =>
        app.label.toLowerCase().includes(search.toLowerCase())
      )
    );
  };

  const generateApiKey = async () => {
    if ((!teamSelected && featureCheck('api_access')) || (teamSelected && featureCheck('api_key'))) {
      setIsLoading(true);
      try {
        const response = await userService.createUserAPIKey();
        setGeneratedApiKey(response.data.secret_key);
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    setProgressBar([]);
    if (apiKey?.secret_key) {
      setGeneratedApiKey(apiKey.secret_key);
    }
  }, [apiKey]);

  useEffect(() => {
    setSidebarOpen(false);
    if (window.innerWidth < 768) {
      setLayoutTitle('Chatbot');
    } else {
      setLayoutTitle('');
    }
  }, [window.innerWidth]);

  useEffect(() => {
    if (apps) {
      setFilteredApps(apps);
    }
  }, [apps]);

  if (
    checkTeamManagementPermission('integrations') &&
    checkTeamManagementPermission('api_key')
  ) {
    if (!apps || appsLoading || apiKeyLoading) {
      return <DLoading show={true} />;
    }
  }

  return (
    <div className="w-full h-[1px] grow overflow-y-auto md:h-full bg-white rounded-size1 p-size5 flex flex-col gap-size5">
      {checkTeamManagementPermission('api_key') && (
        <div className="flex gap-size1 flex-col w-full">
          <p className="text-xl font-medium tracking-tight">API Key</p>
          <div className="w-full flex flex-col md:flex-row items-start gap-size3">
            <DInput
              className="!rounded-size1 !w-full"
              value={generatedApiKey}
            />
            {!generatedApiKey && (
              <DButton
                variant="dark"
                className="!h-11 !min-w-52"
                onClick={generateApiKey}
                disabled={isLoading}
              >
                <KeyIcon />
                Generate API Key
              </DButton>
            )}
            <div className="flex gap-size1">
              {generatedApiKey && (
                <DButton
                  variant="dark"
                  className="!h-11 !min-w-52"
                  onClick={copyApiKey}
                >
                  {isCopying ? <CheckmarkIcon /> : <CopyIcon />}
                  Copy API Key
                </DButton>
              )}
              {generatedApiKey && (
                <DButtonIcon
                  variant="grey"
                  onClick={deleteApiKey}
                  className="!h-11 !w-11"
                >
                  <DeleteIcon />
                </DButtonIcon>
              )}
            </div>
          </div>
        </div>
      )}
      {apps?.filter((app) => app.connected).length > 0 && (
        <div className="w-full h-px bg-grey-5"></div>
      )}
      {apps?.filter((app) => app.connected).length > 0 && (
        <div className="flex flex-col gap-size3">
          <div className="flex items-center justify-between">
            <p className="text-xl font-medium tracking-tight">Connected</p>
          </div>
        </div>
      )}
      {apps?.filter((app) => app.connected).length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-size3">
          {apps
            .filter((app) => app.connected)
            .map((app) => (
              <BlurredOverlay
                show={!checkTeamManagementPermission('integrations')}
                className="h-full"
              >
                <DIntegrationApp app={app} key={app.label} />
              </BlurredOverlay>
            ))}
        </div>
      )}
      {apps?.filter((app) => app.connected).length > 0 && (
        <div className="bg-grey-5 h-px w-full"></div>
      )}
      {checkTeamManagementPermission('integrations') && (
        <div className="flex flex-row gap-size3 items-center w-full">
          <div className="flex items-center gap-size3">
            <p className="text-xl font-medium tracking-tight">Integrations</p>
          </div>
          <div className="flex items-center gap-size3 w-full">
            <div
              className={`transition-all duration-300 ease-in-out flex items-center justify-start border border-grey-10 rounded-size0 h-11 max-w-[300px] ${
                showInput ? 'w-full ' : 'w-11'
              }`}
            >
              <div className={'size-11 flex items-center justify-center'}>
                <DButtonIcon
                  size="lg"
                  variant="ghost"
                  onClick={() => setShowInput(!showInput)}
                  className={'!size-11'}
                >
                  <SearchIcon />
                </DButtonIcon>
              </div>
              <Transition show={showInput}>
                <div className="w-full transition-all duration-300 ease-in-out w-full max-w-[300px]">
                  <input
                    type="text"
                    className={'w-full bg-white'}
                    onChange={(e) => searchApps(e.target.value)}
                  />
                </div>
              </Transition>
            </div>
          </div>
        </div>
      )}
      <div>
        <IntegrationApps filteredAppSeacrh={filteredApps} />
      </div>
      <GlobalModals />
    </div>
  );
};

export default ChatbotIntegrations;
