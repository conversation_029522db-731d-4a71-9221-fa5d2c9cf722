import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import DModal from '@/components/Global/DModal';
import DButton from '@/components/Global/DButton';
import SuccessIcon from '@/components/Global/Icons/SuccessIcon';
import { useUserStore } from '@/stores/user/userStore';
import useModalStore from '@/stores/modal/modalStore';

const FirstVoiceSuccessModal = ({ isOpen, onClose, id }) => {
  const navigate = useNavigate();
  const { user } = useUserStore();
  const openPlansModal = useModalStore((state) => state.openPlansModal);
  
  return (
    <DModal
      isOpen={isOpen}
      onClose={onClose}
      // title="🎉 Congratulations!"
      className="!max-w-[500px]"
    >
      <div className="flex flex-col items-center gap-6 py-8 pb-1">
        <div className="w-20 h-20 bg-green-50 rounded-full flex items-center justify-center animate-bounce">
          <SuccessIcon className="w-10 h-10 text-green-500" />
        </div>
        
        <div className="text-center space-y-6">
          <h3 className="text-2xl font-semibold text-gray-900">
            Your AI Voice Agent is ready!
          </h3>
          
          <p className="text-gray-600 leading-relaxed text-sm">
            Your AI agent is ready to use right away in the Dante dashboard. You can start interacting with it and testing its capabilities immediately.
          </p>

          {user.tier_type === 'free' ? (
            <>
              <DButton 
                fullWidth 
                variant="dark" 
                size="sm"
                onClick={() => openPlansModal('nr_free_phone_numbers')}
                className="text-lg font-medium !bg-purple-300"
              >
                Upgrade to unlock your free number
              </DButton>
              
              <p className="text-gray-600 leading-relaxed text-sm">
                To connect your AI agent to a phone number, you'll need to upgrade your plan first. 
                Starter plans and above include a free phone number. <br/> Upgrade now to start receiving calls to your AI agent!
              </p>
            </>
          ) : (
            <>
              <DButton 
                fullWidth 
                variant="green" 
                size="sm"
                onClick={() => navigate('/phone-numbers/purchase')}
                className="text-lg font-medium"
              >
                Claim your free business number
              </DButton>
              
              <p className="text-gray-600 leading-relaxed text-sm">
                If you'd like to call your AI agent from an external phone number, you'll need to connect a phone number first. <br/> <br /> Don't worry, you can always come back later to get your free number, or if you already have a phone number you'd like to use, you can add it on the <a href="/phone-numbers" className="text-gray-500 hover:text-blue-800 underline">Phone Numbers page</a>.
              </p>
            </>
          )}
          <DButton
            fullWidth
            variant="contained"
            size="sm"
            onClick={() => navigate(`/voice-agents/${id}/edit`)}
            className="text-lg font-medium"
          >
            Preview your AI Voice Agent
          </DButton>
        </div>
      </div>
    </DModal>
  );
};

export default FirstVoiceSuccessModal;
