import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { trackPageView } from '@/helpers/analytics';

/**
 * Custom hook to track page views when the URL changes
 *
 * Usage:
 * ```
 * // In your App.jsx or a layout component
 * usePageViewTracking();
 * ```
 */
const usePageViewTracking = () => {
  const location = useLocation();
  const prevLocation = useRef(null);

  useEffect(() => {
    // Only track if this is not the initial render or if the path has changed
    if (prevLocation.current) {
      // Use previous location as referrer
      trackPageView(location, prevLocation.current.pathname);
    } else {
      // First page view, use document.referrer
      trackPageView(location);
    }

    // Update the previous location
    prevLocation.current = location;
  }, [location.pathname, location.search]);

  // Also track on initial mount
  useEffect(() => {
    trackPageView(location);
    prevLocation.current = location;
  }, []);

  return null;
};

export default usePageViewTracking;
