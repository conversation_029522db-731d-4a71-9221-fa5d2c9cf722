import { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom/dist';

import DButton from '@/components/Global/DButton';
import DButtonIcon from '@/components/Global/DButtonIcon';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import DInput from '@/components/Global/DInput/DInput';
import DTable from '@/components/Global/DTable';
import DTooltip from '@/components/Global/DTooltip';
import AddIcon from '@/components/Global/Icons/AddIcon';
import CheckmarkIcon from '@/components/Global/Icons/CheckmarkIcon';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import EditIcon from '@/components/Global/Icons/EditIcon';
import OptionsHorizontalIcon from '@/components/Global/Icons/OptionsHorizontalIcon';
import ResetIcon from '@/components/Global/Icons/ResetIcon';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import * as teamManagementService from '@/services/teamManagement.service';
import useLayoutStore from '@/stores/layout/layoutStore';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { useUserStore } from '@/stores/user/userStore';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';

import AddMember from './AddMember';
import AddRole from './AddRole';
import EditMember from './EditMember';
import LayoutMain from '@/layouts/LayoutMain';
import DLoading from '@/components/DLoading';
import DAddSeatsModal from '@/components/DAddSeatsModal';
import useToast from '@/hooks/useToast';
import EditRole from './EditRole';
import LiveAgentIcon from '@/components/Global/Icons/LiveAgentIcon';

const TeamManagement = () => {
  //store
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const teamName = useTeamManagementStore((state) => state.teamName);
  const setTeamName = useTeamManagementStore((state) => state.setTeamName);
  const selectedTeam = useTeamManagementStore((state) => state.selectedTeam);
  const setSelectedTeam = useTeamManagementStore((state) => state.setSelectedTeam);
  const owner = useTeamManagementStore((state) => state.owner);
  const userData = useUserStore((state) => state.user);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);

  //states
  const [editTeamName, setEditTeamName] = useState(false);
  const [tableData, setTableData] = useState([]);
  const { id } = useParams();
  const [openedModal, setOpenedModal] = useState(null);
  const [openAddSeatsModal, setOpenAddSeatsModal] = useState(false);

  //member
  const [openAddMember, setOpenAddMember] = useState(false);
  const [newMember, setNewMember] = useState({
    email: '',
    team_role_id: '',
    all_knowledge_bases: false,
    allowed_kbs: [],
    credits_available: 100,
  });
  const [openDeleteMember, setOpenDeleteMember] = useState(false);
  const [deleteMember, setDeleteMember] = useState(null);
  const [openResetCreditsModal, setOpenResetCreditsModal] = useState(false);
  const [resetCreditsMember, setResetCreditsMember] = useState(null);

  //edit member
  const [openEditMember, setOpenEditMember] = useState(false);
  const [editMember, setEditMember] = useState(null);

  //roles
  const [openAddRole, setOpenAddRole] = useState(false);
  const [roleName, setRoleName] = useState('');
  const [customRole, setCustomRole] = useState({
    view_chatbots: false,
    edit_chatbot_name: false,
    edit_chatbot_status: false,
    delete_chatbot: false,
    tabs: false,
    insights: false,
    integrations: false,
    api_key: false,
    create_chatbot: false,
    chatbot_safety: false,
    chatbot_knowledge: false,
    chatbot_personality: false,
    chatbot_powerups: false,
    chatbot_core_settings: false,
    chatbot_styling: false,
    edit_chatbots: false,
    edit_customization: false,
    share_chatbots: false,
    edit_safety: false,
    view_chat_logs: false,
    edit_api_key: false,
    edit_whatsapp_integration: false,
    edit_intercom_integration: false,
    analytics: false,
    live_agents: false,
    assign_avatar: false,
    create_avatar: false,
    edit_avatar: false,
  });

  //permissions
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const [isAddMemberLoading, setIsAddMemberLoading] = useState(false)
  const [isEditMemberLoading, setIsEditMemberLoading] = useState(false)
  const [rolePermissions, setRolePermissions] = useState([]);

  //edit role
  const [openEditRole, setOpenEditRole] = useState(false);
  const [openDeleteRole, setOpenDeleteRole] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [changedValuesRolePermissions, setChangedValuesRolePermissions] = useState([]);

  //error
  const [error, setError] = useState({});

  const { addSuccessToast } = useToast()
  //api calles on render
  const { isLoading, data, refetch } = useDanteApi(
    teamManagementService.getTeamMembers,
    [],
    {},
    id
  );
  const {
    isLoading: isLoadingRoles,
    data: roles,
    refetch: refetchRoles,
  } = useDanteApi(teamManagementService.getTeamRoles, [], {}, id);
  const { isLoading: isLoadingPermissions, data: dataPermissions } =
    useDanteApi(teamManagementService.getRolePermissions);

  const isAboveSm = useIsAboveBreakpoint('sm');
  //table columns
  const ownerColumn = [
    {
      label: 'Credits',
      key: 'credits',
      showName: true,
      minWidth: 'min-w-10',
      mobile: true,
    },
    {
      label: 'Role',
      key: 'role',
      showName: true,
      minWidth: 'min-w-10',
      mobile: true,
    },
    {
      label: 'Actions',
      key: 'actions',
      showName: false,
      minWidth: 'min-w-10',
    },
  ];

  const userColumn = [
    {
      label: 'Last Login',
      key: 'last_login',
      showName: true,
      minWidth: 'min-w-10',
      mobile: false,
    },
  ];

  const tableColumns = [
    {
      label: 'Member',
      key: 'member',
      showName: true,
      minWidth: 'min-w-10',
    },
    {
      label: 'Email',
      key: 'email',
      showName: true,
      minWidth: 'min-w-10',
    },
    ...(selectedTeam?.owner_id === userData?.id ? ownerColumn : userColumn),
  ];

  let team = null;

  const handleResendInvitation = async (memberId) => {
    try {
      const response = await teamManagementService.resendInvitation(id, memberId);
      if (response.status === 200) {
        addSuccessToast({ message: 'Invitation sent successfully', state: 'positive' });
      }
    } catch (error) {
      console.log(error);
    }
  }

  //functions
  const setTeamData = (data) => {
    if (data) {
      const teamData = data?.map((member) => {
        return {
          member: (
            <p className={!member.accepted && selectedTeam?.owner_id === userData?.id ? 'text-grey-50' : 'text-black'}>
              {member.user_data.first_name} {member.user_data.last_name}{' '}
              <span className="text-grey-20">
                {member.user_id === userData.id && '(You)'}
                {!member.accepted && selectedTeam?.owner_id === userData?.id && '(Pending)'}
              </span>
            </p>
          ),
          email: (
            <p className={!member.accepted && selectedTeam?.owner_id === userData?.id ? 'text-grey-50' : ''}>
              {member.user_data.email}
            </p>
          ),
          credits: (
            owner?.owner_id !== member.user_data.id && <p>
              {member.credits}
              <span className="text-grey-50">
                /{member.credits_available}
              </span>{' '}
              credits left
            </p>
          ),
          role: member.team_role.name,
          icon: member.user_data.profile_image,
          accepted: member.accepted,
          last_login: new Date(member.user_data.last_login).toLocaleDateString(
            'en-US',
            {
              month: 'long',
              day: 'numeric',
              year: 'numeric',
              hour: 'numeric',
              minute: 'numeric',
            }
          ),
          actions: (
            owner?.owner_id === selectedTeam?.owner_id && member.user_id !== selectedTeam?.owner_id && <Menu>
              <MenuButton className="flex items-center justify-end gap-size2 w-full">
                <div
                  size="lg"
                  className="hover:bg-grey-5 rounded-size0 size-10 flex items-center justify-center"
                >
                  <OptionsHorizontalIcon />
                </div>
              </MenuButton>
              <MenuItems
                transition
                anchor="bottom end"
                className="border border-grey-5 rounded-size0 p-size0 flex flex-col gap-size1 bg-white"
              >
                <MenuItem
                  onClick={() => {
                    setOpenedModal('edit');
                    setEditMember(member);
                    setOpenEditMember(true);
                  }}
                >
                  <div className="p-size0 rounded-size0 hover:bg-grey-5 flex items-center gap-size1">
                    <EditIcon />
                    <span className="text-xs font-regular tracking-tight">
                      Edit member
                    </span>
                  </div>
                </MenuItem>
                <MenuItem
                  onClick={() => {
                    setOpenDeleteMember(true);
                    setDeleteMember(member.user_data.id);
                  }}
                >
                  <div className="p-size0 rounded-size0 hover:bg-grey-5 flex items-center gap-size1">
                    <DeleteIcon />
                    <span className="text-xs font-regular tracking-tight">
                      Remove from Team
                    </span>
                  </div>
                </MenuItem>
                {!member.accepted && (<MenuItem onClick={() => {
                  handleResendInvitation(member.id);
                }}>
                  <div className="p-size0 rounded-size0 hover:bg-grey-5 flex items-center gap-size1">
                    <ResetIcon />
                    <span className="text-xs font-regular tracking-tight">
                      Resend Invitation
                    </span>
                    </div>
                  </MenuItem>
                )}
                <MenuItems
                  onClick={() => {
                    setOpenResetCreditsModal(true);
                    setResetCreditsMember(member.user_id);
                  }}
                >
                  <div className="p-size0 rounded-size0 hover:bg-grey-5 flex items-center gap-size1">
                    <LiveAgentIcon />
                    <span className="text-xs font-regular tracking-tight">
                      Reset Credits
                    </span>
                  </div>
                </MenuItems>
              </MenuItems>
            </Menu>
          ),
        };
      });
      setTableData(teamData);
    } else {
      setTableData([]);
    }
  };

  const handleEditTeamRole = async () => {
    try {
      if(selectedRole.name === '') {
        setError({
          roleName: 'Role name is required'
        })
        return;
      }else{
        setError({})
      }
      
      const response = await teamManagementService.editTeamRole(
        id,
        selectedRole.id,
        changedValuesRolePermissions
      );

      if (response.status === 200) {
        setOpenEditRole(false);
        refetchRoles();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleEditTeamMember = async () => {
    setIsEditMemberLoading(true)
    try {
      const response = await teamManagementService.editTeamMember(
        id,
        editMember.user_data.id,
        {
          ...editMember,
          allowed_kbs: editMember.allowed_kbs.map((kb) => kb.id),
        }
      );
      if (response.status === 200) {
        setOpenEditMember(false);
        refetch();
      }
    } catch (error) {
      console.log(error);
    }finally{
      setIsEditMemberLoading(false)
    }
  };

  // const categorizePermissions = (permissions) => {
  //   let categoriesNames = [];
  //   const categorized = permissions.reduce((acc, permission) => {
  //     const category = permission.category;
  //     if (!acc[category]) {
  //       acc[category] = [];
  //       categoriesNames.push(category);
  //     }
  //     acc[category].push({ name: permission.name, label: permission.label });
  //     return acc;
  //   }, {});

  //   setPermissionCategories(
  //     categoriesNames.sort((a, b) => {
  //       if (a.startsWith('Other') && !b.startsWith('Other')) {
  //         return 1;
  //       } else if (!a.startsWith('Other') && b.startsWith('Other')) {
  //         return -1;
  //       }
  //       return 0;
  //     })
  //   );
  //   setCategorizedPermissions(categorized);
  // };

  const getPermissions = async () => {
    if (dataPermissions?.permissions) {
      setRolePermissions(dataPermissions?.permissions);
    }
  };

  const createTeamRole = async (e) => {
    e.preventDefault();
    if(roleName === '') {
      setError({
        roleName: 'Role name is required'
      })
      return;
    }else{
      setError({})
    }
    try {
      const response = await teamManagementService.createTeamRole({
        team_id: id,
        name: roleName,
        ...customRole,
      });
      if (response.status === 200) {
        setOpenAddRole(false);
        setRoleName('');
        setSelectedPermissions([]);
        if (openedModal === 'add') {
          setNewMember({ ...newMember, team_role_id: response.data.id });
          setOpenAddMember(true);
        } else if (openedModal === 'edit') {
          setOpenEditMember(true);
          setEditMember({ ...editMember, team_role_id: response.data.id });
        }
        refetchRoles();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const resetCustomRole = () => {
    setCustomRole({
      name: '',
      view_chatbots: false,
      edit_chatbots: false,
      create_chatbot: false,
      edit_customization: false,
      share_chatbots: false,
      edit_safety: false,
      view_chat_logs: false,
      edit_api_key: false,
      edit_whatsapp_integration: false,
      edit_intercom_integration: false,
      analytics: false,
      live_agents: false,
      assign_avatar: false,
      create_avatar: false,
      edit_avatar: false,
    });
  };

  const createNewMember = async () => {
    let validationErrors = {};
    setNewMember({...newMember, credits_available: 100});
  
    if (!newMember.email) {
      validationErrors.email = 'Email is required';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(newMember.email)) {
        validationErrors.email = 'Invalid email address';
      }
    }
  
    if (newMember.team_role_id === '' || newMember.team_role_id === undefined) {
      validationErrors.team_role_id = 'Team role is required';
    }
  
    // if (
    //   !newMember.all_knowledge_bases &&
    //   (!newMember.allowed_kbs || newMember.allowed_kbs.length === 0)
    // ) {
    //   validationErrors.allowed_kbs =
    //     'Please select at least one chatbot or choose "Add all future chatbots".';
    // }
  
    // if (newMember.credits_available == null || newMember.credits_available < 0 || newMember.credits_available === '' || newMember.credits_available === undefined) {
    //   validationErrors.credits_available =
    //     'Credit allowance must be a positive number or zero.';
    // }
  
    if (Object.keys(validationErrors).length > 0) {
      setError(validationErrors);
      return;
    }
  
    setError({});
  
    setIsAddMemberLoading(true);
    try {
      const response = await teamManagementService.createTeamMember(id, newMember);
      if (response.status === 200) {
        setOpenAddMember(false);
        setNewMember({
          email: '',
          team_role_id: '',
          all_knowledge_bases: false,
          allowed_kbs: [],
        });
        refetch();
        addSuccessToast({ message: 'Member added successfully to the team' });
      }
    } catch (error) {
      console.log(error);
      // Optionally, you can update error state or show a toast here
    } finally {
      setIsAddMemberLoading(false);
    }
  };
  
  const deleteTeamMember = async () => {
    try {
      const response = await teamManagementService.deleteTeamMember(
        id,
        deleteMember
      );
      if (response.status === 200) {
        setOpenDeleteMember(false);
        setOpenEditMember(false);
        refetch();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const deleteTeamRole = async () => {
    try {
      const response = await teamManagementService.deleteTeamRole(selectedRole.id);
      if (response.status === 200) {
        setOpenDeleteRole(false);
        refetchRoles();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleBackBtn = () => {
    setOpenAddRole(false);
    if (openedModal === 'edit') {
      setOpenEditMember(true);
    } else if (openedModal === 'add') {
      setOpenAddMember(true);
    }
  };

  const handleEditTeamName = async () => {
    setTeamName(teamName);
    try {
      const response = await teamManagementService.updateTeam(id, teamName);
      if (response.status === 200) {
        setSelectedTeam({ ...selectedTeam, name: teamName });
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const getTeam = async () => {
    try{
      const response = await teamManagementService.getTeam(id);
      if(response.status === 200) {
        team = response.data;
      }
    } catch (error) {
      console.log('error', error);
    }
  }

  const resetCredits = async () => {
    try {
      const response = await teamManagementService.resetCredits(id, resetCreditsMember);
      if(response.status === 200) {
        setOpenResetCreditsModal(false);
        refetch();
      }
    } catch (error) {
      console.log('error', error);
    }
  }

  useEffect(() => {
    setTeamData(data?.results);
    getPermissions();
  }, [data]);

  useEffect(() => {
    if (!isAboveSm) {
      setLayoutTitle('Team Management');
    } else {
      setLayoutTitle(null);
    }
    if(selectedTeam?.owner_id === userData?.id) {
      getTeam();
    }
    setProgressBar([]);
  }, []);

  if (isLoading) {
    return <DLoading show={true} />;
  }

  return (
    <LayoutMain title="Team Management">
      <div className="3xl:max-w-[1200px] 3xl:mx-auto">
        <div className="flex flex-col gap-size2 md:gap-0 md:flex-row items-center h-auto md:h-11 mb-size2">
          <div className="flex w-full items-center gap-size3">
            {editTeamName ? (
              <DInput
                value={teamName || selectedTeam?.name}
                onChange={(e) => {
                  setTeamName(e.target.value);
                }}
                className="rounded-none border-t-0 border-r-0 border-l-0"
              />
            ) : (
              <p className="text-xl font-regular tracking-tight">{teamName || selectedTeam?.name}</p>
            )}
            <div className="flex items-center gap-size2">
              <DButtonIcon
                size="lg"
                className={`border border-grey-5 rounded-size0 size-8 ${selectedTeam?.owner_id !== userData?.id ? 'hidden' : ''}`}
                onClick={() => {
                  if (editTeamName) {
                    setEditTeamName(false);
                    handleEditTeamName();
                  } else {
                    setEditTeamName(true);
                  }
                }}
              >
                {editTeamName ? <CheckmarkIcon /> : <EditIcon />}
              </DButtonIcon>
              {editTeamName && (
                <DButtonIcon
                  size="lg"
                  className="border border-grey-5 rounded-size0 size-8"
                  onClick={() => setEditTeamName(false)}
                >
                  <CloseIcon />
                </DButtonIcon>
              )}
            </div>
          </div>
          <div className={`flex items-center gap-size3 w-full justify-between md:justify-end ${selectedTeam?.owner_id !== userData?.id ? 'hidden' : ''}`}>
            <DTooltip content="Remaining seats from both tiers and addon purchases" position="top">
              <p className="font-light font-lg tracking-tight text-grey-20">
                {data?.nr_seats_from_addons +
                  data?.nr_seats_from_tier -
                  (data?.results?.length - 1)}{' '}
                free seats left
              </p>
            </DTooltip>
            <DButton variant="grey" size="sm" onClick={() => setOpenAddSeatsModal(true)}>
              Add seats
            </DButton>
          </div>
        </div>
        <div>
          <DTable columns={tableColumns} data={tableData} />
        </div>
        <DButton
          variant="dark"
          size="md"
          className="max-w-56 md:max-w-52 mt-size2 h-10"
          onClick={() => {
            setOpenedModal('add');
            setOpenAddMember(true);
          }}
        >
          <AddIcon />
          Add new member
        </DButton>
      </div>
      <EditMember
        open={openEditMember}
        onClose={() => {
          setOpenEditMember(false);
          setEditMember(null);
        }}
        member={editMember}
        setMember={setEditMember}
        roles={roles?.results}
        onSave={handleEditTeamMember}
        setOpenAddRole={setOpenAddRole}
        deleteTeamMember={() => setOpenDeleteMember(true)}
        setDeleteMember={setDeleteMember}
        handleResendInvitation={handleResendInvitation}
        loading={isEditMemberLoading}
        setOpenEditRole={setOpenEditRole}
        setOpenDeleteRole={setOpenDeleteRole}
        setSelectedRole={setSelectedRole}
      />
      <AddRole
        open={openAddRole}
        onClose={() => setOpenAddRole(false)}
        rolePermissions={rolePermissions}
        selectedPermissions={selectedPermissions}
        setSelectedPermissions={setSelectedPermissions}
        createTeamRole={createTeamRole}
        roleName={roleName}
        setRoleName={setRoleName}
        customRole={customRole}
        setCustomRole={setCustomRole}
        resetCustomRole={resetCustomRole}
        onBack={handleBackBtn}
        error={error}
        setError={setError}
      />
      <AddMember
        open={openAddMember}
        onClose={() => {
          setOpenAddMember(false);
          setNewMember({
            email: '',
            team_role_id: '',
            all_knowledge_bases: false,
            allowed_kbs: [],
          });
        }}
        onSubmit={createNewMember}
        newMember={newMember}
        setNewMember={setNewMember}
        roles={roles?.results}
        setOpenAddRole={setOpenAddRole}
        loading={isAddMemberLoading}
        setSelectedRole={setSelectedRole}
        setOpenEditRole={setOpenEditRole}
        setOpenDeleteRole={setOpenDeleteRole}
        error={error}
        teamData={data?.results}
        teamOwner={selectedTeam?.owner_id}
      />
      <DConfirmationModal
        open={openDeleteMember}
        onClose={() => setOpenDeleteMember(false)}
        onConfirm={deleteTeamMember}
        title="Remove from Team"
        description="Are you sure you want to remove this member from the team?"
        confirmText="Remove"
        cancelText="Cancel"
        variantConfirm="danger"
      />
      <DAddSeatsModal
        open={openAddSeatsModal}
        onClose={() => setOpenAddSeatsModal(false)}
      />
      <EditRole
        open={openEditRole}
        onClose={() => setOpenEditRole(false)}
        onSubmit={handleEditTeamRole}
        role={selectedRole}
        error={error}
        setError={setError}
        selectedPermissions={selectedPermissions}
        setSelectedPermissions={setSelectedPermissions}
        changedValuesRolePermissions={changedValuesRolePermissions}
        setChangedValuesRolePermissions={setChangedValuesRolePermissions}
        selectedRole={selectedRole}
        setSelectedRole={setSelectedRole}
      />
      <DConfirmationModal
        open={openDeleteRole}
        onClose={() => setOpenDeleteRole(false)}
        onConfirm={deleteTeamRole}
        title="Delete Role"
        description="Are you sure you want to delete this role? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variantConfirm="danger"
      />
      <DConfirmationModal
        open={openResetCreditsModal}
        onClose={() => setOpenResetCreditsModal(false)}
        onConfirm={resetCredits}
        title="Reset Credits"
        description="Reseting credits will set the credits to 0 and the member will be able to use the credits from beginning. This action cannot be undone."
        confirmText="Reset"
        cancelText="Cancel"
      />
    </LayoutMain>
  );
};

export default TeamManagement;
