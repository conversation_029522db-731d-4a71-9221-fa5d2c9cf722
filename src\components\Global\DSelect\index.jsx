import { useEffect, useRef, useState } from 'react';
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';
import ChevronDownIcon from '../Icons/ChevronDownIcon';
import DTransition from '../DTransition';
import PlayIcon from '../Icons/PlayIcon';
import DButtonIcon from '../DButtonIcon';

const DSelect = ({
  options,
  children,
  selectedChild,
  listButtonClass,
  value,
  onChange,
  error,
  size = 'md',
  placeholder = 'Select an item',
  disabled = false,
  selectClassName,
  ...props
}) => {
  const listButton = useRef(null);
  const audioRef = useRef(new Audio());
  const [playing, setPlaying] = useState(null); // Track which option is playing
  const [buttonWidth, setButtonWidth] = useState('auto');
  const [selectedValue, setSelectedValue] = useState(value ?? '');

  useEffect(() => {
    if (listButton.current) {
      setButtonWidth(listButton.current.offsetWidth + 'px');
    }
  }, [listButton.current]);

  useEffect(() => {
    setSelectedValue(value ?? '');
  }, [value]);

  const playSound = (soundUrl, idx) => {
    if (playing === idx) {
      audioRef.current.pause();
      setPlaying(null);
    } else {
      audioRef.current.src = soundUrl;
      audioRef.current.play();
      setPlaying(idx);

      audioRef.current.onended = () => {
        setPlaying(null);
      };
    }
  };

  return (
    <>
      <Listbox
        value={selectedValue}
        onChange={(value) => {
          setSelectedValue(value);
          onChange(value);
        }}
      >
        <ListboxButton
          ref={listButton}
          className={`relative w-full cursor-pointer ${
            size === 'md' ? 'h-11 sm:leading-6' : 'h-8 leading-4'
          } cursor-default rounded-size1 bg-white py-1.5 pl-3 pr-10 text-left text-black border border-grey-5 sm:text-sm truncate ${listButtonClass} ${
            error ? 'border-red-500' : ''
          } `}
          data-testid={`d-select-${props.name || props.id || ''}`}
          {...props}
        >
          {selectedChild ||
            options?.find((option) => option.value === selectedValue)?.label ?
            selectedChild || options?.find((option) => option.value === selectedValue)?.label :
            placeholder}
          <ChevronDownIcon className="pointer-events-none absolute inset-y-0 right-0 ml-3 flex items-center pr-2 m-auto" />
        </ListboxButton>

        <ListboxOptions
          anchor="bottom"
          className={'border border-grey-10 shadow-lg rounded-size1 p-size1 z-[9999] bg-white !max-h-[300px] overflow-y-auto w-[var(--button-width)] '}
          style={{ zIndex: 9999 }}
        >

          {options && options.length > 0
            ? options.map((option, idx) => (
                <ListboxOption
                  key={idx}
                  value={option.value || option}
                  disabled={option.disabled}
                  className={`group relative select-none py-size1 text-black flex justify-between items-center px-size1 rounded-size1 ${
                    option.disabled ? 'cursor-not-allowed text-grey-50' : 'cursor-pointer hover:bg-grey-5'
                  } ${
                    selectedValue.value === (option.value || option) ? 'bg-grey-5' : ''
                  } ${selectClassName}`}
                  data-testid={`d-select-option-${option.value || option}`}
                >
                  <span className="text-sm font-medium tracking-tight w-max max-w-[99%] block truncate">
                    {option.label || option}
                  </span>

                  {/* Play Button */}
                  {option.soundUrl && (
                    <DButtonIcon
                      className="!size-7 !rounded-full hover:bg-gray-200"
                      variant={playing === idx ? 'dark' : 'outlined'}
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        playSound(option.soundUrl, idx);
                      }}
                    >
                      <PlayIcon
                        className={`h-4 w-4 ${
                          playing === idx ? 'text-white' : 'text-grey-50'
                        }`}
                      />
                    </DButtonIcon>
                  )}
                </ListboxOption>
              ))
            : children}
        </ListboxOptions>
      </Listbox>

      <DTransition show={error}>
        <p className="text-error">{error}</p>
      </DTransition>
    </>
  );
};

export default DSelect;
