import { useState, useEffect } from 'react';

export const useTimezone = () => {
  const [timezoneInfo, setTimezoneInfo] = useState({
    timezone: '',
    offset: 0,
  });

  useEffect(() => {
    const getTimezoneInfo = () => {
      try {
        // Get timezone name (e.g., "America/New_York")
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        
        // Get timezone offset in minutes
        const offset = new Date().getTimezoneOffset();
        
        setTimezoneInfo({
          timezone,
          offset,
        });
      } catch (error) {
        console.error('Error getting timezone information:', error);
        // Fallback to UTC if there's an error
        setTimezoneInfo({
          timezone: 'UTC',
          offset: 0,
        });
      }
    };

    getTimezoneInfo();
  }, []);

  return timezoneInfo;
}; 