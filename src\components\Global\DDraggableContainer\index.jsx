import { DndContext, closestCenter } from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from '@dnd-kit/sortable';
import SortableItem from '../SortableItem';

const DDraggableContainer = ({ items, setItems, ItemComponent, ...rest }) => {
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over?.id && over) {
      const oldIndex = items.findIndex((item) => (item.id || item.frontend_id) === active.id);
      const newIndex = items.findIndex((item) => (item.id || item.frontend_id) === over.id);

      const newItems = arrayMove(items, oldIndex, newIndex).map((item, index) => ({
        ...item,
        order: index
      }));

      setItems(newItems);
    }
  };


  return (
    <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
      {items?.length > 0 && (<SortableContext
        items={items?.map((item) => item?.id || item?.frontend_id) || []}
        strategy={verticalListSortingStrategy}
      >
        {items?.map((item) => (
          <SortableItem
            key={item?.id || item?.frontend_id}
            id={item?.id || item?.frontend_id}
            item={item}
            ItemComponent={ItemComponent}
            {...rest}
          />
          ))}
        </SortableContext>
      )}
    </DndContext>
  );
};

export default DDraggableContainer;
