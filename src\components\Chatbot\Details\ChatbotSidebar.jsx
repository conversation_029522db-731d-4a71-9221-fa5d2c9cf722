import React, { useEffect, useState } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';

import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { useUserStore } from '@/stores/user/userStore';
import { Transition } from '@headlessui/react';

import DButton from '../../Global/DButton';
import DNavLink from '../../Global/DNavLink';
import ChevronLeftIcon from '../../Global/Icons/ChevronLeftIcon';
import ChatbotActions from '../ChatbotActions';

import chatbotNavItems from './NavItems';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import { SidebarStepEnum } from './SidebarStepEnum';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import ShareChatbotIcon from '@/components/Global/Icons/ShareChatbotIcon';
import DModalShareChatbot from '../DModalShareChatbot';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import { LLM_MODEL_DEFAULT } from '@/constants';
import { deleteChatbot } from '@/services/chatbot.service';
import { getUserProfile } from '@/services/user.service';

const DChatbotSidebar = () => {
  const { id } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const setCurrentStep = useChatbotStore((state) => state.setCurrentStep);
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const setUser = useUserStore((state) => state.setUser);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [groups, setGroups] = useState({});
  const [showShareModal, setShowShareModal] = useState(false);

  useEffect(() => {
    const initialGroups = {};
    chatbotNavItems.forEach((item) => {
      if (item.type === 'group') {
        initialGroups[item.label] = true;
      }
    });
    setGroups(initialGroups);
  }, []);

  const hasPermission = (permission) => {
    if (Array.isArray(permission)) {
      return permission.some((perm) => checkTeamManagementPermission(perm));
    }
    return checkTeamManagementPermission(permission);
  };

  const handleDeleteChatbot = async () => {
    try {
      setDeleteLoading(true);
      const response = await deleteChatbot(id);
      if (response.status === 200) {
        setShowDeleteModal(false);
        const profileResponse = await getUserProfile();
        if(profileResponse.status === 200){
          setUser(profileResponse.data);
        }
        navigate('/');
      }
    } catch (error) {
      console.error('Error deleting AI Chatbot:', error);
      setDeleteLoading(false);
    } finally {
      setDeleteLoading(false);
    }
  };

  return (
    <div className="chatbot_nav_wrapper flex flex-col gap-size5 w-full md:w-[184px] md:h-full">
      <span className="hidden md:block text-xl font-medium tracking-tight">
        AI Chatbot
      </span>
      <div className="hidden md:block w-full h-px bg-grey-5"></div>

      {/* Main navigation section - this should flex-grow to fill available space */}
      <div className="flex md:flex-col gap-size1 overflow-y-auto no-scrollbar flex-grow">
        {chatbotNavItems.map((navItem) => {
          if (navItem.permission && !hasPermission(navItem.permission)) {
            return null;
          }

          if (navItem.type === 'group') {
            const visibleSubItems = navItem.items.filter((subItem) => {
              if (subItem.permission) {
                return hasPermission(subItem.permission);
              }
              return true;
            });

            if (visibleSubItems.length === 0) {
              return null;
            }

            return (
              <div className="flex flex-col gap-size3" key={navItem.label}>
                <header className="hidden md:flex flex-col gap-size1">
                  <div className="flex justify-between items-center gap-size5">
                    <h3>{navItem.label}</h3>
                    <div
                      className={[
                        'transition text-grey-20 duration-300',
                        groups[navItem.label] ? 'rotate-90' : 'rotate-[270deg]',
                      ].join(' ')}
                    >
                    </div>
                  </div>
                  <div className="w-full h-px bg-grey-5"></div>
                </header>
                <Transition show={true} appear>
                  <div className="flex md:flex-col gap-size1 transition duration-300 ease-in">
                    {visibleSubItems.map((subItem, index) => (
                      <DNavLink
                        key={index}
                        label={subItem.label}
                        icon={subItem.icon}
                        iconPlacement="pre"
                        active={location.pathname.endsWith(subItem.link)}
                        onClick={() => {
                          setCurrentStep(subItem.id);
                          navigate(`/chatbot/${id}${subItem.link}`);
                        }}
                      />
                    ))}
                  </div>
                </Transition>
              </div>
            );
          }

          const isActive =
            (navItem.id === SidebarStepEnum.OVERVIEW && location.pathname === `/chatbot/${id}`) ||
            (navItem.id === SidebarStepEnum.TABS && location.pathname === `/chatbot/${id}/tabs`) ||
            (navItem.id === SidebarStepEnum.INSIGHTS &&
              location.pathname.startsWith(`/chatbot/${id}/insights`)) ||
            (navItem.id === SidebarStepEnum.INTEGRATIONS &&
              location.pathname.startsWith(`/chatbot/${id}/integrations`)) ||
            (navItem.id === SidebarStepEnum.SAFETY && location.pathname === `/chatbot/${id}/safety`) ||
            (navItem.id === SidebarStepEnum.KNOWLEDGE && location.pathname === `/chatbot/${id}/knowledge`) ||
            (navItem.id === SidebarStepEnum.PERSONALITY && location.pathname === `/chatbot/${id}/personality`) ||
            (navItem.id === SidebarStepEnum.SET_UP && location.pathname === `/chatbot/${id}/setup`) ||
            (navItem.id === SidebarStepEnum.STYLING && location.pathname === `/chatbot/${id}/styling`) ||
            (navItem.id === SidebarStepEnum.POWER_UPS && location.pathname === `/chatbot/${id}/powerups`) ||
            (navItem.id === SidebarStepEnum.REALTIME_VOICE && location.pathname === `/chatbot/${id}/realtime-voice`);

          return (
            <DNavLink
              key={navItem.label}
              label={navItem.label}
              icon={navItem.icon}
              iconPlacement="pre"
              active={isActive}
              onClick={() => {
                setCurrentStep(navItem.id);
                navigate(`/chatbot/${id}${navItem.link}`);
              }}
            />
          );
        })}
      </div>

      {/* Bottom actions section - this should always stay at the bottom */}
      <div className="mt-auto md:flex flex-col gap-size1 hidden">
        <ChatbotActions 
          chatbotId={id}
          selectedChatbot={selectedChatbot}
          variant="desktop"
        />
      </div>

      <DModalShareChatbot
        kb_id={id}
        llm_model={selectedChatbot?.last_model_used?.value || LLM_MODEL_DEFAULT.value}
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
      />
      <DConfirmationModal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteChatbot}
        title="Delete a chatbot?"
        description="Are you sure you want to delete this chatbot? This action is irreversible."
        confirmText="Delete"
        cancelText="Cancel"
        variantConfirm="danger"
        loading={deleteLoading}
      />
    </div>
  );
};

export default DChatbotSidebar;
