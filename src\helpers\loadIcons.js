const loadIcon = async (name_icon) => {
  try {
    const module = await import(`../components/Global/Icons/${name_icon}.jsx`);


    // Check if the default export exists and is a valid component
    if (module?.default) {
      return module.default;
    }

    // Check if named export matches
    if (module?.[name_icon]) {
      return module[name_icon];
    }

    throw new Error(`Export not found for icon: ${name_icon}`);
  } catch (error) {
    console.error(`Failed to load icon for ${name_icon}:`, error);
    return null;
  }
};

export default loadIcon;