import CardAmexIcon from '@/components/Global/Icons/CardAmexIcon';
import CardDiscoverIcon from '@/components/Global/Icons/CardDiscoverIcon';
import CardDinersIcon from '@/components/Global/Icons/CardDinersIcon';
import CardJCBIcon from '@/components/Global/Icons/CardJCBIcon';
import CardMasterCardIcon from '@/components/Global/Icons/CardMasterCardIcon';
import CardUnknownIcon from '@/components/Global/Icons/CardUnknownIcon';
import CardVisaIcon from '@/components/Global/Icons/CardVisaIcon';

const listCards = [
  {
    name: 'american express',
    icon: CardAmexIcon,
  },
  {
    name: 'diners club',
    icon: CardDinersIcon,
  },
  {
    name: 'discover',
    icon: CardDiscoverIcon,
  },
  {
    name: 'jcb',
    icon: CardJCBIcon,
  },
  {
    name: 'mastercard',
    icon: CardMasterCardIcon,
  },
  {
    name: 'visa',
    icon: CardVisaIcon,
  },
  {
    name: 'unknown',
    icon: CardUnknownIcon,
  },
];

const findCardIcon = (name) => {
  if (!name) {
    return listCards.find((card) => card.name === 'unknown')?.icon;
  }

  const lower = name.toLowerCase();
  const foundCard = listCards.find(
    (card) => card.name.toLowerCase() === lower
  )?.icon;

  return (
    foundCard ||
    listCards.find((card) => card.name === 'unknown')?.icon
  );
};


export default findCardIcon;
