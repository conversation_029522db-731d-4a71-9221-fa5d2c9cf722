import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import VoiceBlock from '../VoiceBlock';
import LayoutMain from '@/layouts/LayoutMain';
import AddIcon from '@/components/Global/Icons/AddIcon';
import DanteFAQ from '@/components/Chatbot/DanteFAQ';
import EmptyVoice from '@/assets/voice/empty_voice.png';
import EmptyVoiceDark from '@/assets/voice/empty_voice_dark.png';
import EmptyDashboard from '@/assets/empty_dashboard.png';
import EmptyDashboardDark from '@/assets/empty_dashboard_dark.png';
import DButton from '@/components/Global/DButton';
import AddPhoneNumberForm from '../AddPhoneNumberForm';
import { deleteVoice } from '@/services/voice.service';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import BlobAnimationButton from '../BlobAnimationButton';
import { trackCreateVoiceClick } from '@/helpers/analytics';
import { useUserStore } from '@/stores/user/userStore';

const VoiceDashboard = ({ voices = [], phoneNumbers = [], refetchPhoneNumbers, refetchVoices }) => {
  const navigate = useNavigate();

  const [showChat, setShowChat] = useState(false);
  const [firstMessage, setFirstMessage] = useState('');
  const [isAddPhoneNumberFormOpen, setIsAddPhoneNumberFormOpen] = useState(false);
  const [isDeleteVoiceModalOpen, setIsDeleteVoiceModalOpen] = useState(false);
  const [voiceToDelete, setVoiceToDelete] = useState(null);
  const user = useUserStore((state) => state.user);

  const handleDeleteVoice = async (id) => {
    setVoiceToDelete(id);
    setIsDeleteVoiceModalOpen(true);
  }

  const handleConfirmDeleteVoice = async () => {
    try {
      const response = await deleteVoice(voiceToDelete);
      if(response.status === 200){
        refetchVoices();
        setIsDeleteVoiceModalOpen(false);
      }
    } catch (error) {
      console.error(error);

    }
  }

  return (
    <LayoutMain
      title={!showChat ? 'Manage AI Voice Agents' : ''}
      variant="transparent-minimal"
      boxMode={false}
    >
      <div className="absolute w-full h-full flex flex-col overflow-hidden">
        {voices?.results?.length > 0 && (
            <>
                {!showChat && (
                    <div className="flex flex-1 min-h-0">
                        <div className="flex-1 overflow-y-auto">
                        <div className="dashboard-grid_content grid-voice grid gap-size5">
                            <div className="dashboard-create_btn rounded-size1 overflow-hidden shadow-sm bg-white/50">
                              <BlobAnimationButton
                                onClick={() => {
                                  trackCreateVoiceClick({
                                    user_id: user?.id,
                                    email: user?.email,
                                  });
                                  navigate('/voice/create')
                                }}
                                className="w-[150px] h-full m-auto"
                                whiteBlob4={true}
                              />
                            </div>
                            {voices?.results?.map((voice, index) => (
                            <VoiceBlock
                                id={voice?.id}
                                key={index}
                                name={voice?.name}
                                chatbot={voice?.knowledge_base}
                                voice={voice?.text_to_voice_settings_key?.label}
                                nrOfConversations={voice?.nr_of_conversations}
                                phoneNumbers={voice?.phone_numbers}
                                onEditVoice={() => navigate(`/voice/${voice?.id}/edit`)}
                                onDeleteVoice={() => handleDeleteVoice(voice?.id)}
                                previewUrl={voice?.preview_url}
                            />
                            ))}
                        </div>
                        </div>
                    </div>
                )}
            </>
            )}
            {voices?.results?.length === 0 && (
            <div className='dashboard_voice_create_btn flex flex-1 min-h-0 flex-col gap-[48px] items-center justify-center'>
                {/* <img src={EmptyDashboard} alt="empty dashboard" className='max-w-[336px]' /> */}
                <div className='flex flex-col gap-size3 items-center w-full md:max-w-[400px] justify-center'>
                    {/* <p className='text-xl font-regular tracking-tight text-center'>No AI Voice Agents trained yet</p>
                    <p className='text-grey-75 text-base font-regular text-center'>Train your first AI Voice Agent by clicking the button below.</p> */}
                    <div className="w-64">
                      <BlobAnimationButton
                        onClick={() => {
                          trackCreateVoiceClick({
                            user_id: user?.id,
                            email: user?.email,
                          });
                          navigate('/voice/create')
                        }}
                        className="w-[200px] h-full m-auto"
                        whiteBlob4={true}
                      />
                    </div>
                </div>
            </div>
            )}
            {/* {phoneNumbers?.results?.length === 0 && (
                <div className='flex flex-1 min-h-0 flex-col gap-[48px] items-center justify-center'>
                    <img src={EmptyVoice} alt="empty voice" className='max-w-[336px]' />
                    <div className='flex flex-col gap-size3 items-center w-full md:max-w-[450px] justify-center'>
                        <p className='text-xl font-regular tracking-tight text-center'>Add a phone number to start</p>
                        <p className='text-grey-75 text-base font-regular text-center'>Before you create your first AI Voice Agent, you must assign a phone number that will be used to call the Agent. Create your custom phone number on <a href="https://www.twilio.com/" target="_blank" rel="noopener noreferrer" className="text-black underline font-medium">Twilio here</a>.</p>
                        <p className='text-grey-75 text-base font-regular text-center'>Read our
                          <a className='text-black underline font-medium' href="https://www.dante-ai.com/guides/step-1-set-up-a-twilio-phone-number" target="_blank" rel="noopener noreferrer">AI Voice Agents guide</a> for step-by-step instructions.</p>
                        <div className="flex items-center justify-center">
                          <DButton variant='grey' onClick={() => setIsAddPhoneNumberFormOpen(true)}>
                            <AddIcon />
                            Add a Number
                          </DButton>
                        </div>
                    </div>
                </div>
            )} */}
        {/* <DanteFAQ
          showChat={showChat}
          setShowChat={setShowChat}
          firstMessage={firstMessage}
          setFirstMessage={setFirstMessage}

        /> */}
        <AddPhoneNumberForm
          isOpen={isAddPhoneNumberFormOpen}
          onClose={() => setIsAddPhoneNumberFormOpen(false)}
          refetchPhoneNumbers={refetchPhoneNumbers}
        />
        <DConfirmationModal
          open={isDeleteVoiceModalOpen}
          onClose={() => setIsDeleteVoiceModalOpen(false)}
          onConfirm={handleConfirmDeleteVoice}
          title="Delete AI Voice Agent"
          description="Are you sure you want to delete this AI Voice Agent? This action cannot be undone."
          confirmText="Delete"
          cancelText="Cancel"
          variantConfirm="danger"
        />
      </div>
    </LayoutMain>
  );
};

export default VoiceDashboard;
