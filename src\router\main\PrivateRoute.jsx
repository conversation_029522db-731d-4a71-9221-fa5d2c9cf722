import { useEffect, useRef, useState } from 'react';
import { Navigate } from 'react-router-dom';

import refreshAccessToken from '@/application/auth/refreshAccessToken';
import { useUserStore } from '@/stores/user/userStore';
import { updateUserDataLayer } from '@/helpers/analytics';
import RouteChangeHandler from '@/components/RouteChangeHandler';

const PrivateRoute = ({ permission, children }) => {
  const { auth, user } = useUserStore();
  const [checkingSession, setCheckingSession] = useState(true);
  const shouldRequest = useRef(false);

  const validateToken = async () => {
    setCheckingSession(true);
    if (auth.access_token) {
      await refreshAccessToken();
    }
    setCheckingSession(false);
  };

  useEffect(() => {
    if (!shouldRequest.current) {
      validateToken();
      shouldRequest.current = true;
    }
  }, []);

  // Update dataLayer when authentication is confirmed
  useEffect(() => {
    if (!checkingSession && auth.access_token) {
      updateUserDataLayer(user, auth);
    }
  }, [checkingSession, auth, user]);

  if (checkingSession) {
    return null;
  }

  // Wrap children with RouteChangeHandler to close modals on route changes
  return auth.access_token ? (
    <>
      <RouteChangeHandler />
      {children}
    </>
  ) : (
    <Navigate to="/log-in" replace />
  );
};

export default PrivateRoute;
