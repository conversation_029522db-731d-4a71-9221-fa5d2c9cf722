import React, { useEffect, useState } from 'react';

import { IconGallery, IconItem } from '@storybook/blocks';

const IconsPreview = () => {
  const [icons, setIcons] = React.useState([]);

  useEffect(() => {
    const importIcons = async () => {
      const context = import.meta.glob('../*Icon.jsx');

      const iconModules = await Promise.all(
        Object.entries(context).map(async ([filePath, moduleImporter]) => {
          const module = await moduleImporter();
          return {
            component: module.default,
            name: filePath.split('/').pop().replace('.jsx', '')
          };
        })
      );

      setIcons(iconModules);
    };

    importIcons();
  }, []);

  return (
    <IconGallery>
      {icons.map(({ component: IconComponent, name }) => {
        return (
          <IconItem key={name} name={name}>
            <IconComponent />
          </IconItem>
        );
      })}
    </IconGallery>
  );
};

export default IconsPreview;
