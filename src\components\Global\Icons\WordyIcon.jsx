const WordyIcon = (props) => {
  return (
    <svg
      width="22"
      height="19"
      viewBox="0 0 22 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11 17.75C11 16.9544 10.6839 16.1913 10.1213 15.6287C9.55871 15.0661 8.79565 14.75 8 14.75H2C1.80109 14.75 1.61032 14.671 1.46967 14.5303C1.32902 14.3897 1.25 14.1989 1.25 14V2C1.25 1.80109 1.32902 1.61032 1.46967 1.46967C1.61032 1.32902 1.80109 1.25 2 1.25H8C8.79565 1.25 9.55871 1.56607 10.1213 2.12868C10.6839 2.69129 11 3.45435 11 4.25M11 17.75V4.25M11 17.75C11 16.9544 11.3161 16.1913 11.8787 15.6287C12.4413 15.0661 13.2044 14.75 14 14.75H20C20.1989 14.75 20.3897 14.671 20.5303 14.5303C20.671 14.3897 20.75 14.1989 20.75 14V2C20.75 1.80109 20.671 1.61032 20.5303 1.46967C20.3897 1.32902 20.1989 1.25 20 1.25H14C13.2044 1.25 12.4413 1.56607 11.8787 2.12868C11.3161 2.69129 11 3.45435 11 4.25M14.375 9.875H17.375M14.375 6.125H17.375"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default WordyIcon;
