import { useEffect, useRef, useState } from 'react';

import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';

import ChevronDownIcon from '../Icons/ChevronDownIcon';
import DTransition from '../DTransition';
import clsx from 'clsx';
import DInputBlock from '../DInput/DInputBlock';
import ValidationError from '../ValidationError';

const DSelectSearch = ({
  options,
  children,
  selectedChild,
  listButtonClass,
  value,
  onChange,
  error,
  props,
  size = 'md',
  showOtherOptions = false,
  label,
  name,
  required,
  hideError = false,
}) => {
  const listButton = useRef(null);
  const [buttonWidth, setButtonWidth] = useState('auto');
  const [selectedValue, setSelectedValue] = useState(value);

  useEffect(() => {
    if (listButton.current) {
      setButtonWidth(listButton.current.offsetWidth + 'px');
    }
  }, [listButton.current]);

  const [selectedPerson, setSelectedPerson] = useState('');
  const [query, setQuery] = useState('');

  const filteredPeople =
    query === ''
      ? options
      : options.filter((option) => {
          return option.label.toLowerCase().includes(query.toLowerCase());
        });

  useEffect(() => {
    setSelectedValue(value);
  }, [value]);

  return (
    <DInputBlock
      label={label}
      name={name}
      required={required}
      hideError={hideError}
    >
      <Combobox
        immediate
        value={selectedValue}
        onChange={(value) => {
          setSelectedValue(value);
          onChange(value);
        }}
        onClose={() => setQuery('')}
      >
        <div className="relative">
          <ComboboxInput
            onChange={(event) => setQuery(event.target.value)}
            className={clsx(
              'w-full rounded-size1 bg-white py-1.5 pl-3 pr-10 text-left text-black border border-grey-5 sm:text-sm',
              size === 'md' ? 'h-11 sm:leading-6' : 'h-8 leading-4'
            )}
            displayValue={(value) => {
              if (value === 'other') {
                return 'Other';
              }
              return options?.find((option) => option.value === value)?.label;
            }}
            data-testid={`d-select-search-input-${name ?? props.id ?? ''}`}
          />
          <ComboboxButton
            className={
              'absolute inset-y-0 right-0 ml-3 flex items-center pr-2 m-auto cursor-pointer'
            }
            // className={` cursor-pointer ${
            // } cursor-default   ${listButtonClass} ${
            //
            //   error ? 'border-red-500' : ''
            // } `}
            data-testid={`d-select-search-button-${name ?? props.id ?? ''}`}
          >
            {/* {selectedChild ||
            options?.find((option) => option.value === selectedValue)?.label} */}
            <ChevronDownIcon className="size-4" />
          </ComboboxButton>
        </div>
        <ComboboxOptions
          transition
          anchor="bottom"
          className={'border shadow-lg rounded-size1 py-size1 z-[9999] bg-white !max-h-[400px] overflow-y-auto w-[var(--input-width)] '}
          style={{ zIndex: 9999 }}
          data-testid={`d-select-search-options-${name ?? props.id ?? ''}`}
        >
          {filteredPeople && filteredPeople.length > 0
            ? filteredPeople.map((option, idx) => (
                <ComboboxOption
                  key={idx}
                  value={option.value || option}
                  className="group relative cursor-pointer select-none px-size1 pl-size3 py-2 text-black hover:bg-grey-5"
                >
                  <span className="text-sm font-medium tracking-tight w-max max-w-[99%] block truncate">
                    {option.label || option}
                  </span>
                </ComboboxOption>
              ))
            : children}
          {showOtherOptions && (
            <ComboboxOption
              value="other"
              className="group relative cursor-pointer select-none px-size1 pl-size3 py-2 text-black hover:bg-grey-5"
            >
              <span className="text-sm font-medium tracking-tight w-max max-w-[99%] block truncate">
                Other
              </span>
            </ComboboxOption>
          )}
        </ComboboxOptions>
      </Combobox>

      {!hideError && <ValidationError error={error} />}
    </DInputBlock>
  );
};

export default DSelectSearch;
