import * as React from 'react';
const CustomizeIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.252 3.9a1.07 1.07 0 0 0-.757.313l-8.903 8.903c-.26.26-.337.341-.391.43a1 1 0 0 0-.119.284c-.024.103-.027.216-.027.583v.753h.754c.366 0 .48-.003.581-.027q.153-.037.286-.118c.088-.054.17-.132.43-.392l8.903-8.903a1.071 1.071 0 0 0-.757-1.826m-1.37-.3a1.937 1.937 0 1 1 2.74 2.739l-8.904 8.903-.032.032c-.214.214-.37.371-.558.486a1.9 1.9 0 0 1-.533.221h-.002c-.214.052-.436.052-.738.052H3.622a.433.433 0 0 1-.433-.434v-1.231c0-.304 0-.525.05-.74q.07-.285.223-.535c.114-.187.271-.344.485-.557l.033-.033zm3.788 10.262a.433.433 0 0 1 .028.613l-.707.773a2.36 2.36 0 0 1-1.735.785h-.004c-.66 0-1.284-.287-1.737-.783l-.003-.003a1.5 1.5 0 0 0-1.097-.497 1.5 1.5 0 0 0-1.096.5.433.433 0 1 1-.638-.585 2.36 2.36 0 0 1 1.736-.781h.004a2.36 2.36 0 0 1 1.735.783c.297.324.692.499 1.094.5a1.5 1.5 0 0 0 1.096-.498l.003-.004.71-.775a.433.433 0 0 1 .611-.028"
      fill="currentColor"
    />
  </svg>
);
export default CustomizeIcon;
