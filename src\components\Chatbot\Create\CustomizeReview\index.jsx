import DAlert from '@/components/Global/DAlert';

const CustomizeReview = ({ customizationData }) => {
  return (
    <div className="flex flex-col gap-size6">
      <DAlert state="positive" className='!w-full'>
        <p className="text-sm font-medium tracking-tight">
          {customizationData?.kb_name} is ready! Review customization details before finalizing the creation process.
        </p>
      </DAlert>
      <div className="flex flex-col gap-size5">
        <div className="flex flex-col gap-size2">
          <p className="text-base font-medium tracking-tight">Personality</p>
          <p className="text-xs font-regular tracking-tight">{customizationData?.base_system_prompt}</p>
          <DAlert state="info" className='!w-full'>
            <p className="text-xs font-regular tracking-tight">
              {customizationData?.open_to_internet_knowledge ? 'AI Chatbot is allowed access to internet.' : 'AI Chatbot is not allowed access to internet.'}
            </p>
          </DAlert>
        </div>
        <div className="w-full h-[1px] bg-grey-5" />
        <div className="flex flex-col gap-size2">
          <p className="text-base font-medium tracking-tight">Power Ups</p>
          <DAlert state="info" className='!w-full'>
            <p className="text-xs font-regular tracking-tight">{customizationData?.talk_to_live_agent ? 'Human handover is enabled.' : 'Human handover is disabled.'}</p>
          </DAlert>
          <DAlert state="info" className='!w-full'>
            <p className="text-xs font-regular tracking-tight">{customizationData?.data_collection ? 'Lead gen form is enabled.' : 'Lead gen form is disabled.'}</p>
          </DAlert>
          <DAlert state="info" className='!w-full'>
            <p className="text-xs font-regular tracking-tight">{customizationData?.calendly_integration_enabled ? 'Calendar link is enabled.' : 'Calendar link is disabled.'}</p>
          </DAlert>
        </div>
        <div className="w-full h-[1px] bg-grey-5" />
        <div className="flex flex-col gap-size2">
          <p className="text-base font-medium tracking-tight">Home</p>
          <DAlert state="info" className='!w-full'>
            <p className="text-xs font-regular tracking-tight">{customizationData?.home_tab_enabled ? 'Home tab is enabled.' : 'Home tab is disabled.'}</p>
          </DAlert>
        </div>
      </div>
    </div>
  );
};

export default CustomizeReview;
