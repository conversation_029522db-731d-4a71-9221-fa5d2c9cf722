const OpenNavIcon = (props) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.79054 19.8C4.79054 20.1314 4.52399 20.4 4.19523 20.4C3.86647 20.4 3.59999 20.1314 3.59999 19.8V4.19985C3.59999 3.86848 3.86647 3.59985 4.19523 3.59985C4.52399 3.59985 4.79054 3.86848 4.79054 4.19985V19.8ZM20.2257 11.5756C20.4581 11.8099 20.4581 12.1899 20.2257 12.4242L15.0666 17.6243C14.8341 17.8586 14.4572 17.8586 14.2247 17.6243C13.9923 17.39 13.9923 17.01 14.2247 16.7757L18.3676 12.6H7.76688C7.43812 12.6 7.17164 12.3314 7.17164 12C7.17164 11.6686 7.43812 11.4 7.76688 11.4H18.3676L14.2247 7.22417C13.9923 6.98986 13.9923 6.6099 14.2247 6.37559C14.4572 6.14127 14.8341 6.14127 15.0666 6.37559L20.2257 11.5756Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default OpenNavIcon;
