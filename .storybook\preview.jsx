import { background } from 'storybook/internal/theming';
import '../src/entrypoints/main/index.css';
import { INITIAL_VIEWPORTS } from '@storybook/addon-viewport';

import { withThemeByClassName } from '@storybook/addon-themes';
import { MemoryRouter } from 'react-router-dom';

/** @type { import('@storybook/addon-viewport').ViewportMap } */
const danteViewports = {
  figmaDesktop: {
    name: 'Figma Desktop',
    styles: {
      width: '1440px',
      height: '960px',
    },
  },
  figmaMobile: {
    name: 'Figma Mobile',
    styles: {
      width: '375px',
      height: '800px',
    },
  },
  macbookAir13: {
    name: 'MacBook Air 13',
    styles: {
      width: '2560px',
      height: '1664px',
    },
  },
  laptopHd: {
    name: 'Laptop HD',
    styles: {
      width: '1366px',
      height: '768px',
    },
  },
  laptopFullHd: {
    name: 'Laptop FullHD',
    styles: {
      width: '1920px',
      height: '1080px',
    },
  },
};

/** @type { import('@storybook/react').Preview } */
const preview = {
  globalTypes: {
    theme: { type: 'string' },
  },

  parameters: {
    backgrounds: {
      default: 'grey',
      values: [
        {
          name: 'grey',
          value: '#f8f8f8',
        },
        {
          name: 'white',
          value: '#fff',
        },
        {
          name: 'transparent',
          value: 'transparent',
        },
      ],
    },
    viewport: {
      viewports: {
        ...danteViewports,
        ...INITIAL_VIEWPORTS,
      },
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },

  decorators: [
    (Story) => (
      <MemoryRouter initialEntries={['/']}>
        <div className="bg-grey-100 text-black">
          <Story />
        </div>
      </MemoryRouter>
    ),
    withThemeByClassName({
      themes: {
        // nameOfTheme: 'classNameForTheme',
        light: '',
        dark: 'dark',
      },
      defaultTheme: 'light',
    }),
  ],
};

export default preview;
