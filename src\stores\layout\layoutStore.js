import { create } from 'zustand';
import { createJSONStorage, devtools } from 'zustand/middleware';

const initialState = {
  layoutTitle: '',
  isDarkMode: false,
  sidebarOpen: true,
  progressBar: [],
  welcomeModal: {
    isOpen: false,
    show: false
  },
  previewBubblePage: {
    inPage: false,
    isOpen: false,
  },
  planModal: {
    isOpen: false,
    show: false
  }
};

const useLayoutStore = create(
  devtools(
    (set) => ({
      ...initialState, // Used to check if the page has a bubble preview
      setWelcomeModal: (open) => set((state) => ({ welcomeModal: { ...state.welcomeModal, ...open } })),
      setPreviewBubblePage: (preview) => set({ previewBubblePage: preview }),
      setIsInPreviewBubblePage: (inPage) => set((state) => ({ previewBubblePage: { ...state.previewBubblePage, inPage } })),
      setLayoutTitle: (title) => set({ layoutTitle: title }),
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      setIsDarkMode: (isDark) => set({ isDarkMode: isDark }),
      setProgressBar: (update) =>
        set((state) => ({
          progressBar: typeof update === 'function' ? update(state.progressBar) : update
        })),
      completeAllSteps: () =>
        set((state) => ({
          progressBar: state.progressBar.map((step) => ({
            ...step,
            completed: true,
            pending: false,
            active: false
          }))
        })),
      setPlanModal: (data) => set((state) => ({ planModal: { ...state.planModal, ...data } })),
      reset: () => set({ ...initialState })
    }),
    {
      name: 'layout-store', // Name of the item in storage
      storage: createJSONStorage(() => localStorage) // Specify storage
    }
  )
);

export default useLayoutStore;
