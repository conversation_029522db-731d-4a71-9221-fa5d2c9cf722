import path from 'path';
import { fileURLToPath } from 'url';
import { defineConfig } from 'vite';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
import eslint from 'vite-plugin-eslint';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

export default defineConfig({
  plugins: [cssInjectedByJsPlugin({ styleId: 'dante-embed-styles' }), eslint()],
  build: {
    emptyOutDir: false,
    chunkSizeWarningLimit: 10,
    rollupOptions: {
      input: {
        index: path.resolve(dirname, 'bubble-embed/src/index.js')
      },
      output: [
        {
          manualChunks: undefined,
          entryFileNames: 'bubble-embed.js'
        },
        {
          manualChunks: undefined,
          entryFileNames: 'bubble-embed.min.js'
        }
      ]
    },
    outDir: './public'
  },
  publicDir: 'assets'
});
