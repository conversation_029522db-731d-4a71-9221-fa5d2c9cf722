import { useState, useEffect, useRef } from 'react';
import { Transition } from '@headlessui/react';
import DLoading from '@/components/DLoading';
import Bubble from '@/components/Bubble';
import StyleTag from '@/components/StyleTag';
import { DANTE_THEME_CHAT } from '@/constants';
import clsx from 'clsx';

/**
 * BubbleLoadingWrapper - A component that wraps the Bubble component and handles loading states
 * 
 * This component ensures that the Bubble is only rendered when all customization data is fully loaded,
 * preventing premature rendering with default values.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.config - Configuration object for the Bubble component
 * @param {boolean} props.isLoading - Whether the customization data is still loading
 * @param {Object} props.customizationData - The customization data for the Bubble
 * @param {string} props.customizationFontFamily - Font family import URL
 * @param {boolean} props.isPreviewMode - Whether the Bubble is in preview mode
 * @param {boolean} props.isInApp - Whether the Bubble is rendered within the app
 * @param {boolean} props.initialShouldFetchCustomization - Whether to fetch customization data initially
 * @param {Object} props.slots - Slots for the Bubble component
 * @param {boolean} props.isVoiceMode - Whether the Bubble is in voice mode
 * @returns {JSX.Element} The wrapped Bubble component with loading state handling
 */
const BubbleLoadingWrapper = ({
  config,
  isLoading = false,
  customizationData = {},
  customizationFontFamily,
  isPreviewMode = false,
  isInApp = false,
  initialShouldFetchCustomization = false,
  slots,
  isVoiceMode = false,
  ...rest
}) => {
  const [loading, setLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [mergedConfig, setMergedConfig] = useState(null);
  const timeoutRef = useRef(null);

  // Effect to handle loading state based on isLoading prop and config availability
  useEffect(() => {
    // Clear any existing timeout to prevent memory leaks
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // If we're still loading or don't have required config data, maintain loading state
    if (isLoading || !config) {
      setLoading(true);
      return;
    }

    // Merge the config with customization data
    const mergedData = {
      ...config,
      ...(customizationData || {})
    };

    // Check if we have all required customization properties
    const hasRequiredCustomization = 
      mergedData.brand_color && 
      mergedData.surface_color && 
      mergedData.element_color;

    // If we have all required customization, set the merged config and stop loading
    if (hasRequiredCustomization) {
      setMergedConfig(mergedData);
      
      // Add a small delay to ensure smooth transition
      timeoutRef.current = setTimeout(() => {
        setLoading(false);
      }, 300);
    } else if (!isPreviewMode) {
      // If we're missing required customization and not in preview mode, set error state
      setHasError(true);
      
      // Use fallback values after a delay
      timeoutRef.current = setTimeout(() => {
        setMergedConfig({
          ...mergedData,
          brand_color: mergedData.brand_color || DANTE_THEME_CHAT.brand_color,
          surface_color: mergedData.surface_color || DANTE_THEME_CHAT.surface_color,
          element_color: mergedData.element_color || DANTE_THEME_CHAT.element_color,
        });
        setLoading(false);
      }, 500);
    } else {
      // In preview mode, we can proceed with whatever data we have
      setMergedConfig(mergedData);
      
      timeoutRef.current = setTimeout(() => {
        setLoading(false);
      }, 300);
    }

    // Cleanup function to clear timeout
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isLoading, config, customizationData, isPreviewMode]);

  return (
    <>
      {/* Apply customization styles */}
      {mergedConfig && (
        <StyleTag 
          tag=".bubble" 
          tempCustomizationData={mergedConfig} 
          customImport={customizationFontFamily} 
        />
      )}

      {/* Loading state */}
      <Transition
        show={loading}
        enter="transition-opacity duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="transition-opacity duration-300"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className={clsx(
          'flex flex-col w-full h-full items-center justify-center',
          'transition-all duration-300'
        )}>
          <DLoading show={true} />
        </div>
      </Transition>

      {/* Bubble component */}
      <Transition
        show={!loading && !!mergedConfig}
        enter="transition-opacity duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="transition-opacity duration-300"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className={clsx(
          'w-full h-full',
          'transition-all duration-300'
        )}>
          <Bubble
            config={mergedConfig}
            isPreviewMode={isPreviewMode}
            isInApp={isInApp}
            initialShouldFetchCustomization={initialShouldFetchCustomization}
            slots={slots}
            isVoiceMode={isVoiceMode}
            {...rest}
          />
        </div>
      </Transition>
    </>
  );
};

export default BubbleLoadingWrapper;
