const OpenAiLogoIcon = (props) => {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.42911 2.09297C2.84142 0.845041 4.02454 3.2249e-05 5.35629 3.2249e-05C6.22595 -0.00393509 7.05624 0.358314 7.63839 0.996236L7.64311 0.994516C8.94435 0.722941 10.2774 1.31162 10.9433 2.44984C11.3816 3.19114 11.4789 4.08189 11.2102 4.8984L11.214 4.90154C12.1029 5.8779 12.2529 7.31159 11.587 8.44981C11.1557 9.19509 10.4226 9.72359 9.57173 9.90218L9.57089 9.90703C9.15858 11.155 7.97546 12 6.64371 12C5.77405 12.0039 4.94376 11.6417 4.36161 11.0038L4.35689 11.0055C3.05565 11.2771 1.72258 10.6884 1.0567 9.55016C0.618391 8.80886 0.52113 7.91811 0.789841 7.1016L0.786006 7.09846C-0.102929 6.1221 -0.252883 4.68841 0.412993 3.55019C0.844349 2.80491 1.57739 2.27641 2.42827 2.09782L2.42911 2.09297ZM3.08202 5.70867L3.08156 3.03646C3.08125 2.77786 3.12707 2.52247 3.21436 2.28072L3.20659 2.27887C3.52005 1.40674 4.36372 0.781557 5.35629 0.779989L5.35225 0.783972C5.88585 0.783972 6.3988 0.964839 6.81015 1.29973L4.32429 2.71614C4.29575 2.7324 4.26987 2.75156 4.24692 2.77315C4.16591 2.84628 4.11937 2.95143 4.11937 3.0641V6.29877L3.08202 5.70867ZM2.28046 2.95745V5.7903C2.28046 5.8225 2.28427 5.85388 2.29159 5.884C2.31506 5.99018 2.38417 6.0829 2.48332 6.13939L5.32182 7.75672L4.28536 8.34825L1.94018 7.01252C1.71309 6.88349 1.5119 6.71664 1.34339 6.52115L1.33786 6.52689C0.729284 5.82293 0.602518 4.78927 1.09742 3.94017L1.0989 3.94561C1.3657 3.48955 1.7809 3.14157 2.28046 2.95745ZM5.99996 7.37281L4.79534 6.68643V5.31362L6.00004 4.62719L7.20466 5.31357V6.68638L5.99996 7.37281ZM1.47029 7.65774L3.95617 9.07416C3.98507 9.09063 4.01521 9.10327 4.04603 9.11211C4.1504 9.14433 4.26545 9.13142 4.36395 9.07529L7.20245 7.45796L7.2033 8.63958L4.85861 9.97606C4.63184 10.1056 4.38483 10.1942 4.12904 10.2404L4.13131 10.248C3.20926 10.4162 2.2388 10.0077 1.74113 9.16018L1.74665 9.16163C1.47985 8.70559 1.38209 8.17676 1.47029 7.65774ZM6.64775 11.216C6.11415 11.216 5.6012 11.0352 5.18985 10.7003L7.67571 9.28386C7.70425 9.2676 7.73013 9.24843 7.75308 9.22684C7.83409 9.15372 7.88063 9.04857 7.88063 8.9359V5.70123L8.91798 6.29133L8.91844 8.96354C8.91875 9.22214 8.87293 9.47753 8.78564 9.71928L8.79341 9.72113C8.47995 10.5933 7.63628 11.2184 6.64371 11.22L6.64775 11.216ZM9.71954 9.04255V6.2097C9.71954 6.1775 9.71574 6.14612 9.70842 6.116C9.68494 6.00982 9.61583 5.9171 9.51668 5.86061L6.67818 4.24327L7.71464 3.65175L10.0598 4.98748C10.2869 5.11651 10.4881 5.28336 10.6566 5.47885L10.6621 5.47311C11.2707 6.17707 11.3975 7.21073 10.9026 8.05983L10.9011 8.05439C10.6343 8.51045 10.2191 8.85843 9.71954 9.04255ZM10.2534 2.83837C10.5201 3.29441 10.6179 3.82324 10.5297 4.34226L8.04383 2.92584C8.01493 2.90937 7.9848 2.89673 7.95398 2.88789C7.84961 2.85567 7.73455 2.86858 7.63605 2.92471L4.79755 4.54204L4.7967 3.36042L7.14139 2.02394C7.36816 1.89437 7.61517 1.80583 7.87096 1.75956L7.86869 1.75196C8.79074 1.58379 9.7612 1.99227 10.2589 2.83982L10.2534 2.83837Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default OpenAiLogoIcon;
