import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import Bubble from '@/components/Bubble';
import DButton from '@/components/Global/DButton';
import { COMMON_CLASSNAMES } from '@/constants';
import useCustomizationData from '@/hooks/useCustomization';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import useLayoutStore from '@/stores/layout/layoutStore';
import ReactRouterPrompt from 'react-router-prompt';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import compareObjects from '@/helpers/compareObjects';
import DLoading from '@/components/DLoading';
import { updateChatbotPowerUps } from '@/services/customization.service';
import useToast  from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { trackKlaviyoEvent } from '@/services/chatbot.service';
import StyleTag from '@/components/StyleTag';
import DSwitchAccordion from '@/components/Global/DSwitchAccordion';
import NewBadge from '@/components/Global/NewBadge';
import VoiceSelector from '@/components/Voice/VoiceSelector';
import { getTextToVoiceOptions, getVoices } from '@/services/voice.service';
import DTooltip from '@/components/Global/DTooltip';
import DCheckbox from '@/components/Global/DCheckbox';
import DInput from '@/components/Global/DInput/DInput';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
import featureCheck from '@/helpers/tier/featureCheck';

const ChatbotRealtimeVoice = () => {
  let params = useParams();
  const navigate = useNavigate();
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );
  const { addSuccessToast } = useToast();

  const { customizationData, setChatbotCustomization, savingChanges } =
    useCustomizationData(true, params.id);

  const [tempCustomization, setTempCustomization] = useState(customizationData);
  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [aiVoiceOptions, setAiVoiceOptions] = useState([]);

  const [changedData, setChangedData] = useState({});

  const handleSave = async () => {
    setIsSaveLoading(true);
    try{
      const res = await updateChatbotPowerUps(params.id, changedData);
      if(res.status === 200){
        // Track customized-powerup event
        const user = useUserStore.getState().user;
        await trackKlaviyoEvent('customized-powerup', { 
          chatbot_id: params.id
        });
        setChatbotCustomization(res.data);
        setTempCustomization(res.data);
        addSuccessToast({
          message: 'Realtime voice updated successfully',
        });
        setIsSaveLoading(false);
      }
    }catch(e){
      setIsSaveLoading(false);
      console.log('error', e)
    }
  };

  const updateCustomizationData = (key, value) => {
    setTempCustomization((prev) => ({ ...prev, [key]: value }))
    setChangedData((prev) => ({ ...prev, [key]: value }))
  }

  useEffect(() => {
    const getTextToVoice = async () => {
      try {
        const res = await getTextToVoiceOptions();
        if(res.status === 200) {

          const processedVoices = res.data.results.map((voice) => {
            let provider = 'Other';

            if (voice.voice_type) {
              switch(voice.voice_type.toLowerCase()) {
                case 'openai':
                  provider = 'OpenAI';
                  break;
                case 'elevenlabs':
                  provider = 'ElevenLabs';
                  break;
                case 'cartesia':
                  provider = 'Cartesia';
                  break;
                default:
                  provider = 'Other';
              }
            }

            return {
              label: voice.name,
              value: voice.id, 
              voice_value: voice.id, 
              soundUrl: voice.preview_url,
              provider: provider,
              description: voice.description || '',
              voice_type: voice.voice_type
            };
          });

          setAiVoiceOptions(processedVoices);
        }
      } catch (err) {
        console.error('Error fetching provider voices:', err);
        setAiVoiceOptions([]);
      }
    };

    getTextToVoice();
  }, []);
  

  useEffect(() => {
    setSidebarOpen(false);
    setProgressBar([]);
    setIsInPreviewBubblePage(true);
  }, []);

  useEffect(() => {
    setTempCustomization(customizationData);
  }, [customizationData]);

  useEffect(() => {
    if (!tempCustomization || !customizationData) {
      return;
    }

    const hasUnsavedChanges = !compareObjects(
      customizationData,
      tempCustomization
    );

    setUnsavedChanges(hasUnsavedChanges);
  }, [tempCustomization]);

  if (!customizationData || !tempCustomization?.kb_id) {
    return <DLoading show={true} />;
  }

  return (
    <>
      <LayoutRightSidebar
        RightSidebar={() => (
          <div className={COMMON_CLASSNAMES.previewBubble}>
            <StyleTag tag=".bubble" tempCustomizationData={tempCustomization} />
            <Bubble
              type="chatbot"
              config={{
                ...tempCustomization,
                public: true,
                home_tab_enabled: false,
                remove_watermark: true,
              }}
              isPreviewMode={true}
              isInApp={false}
            />
          </div>
        )}
      >
        {() => (
            <LayoutWithButtons
              footer={
                <div className="flex items-center justify-between">
                  <DButton
                    variant="grey"
                    className="!h-12 w-full md:w-auto md:min-w-32 xl:!min-w-52"
                    onClick={() => navigate(`/chatbot/${params.id}`)}
                  >
                    Cancel
                  </DButton>
                  <DButton
                    variant="dark"
                    className="!h-12 w-full md:w-auto md:min-w-32 xl:!min-w-52"
                    onClick={handleSave}
                    loading={isSaveLoading}
                    disabled={compareObjects(
                      customizationData,
                      tempCustomization
                    )}
                  >
                    Save
                  </DButton>
                </div>
              }
            >
                <DSwitchAccordion
                    title="Realtime AI Voice"
                    extraContent={<NewBadge className="h-5"/>}
                    tooltip={true}
                    tooltipContent="Chat in realtime with an AI voice agent. Offer your customers a personalized experience with a real-time voice agent."
                    switchOpen={tempCustomization.realtime_voice_access}
                    onToggle={(value) => {
                    if (featureCheck('realtime_voice_access')) {
                        updateCustomizationData('realtime_voice_access', value)
                    }
                    }}
                >
                    <div className='flex flex-col gap-size3'>
                        <VoiceSelector
                            selectedVoice={tempCustomization?.ai_voice_agent_id}
                            onVoiceChange={(value) => {
                                updateCustomizationData('ai_voice_agent_id', value)
                            }}
                            voices={aiVoiceOptions}
                        />
                        <div className="flex flex-col gap-size1">
                            <span className="text-base font-medium tracking-tight flex items-center gap-size1">
                            Voice tooltip text
                            <DTooltip content="Customize the tooltip text that appears when users hover over the voice feature">
                                <InfoIcon className="size-3" />
                            </DTooltip>
                            </span>
                            <DInput
                            value={tempCustomization?.voice_tooltip_text || ''}
                            onChange={(e) =>
                                updateCustomizationData('voice_tooltip_text', e.target.value)
                            }
                            placeholder="Enter tooltip text"
                            />
                        </div>
                        <div className="flex items-center gap-size2">
                            <DCheckbox
                            checked={tempCustomization?.show_voice_tooltip}
                            onChange={(checked) =>
                                updateCustomizationData('show_voice_tooltip', checked)
                            }
                            label="Show voice tooltip"
                            />
                        </div>
                    </div>
                </DSwitchAccordion>
              <ReactRouterPrompt when={unsavedChanges}>
                {({ isActive, onConfirm, onCancel }) => (
                  <DConfirmationModal
                    open={isActive}
                    onClose={onCancel}
                    onConfirm={onConfirm}
                    title="Are you sure you want to leave this page?"
                    description="You have unsaved changes. If you leave, you will lose your changes."
                    confirmText="Leave"
                    cancelText="Cancel"
                    variantConfirm="danger"
                  />
                )}
              </ReactRouterPrompt>
            </LayoutWithButtons>
        )}
      </LayoutRightSidebar>
    </>
  );
};

export default ChatbotRealtimeVoice;
