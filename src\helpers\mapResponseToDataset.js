// Utility function to map API response to chart dataset format
function getGradient(ctx, chartArea, color) {
  let width, height, gradient;
  const chartWidth = chartArea.right - chartArea.left;
  const chartHeight = chartArea.bottom - chartArea.top;
  if (!gradient || width !== chartWidth || height !== chartHeight) {
    width = chartWidth;
    height = chartHeight;
    gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
    gradient.addColorStop(0, `rgba(${color}, 0)`);

    gradient.addColorStop(1, `rgba(${color}, 0.5)`);
  }

  return gradient;
}

const mapApiResponseToDataset = (apiData, color) => {
  const dates = Object.keys(apiData).sort();
  const data = dates.map((date) => apiData[date]);

  return {
    data: data,
    label: '',
    borderColor: `rgba(${color}, 1)`,
    borderWidth: 1,
    backgroundColor: (context) => {
      const chart = context.chart;
      const { ctx, chartArea } = chart;
      if (!chartArea) return;
      return getGradient(ctx, chartArea, color);
    },
    fill: true,
    pointStyle: false
  };
};

export default mapApiResponseToDataset;
