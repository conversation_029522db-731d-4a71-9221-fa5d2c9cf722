const CircledCheck = (props) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 3.125C8.17664 3.125 6.42795 3.84933 5.13864 5.13864C3.84933 6.42795 3.125 8.17664 3.125 10C3.125 10.9028 3.30283 11.7968 3.64833 12.6309C3.99383 13.4651 4.50024 14.223 5.13864 14.8614C5.77704 15.4998 6.53494 16.0062 7.36905 16.3517C8.20316 16.6972 9.09716 16.875 10 16.875C10.9028 16.875 11.7968 16.6972 12.6309 16.3517C13.4651 16.0062 14.223 15.4998 14.8614 14.8614C15.4998 14.223 16.0062 13.4651 16.3517 12.6309C16.6972 11.7968 16.875 10.9028 16.875 10C16.875 8.17664 16.1507 6.42795 14.8614 5.13864C13.572 3.84933 11.8234 3.125 10 3.125ZM4.25476 4.25476C5.77849 2.73102 7.84512 1.875 10 1.875C12.1549 1.875 14.2215 2.73102 15.7452 4.25476C17.269 5.77849 18.125 7.84512 18.125 10C18.125 11.067 17.9148 12.1235 17.5065 13.1093C17.0982 14.0951 16.4997 14.9908 15.7452 15.7452C14.9908 16.4997 14.0951 17.0982 13.1093 17.5065C12.1235 17.9148 11.067 18.125 10 18.125C8.93301 18.125 7.87647 17.9148 6.8907 17.5065C5.90493 17.0982 5.00923 16.4997 4.25476 15.7452C3.50028 14.9908 2.9018 14.0951 2.49348 13.1093C2.08516 12.1235 1.875 11.067 1.875 10C1.875 7.84512 2.73102 5.77849 4.25476 4.25476ZM12.8633 7.61642C13.1442 7.81705 13.2092 8.20739 13.0086 8.48827L9.88358 12.8633C9.7768 13.0128 9.60964 13.1077 9.42655 13.1229C9.24346 13.138 9.06297 13.0719 8.93306 12.9419L7.05806 11.0669C6.81398 10.8229 6.81398 10.4271 7.05806 10.1831C7.30214 9.93898 7.69786 9.93898 7.94194 10.1831L9.29525 11.5364L11.9914 7.76173C12.192 7.48084 12.5824 7.41579 12.8633 7.61642Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default CircledCheck;
