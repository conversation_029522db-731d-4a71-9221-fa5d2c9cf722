import { data<PERSON>hart } from '@/helpers/stories/generateDateChart';
import { fn } from '@storybook/test';

import DChatbotBlock from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Chatbot/Block',
  component: DChatbotBlock,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    image: {
      control: { type: 'text' }
    },
    name: {
      control: { type: 'text' }
    },
    status: {
      options: ['Active', 'Paused'],
      control: { type: 'radio' }
    }
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {
    image: 'https://chat.dante-ai.com/btn-embed.png',
    name: 'Long chatbot name example',
    status: 'Active',
    infos: {
      avatars: {
        value: 0,
        status: 'active'
      },
      flagged_messages: {
        value: 0,
        status: 'active'
      },
      active_integrations: {
        value: 0,
        status: 'active'
      }
    },
    stats: [...dataChart]
  }
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const DefaultChatbot = {
  args: {
    image: 'https://chat.dante-ai.com/btn-embed.png',
    name: 'Long chatbot name example',
    status: 'Active',
    infos: {
      avatars: {
        value: 2,
        status: 'active'
      },
      flagged_messages: {
        value: 10,
        status: 'active'
      },
      active_integrations: {
        value: 2,
        status: 'active'
      }
    },
    stats: [...dataChart]
  }
};

export const Paused = {
  args: {
    image: 'https://chat.dante-ai.com/btn-embed.png',
    name: 'Long chatbot name example',
    status: 'paused',
    infos: {
      avatars: {
        value: 0,
        status: 'active'
      },
      flagged_messages: {
        value: 20,
        status: 'active'
      },
      active_integrations: {
        value: 3,
        status: 'active'
      }
    },
    stats: [...dataChart]
  }
};
