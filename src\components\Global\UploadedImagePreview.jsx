import React from 'react';
import DButtonIcon from './DButtonIcon';
import CloseIcon from './Icons/CloseIcon';

const UploadedImagePreview = ({ image, onRemove, className = '' }) => {
  return (
    <div className={`relative w-16 h-16 rounded-size1 overflow-hidden group bg-grey-2 ${className}`}>
      <img 
        src={typeof image === 'string' ? image : URL.createObjectURL(image)} 
        alt="Uploaded" 
        className="w-full h-full object-contain"
      />
      <div className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
        <DButtonIcon 
          onClick={onRemove}
          className="bg-black bg-opacity-50 hover:bg-opacity-70 text-white"
        >
          <CloseIcon className="size-5" />
        </DButtonIcon>
      </div>
    </div>
  );
};

export default UploadedImagePreview; 