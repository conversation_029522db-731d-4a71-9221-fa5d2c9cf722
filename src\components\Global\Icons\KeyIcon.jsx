import * as React from 'react';
const KeyIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.651 4.01a3.36 3.36 0 0 1 3.705-.747c.413.173.788.426 1.104.746a3.47 3.47 0 0 1 .995 2.438 3.47 3.47 0 0 1-.995 2.437 3.38 3.38 0 0 1-2.405 1.01 3.37 3.37 0 0 1-2.07-.713l-3.574 3.623 1.255 1.271a.454.454 0 0 1 0 .636.44.44 0 0 1-.627 0L5.784 13.44 4.32 14.923l1.254 1.272a.454.454 0 0 1 0 .636.44.44 0 0 1-.628 0L3.38 15.24a.454.454 0 0 1 0-.636l5.978-6.06a3.48 3.48 0 0 1-.703-2.098c0-.915.359-1.791.996-2.438m2.405-.11c-.667 0-1.306.267-1.777.745a2.57 2.57 0 0 0-.737 1.802c0 .675.265 1.323.736 1.8a2.5 2.5 0 0 0 1.777.747c.667 0 1.306-.268 1.777-.746a2.57 2.57 0 0 0 .737-1.801c0-.676-.265-1.324-.737-1.802a2.5 2.5 0 0 0-1.777-.746m2.033 7.705c.245 0 .444.202.444.45v1.798h1.774c.245 0 .443.202.443.45s-.198.45-.444.45h-1.773v1.797c0 .249-.199.45-.444.45a.447.447 0 0 1-.443-.45v-1.798h-1.774a.447.447 0 0 1-.444-.45c0-.247.199-.449.444-.449h1.774v-1.798c0-.248.198-.45.443-.45"
      fill="currentColor"
    />
  </svg>
);
export default KeyIcon;
