# Twilio Phone Number Purchase Implementation

This document outlines the API endpoints and frontend implementation required for purchasing Twilio phone numbers through Dante AI's internal account. This will allow users to search for, purchase, and manage phone numbers without needing their own Twilio account.

## API Endpoints

### 1. List Available Phone Numbers

**Endpoint:** `GET /twilio/numbers`

**Description:** Retrieves a list of available Twilio phone numbers based on specified filters. Each call returns 30 different numbers.

**Parameters:**
- `country_code`: Country code to search for numbers (e.g., "US", "CA")
- `area_code`: Optional area code to filter numbers
- `contains`: Optional sequence of digits that the number should contain

**Response:** Returns a list of available phone numbers with their details, including:
- Number
- Formatted number
- Country
- Price
- Capabilities

### 2. Create Checkout Session for Phone Number Purchase

**Endpoint:** `POST /twilio/get-number-purchase-checkout-session`

**Description:** Creates a Stripe checkout session for purchasing a specific phone number.

**Request Body:**
```json
{
  "phone_number": "+***********"
}
```

**Response:** Returns a Stripe checkout session ID and URL that can be used to redirect the user to the Stripe checkout page.

### 3. Handle Successful Purchase

**Endpoint:** `POST /twilio/purchase-number-success`

**Description:** Handles the successful purchase of a phone number after payment is completed.

**Parameters:**
- `token`: Purchase token generated during checkout session creation
- `session_id`: Stripe checkout session ID
- `user_id`: User ID

**Response:** Returns a success message and details about the purchased phone number.

### 4. Handle Cancelled Purchase

**Endpoint:** `POST /twilio/purchase-number-cancel`

**Description:** Handles the cancellation of a phone number purchase.

**Parameters:**
- `token`: Purchase token generated during checkout session creation
- `user_id`: User ID

**Response:** Returns a message confirming the cancellation of the purchase.

## Implementation Plan

### 1. Create Phone Number Purchase Page

**Path:** `/phone-numbers/purchase`

**Features:**
- Table displaying available Twilio numbers with the following columns:
  - Phone Number (formatted)
  - Country
  - Price
  - Buy button
- Filter options:
  - Country dropdown
  - Area code input
  - Contains digits input
- Refresh button to fetch another set of 30 numbers

**Implementation Details:**
- Use the existing table components from the project
- Add filter components similar to other filter implementations in the project
- Implement the "Buy" button to call the checkout session endpoint
- Redirect to Stripe checkout page after session creation

### 2. Create Success Page

**Path:** `/phone-numbers/purchase/success`

**Features:**
- Success message with animation
- Brief details about the purchased number
- Automatic redirect to phone numbers page after 5-10 seconds

**Implementation Details:**
- Call the `/twilio/purchase-number-success` endpoint when the page loads
- Display a loading state while the API call is in progress
- Show success message and countdown to redirect
- Redirect to `/phone-numbers` after countdown

### 3. Create Cancel Page

**Path:** `/phone-numbers/purchase/cancel`

**Features:**
- Cancellation message
- Automatic redirect to phone numbers page after 5-10 seconds

**Implementation Details:**
- Call the `/twilio/purchase-number-cancel` endpoint when the page loads
- Display a loading state while the API call is in progress
- Show cancellation message and countdown to redirect
- Redirect to `/phone-numbers` after countdown

## Frontend Service Implementation

Create a new service file `src/services/twilioNumbers.service.js` with the following functions:

```javascript
import { DEFAULT_HEADERS } from './constants.service';
import http from './http';

export const getAvailableNumbers = async (filters) => {
  return await http.get(import.meta.env.VITE_APP_BASE_API + 'twilio/numbers', {
    params: filters,
    headers: DEFAULT_HEADERS
  });
};

export const createNumberPurchaseCheckoutSession = async (phoneNumber) => {
  return await http.post(
    import.meta.env.VITE_APP_BASE_API + 'twilio/get-number-purchase-checkout-session',
    { phone_number: phoneNumber },
    { headers: DEFAULT_HEADERS }
  );
};

export const confirmNumberPurchase = async (token, sessionId, userId) => {
  return await http.post(
    import.meta.env.VITE_APP_BASE_API + 'twilio/purchase-number-success',
    {},
    {
      params: { token, session_id: sessionId, user_id: userId },
      headers: DEFAULT_HEADERS
    }
  );
};

export const cancelNumberPurchase = async (token, userId) => {
  return await http.post(
    import.meta.env.VITE_APP_BASE_API + 'twilio/purchase-number-cancel',
    {},
    {
      params: { token, user_id: userId },
      headers: DEFAULT_HEADERS
    }
  );
};
```

## Next Steps

1. Create the purchase page component with table and filters
2. Implement the success and cancel pages
3. Add routing for the new pages
4. Test the full purchase flow
