function getGradient(ctx, chartArea, color) {
  let width, height, gradient;
  const chartWidth = chartArea.right - chartArea.left;
  const chartHeight = chartArea.bottom - chartArea.top;
  if (!gradient || width !== chartWidth || height !== chartHeight) {
    width = chartWidth;
    height = chartHeight;
    gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
    gradient.addColorStop(0, `rgba(${color}, 0)`);

    gradient.addColorStop(1, `rgba(${color}, 0.5)`);
  }

  return gradient;
}

const datasetMessages = {
  data: [123, 234, 45, 456, 126, 500, 200, 123, 234, 45, 456, 126, 500, 200],
  label: '',
  borderColor: 'rgba(255, 53, 40, 1)',
  borderWidth: 1,
  backgroundColor: function (context) {
    const chart = context.chart;
    const { ctx, chartArea } = chart;
    if (!chartArea) {
      return;
    }
    return getGradient(ctx, chartArea, '255, 53, 40');
  },
  fill: true,
  pointStyle: false
};

const datasetCreditUsed = {
  data: [112, 23, 362, 45, 1, 50, 200, 112, 23, 362, 45, 1, 50, 200],
  label: '',
  borderColor: 'rgba(255, 156, 40, 1)',
  borderWidth: 1,
  backgroundColor: function (context) {
    const chart = context.chart;
    const { ctx, chartArea } = chart;
    if (!chartArea) {
      return;
    }
    return getGradient(ctx, chartArea, '255, 156, 40');
  },
  fill: true,
  pointStyle: false
};

const datasetUnquieUser = {
  data: [43, 512, 64, 7, 102, 20, 300, 43, 512, 64, 7, 102, 20, 300],
  label: '',
  borderColor: 'rgba(14, 217, 9, 1)',
  borderWidth: 1,
  backgroundColor: function (context) {
    const chart = context.chart;
    const { ctx, chartArea } = chart;
    if (!chartArea) {
      return;
    }
    return getGradient(ctx, chartArea, '14, 217, 9');
  },
  fill: true,
  pointStyle: false
};

const datasetConversations = {
  data: [101, 20, 303, 40, 0, 60, 100, 101, 20, 303, 40, 0, 60, 100],
  label: '',
  borderColor: 'rgba(130, 117, 247, 1)',
  borderWidth: 1,
  backgroundColor: function (context) {
    const chart = context.chart;
    const { ctx, chartArea } = chart;
    if (!chartArea) {
      return;
    }
    return getGradient(ctx, chartArea, '130, 117, 247');
  },
  fill: true,
  pointStyle: false
};

export { datasetConversations, datasetCreditUsed, datasetMessages, datasetUnquieUser };
