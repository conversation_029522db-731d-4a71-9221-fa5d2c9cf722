const PhoneIcon = (props) => {
  return (
    <svg 
      width="1em" 
      height="1em" 
      viewBox="0 0 12 12" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg" 
      {...props}
    >
      <path 
        fillRule="evenodd" 
        clipRule="evenodd"
        d="M2 0.8C1.33726 0.8 0.8 1.33726 0.8 2V3.08724C0.8 3.921 0.916202 4.75068 1.14525 5.55236C1.87286 8.09898 3.9042 10.0637 6.47365 10.7061L7.35049 10.9253C8.08024 11.1077 8.82961 11.2 9.58181 11.2H10C10.6627 11.2 11.2 10.6627 11.2 10V9.82462C11.2 9.64107 11.0751 9.48108 10.897 9.43656L8.74241 8.89791C8.6061 8.86384 8.46191 8.90378 8.36256 9.00313L7.89057 9.47512C7.52533 9.84035 6.96737 9.9309 6.50538 9.6999L5.72923 9.31183C4.41324 8.65383 3.34617 7.58676 2.68817 6.27077L2.34082 5.57606C2.08146 5.05734 2.23017 4.42737 2.69413 4.0794L3.33506 3.59871C3.46381 3.50215 3.52215 3.33783 3.48311 3.18169L2.96344 1.10299C2.91892 0.924919 2.75893 0.8 2.57538 0.8H2ZM0 2C0 0.895431 0.895431 0 2 0H2.57538C3.12602 0 3.606 0.374756 3.73955 0.908957L4.25923 2.98767C4.37633 3.45606 4.20131 3.94902 3.81506 4.23871L3.17413 4.7194C3.01948 4.83539 2.96991 5.04538 3.05636 5.21829L3.40372 5.913C3.9843 7.07417 4.92583 8.0157 6.087 8.59628L6.86315 8.98436C7.01715 9.06136 7.20314 9.03118 7.32488 8.90943L7.79687 8.43744C8.09493 8.13939 8.52751 8.01957 8.93644 8.1218L11.091 8.66045C11.6252 8.794 12 9.27398 12 9.82462V10C12 11.1046 11.1046 12 10 12H9.58181C8.7642 12 7.94966 11.8997 7.15646 11.7014L6.27962 11.4822C3.4328 10.7705 1.18218 8.59367 0.376032 5.77214C0.126562 4.89899 0 3.99533 0 3.08724V2Z" 
        fill="currentColor"
      />
    </svg>
  );
};

export default PhoneIcon;
