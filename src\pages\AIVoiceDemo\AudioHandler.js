// Audio Handler for the AI Voice Demo
import { PLAY_TIME_RESET_THRESHOLD_MS, INITIAL_BUFFER_TIME, JITTER_BUFFER_SIZE, PLAYBACK_BUFFER_SIZE } from './AudioUtils';

export class AudioHandler {
  constructor({
    audioContextRef,
    playTimeRef,
    lastMessageTimeRef,
    frameRef,
    setSpeaking,
    setSmoothedBotLevel,
    outputDeviceIdRef
  }) {
    this.audioContextRef = audioContextRef;
    this.playTimeRef = playTimeRef;
    this.lastMessageTimeRef = lastMessageTimeRef;
    this.frameRef = frameRef;
    this.setSpeaking = setSpeaking;
    this.setSmoothedBotLevel = setSmoothedBotLevel;
    this.outputDeviceIdRef = outputDeviceIdRef;
    this.botAnalyserRef = null;

    // Add jitter buffer for audio packets to reduce choppiness
    this.jitterBuffer = [];
    this.isPlaying = false;
    this.gainNode = null;
    this.lastAudioEndTime = 0;
    this.pendingEndTimeout = null;
    this.initGainNode();
  }

  // Initialize gain node for dynamic volume control
  initGainNode() {
    if (this.audioContextRef.current) {
      this.gainNode = this.audioContextRef.current.createGain();
      this.gainNode.gain.value = 1.0; // Default volume
      this.gainNode.connect(this.audioContextRef.current.destination);
    }
  }

  // Process and play audio from protobuf message
  enqueueAudioFromProto(arrayBuffer) {
    try {
      const parsedFrame = this.frameRef.current.decode(new Uint8Array(arrayBuffer));
      if (!parsedFrame?.audio) {
        return false;
      }

      // Resume the AudioContext if it's suspended
      if (this.audioContextRef.current.state === 'suspended') {
        this.audioContextRef.current.resume();
      }

      // Ensure gain node is created
      if (!this.gainNode) {
        this.initGainNode();
      }

      // Convert audio data
      const audioVector = Array.from(parsedFrame.audio.audio);
      const audioArray = new Uint8Array(audioVector);

      // Debug the received audio buffer
      if (audioArray.length === 0) {
        return false;
      }

      // Always set speaking state to 'bot' when we receive audio data
      // This is critical for stopping the calling animation
      this.setSpeaking('bot');
      this.setSmoothedBotLevel(0.4);

      // Clear any pending end timeout when new audio arrives
      if (this.pendingEndTimeout) {
        clearTimeout(this.pendingEndTimeout);
        this.pendingEndTimeout = null;
      }

      // Add to jitter buffer instead of playing immediately
      this.addToJitterBuffer(audioArray.buffer);

      // Let the caller know we successfully processed audio data
      return true;
    } catch (error) {
      console.error('Error processing audio from protobuf:', error);
      return false;
    }
  }

  // Add audio data to jitter buffer and trigger playback if needed
  addToJitterBuffer(buffer) {
    // Add buffer to jitter buffer
    this.jitterBuffer.push(buffer);

    // If we have enough buffers or we're already playing, process the buffer
    if (this.jitterBuffer.length >= JITTER_BUFFER_SIZE || this.isPlaying) {
      this.processJitterBuffer();
    }
  }

  // Process jitter buffer to play audio chunks in sequence
  processJitterBuffer() {
    if (this.jitterBuffer.length === 0) return;

    // Mark as playing
    this.isPlaying = true;

    // Get the next buffer to play
    const nextBuffer = this.jitterBuffer.shift();

    // Reset play time if it's been a while since we last played something
    const diffTime = this.audioContextRef.current.currentTime - this.lastMessageTimeRef.current;
    if ((this.playTimeRef.current === 0) || (diffTime > PLAY_TIME_RESET_THRESHOLD_MS)) {
      // Add a buffer time for the first message to reduce glitches
      this.playTimeRef.current = this.audioContextRef.current.currentTime +
        (this.playTimeRef.current === 0 ? INITIAL_BUFFER_TIME : 0);
    }
    this.lastMessageTimeRef.current = this.audioContextRef.current.currentTime;

    // Handle buffer decoding with improved error handling
    try {
      this.audioContextRef.current.decodeAudioData(nextBuffer,
        this.handleDecodedAudio.bind(this),
        (error) => this.handleAudioDecodeError(error, new Uint8Array(nextBuffer))
      );
    } catch (decodeError) {
      console.error('Error initiating audio decode:', decodeError);
      // Try to process the next buffer
      this.processJitterBuffer();
    }
  }

  // Handle successfully decoded audio buffer
  handleDecodedAudio(buffer) {
    if (!buffer) {
      this.processJitterBuffer(); // Try next buffer
      return;
    }

    const source = new AudioBufferSourceNode(this.audioContextRef.current);
    source.buffer = buffer;

    // Create analyzer for bot audio if not already created
    if (!this.botAnalyserRef) {
      this.botAnalyserRef = this.audioContextRef.current.createAnalyser();
      this.botAnalyserRef.fftSize = 256;
      this.botAnalyserRef.smoothingTimeConstant = 0.7; // Make visualization smoother
    }

    // Connect source → analyzer → gain node → destination
    source.connect(this.botAnalyserRef);
    this.botAnalyserRef.connect(this.gainNode);

    // Ensure we don't try to schedule audio in the past
    const startTime = Math.max(this.playTimeRef.current, this.audioContextRef.current.currentTime);

    // Schedule the audio playback
    source.start(startTime);

    // Calculate when this audio segment will end
    const endTime = startTime + buffer.duration;
    this.lastAudioEndTime = endTime;

    // Update playTime for the next audio chunk
    this.playTimeRef.current = endTime;

    // Add an event listener for when the audio ends
    source.onended = () => {
      if (this.jitterBuffer.length === 0) {
        // If we have no more buffers and no pending timeout,
        // set a timeout to check if this was the last audio segment
        if (!this.pendingEndTimeout) {
          this.pendingEndTimeout = setTimeout(() => {
            // If no new audio has arrived since the last ended event
            if (this.jitterBuffer.length === 0) {
              this.isPlaying = false;
              this.fadeOutAndStopSpeaking(0.1); // Short fade time since audio already finished
            }
            this.pendingEndTimeout = null;
          }, 250); // Short delay to check for more audio
        }
      }
    };

    // Process next buffer if available
    if (this.jitterBuffer.length > 0) {
      setTimeout(() => {
        this.processJitterBuffer();
      }, Math.min(buffer.duration * 500, 100)); // Process next buffer more quickly
    }
  }

  // Fade out smoothly and stop speaking state
  fadeOutAndStopSpeaking(duration) {
    // Smoothly fade out by gradually reducing the level
    const fadeOutDuration = 300; // Shorter duration (ms)
    const fadeSteps = 5; // Fewer steps for faster fade
    const fadeInterval = fadeOutDuration / fadeSteps;

    let step = 0;
    const fadeTimer = setInterval(() => {
      step++;
      if (step >= fadeSteps) {
        clearInterval(fadeTimer);

        // Only set speaking to null if no new audio has started
        if (!this.isPlaying && this.jitterBuffer.length === 0) {
          this.setSpeaking(null);
        }
      } else {
        // Gradually reduce the smoothed level
        this.setSmoothedBotLevel(prev => prev * 0.6);
      }
    }, fadeInterval);
  }

  // Handle audio decoding errors with fallback
  handleAudioDecodeError(error, audioArray) {
    console.error('Error decoding audio data:', error);

    // Let's try an alternative approach if the standard decoding fails
    try {
      // Create a blob and play it using audio element as fallback
      const blob = new Blob([audioArray], { type: 'audio/wav' });
      const url = URL.createObjectURL(blob);
      const audio = new Audio(url);

      // Set the audio output device if specified and supported
      if (this.outputDeviceIdRef?.current && typeof audio.setSinkId === 'function') {
        audio.setSinkId(this.outputDeviceIdRef.current)
          .then(() => {})
          .catch(err => console.error('Error setting audio output device:', err));
      }

      audio.onplay = () => {
        this.setSpeaking('bot');
        this.setSmoothedBotLevel(0.4);
      };

      audio.onended = () => {
        // Add a small delay before fading out
        setTimeout(() => {
          // Only fade out if no new audio is playing
          if (this.jitterBuffer.length === 0 && !this.isPlaying) {
            this.fadeOutAndStopSpeaking(0.1);
          }
          URL.revokeObjectURL(url);
        }, 100);
      };

      audio.play().catch(err => {
        console.error('Failed to play fallback audio:', err);
        URL.revokeObjectURL(url);
      });
    } catch (fallbackError) {
      console.error('Fallback audio playback also failed:', fallbackError);
    }

    // Try to process the next buffer if available
    if (this.jitterBuffer.length > 0) {
      setTimeout(() => this.processJitterBuffer(), 10);
    }
  }

  // Access to bot analyzer
  getBotAnalyser() {
    return this.botAnalyserRef;
  }

  // Clean up audio resources
  cleanup() {
    // Clear jitter buffer
    this.jitterBuffer = [];
    this.isPlaying = false;

    // Clear any pending timeouts
    if (this.pendingEndTimeout) {
      clearTimeout(this.pendingEndTimeout);
      this.pendingEndTimeout = null;
    }

    if (this.botAnalyserRef) {
      try {
        this.botAnalyserRef.disconnect();
      } catch (err) {
        console.error('Error disconnecting bot analyzer:', err);
      }
      this.botAnalyserRef = null;
    }

    if (this.gainNode) {
      try {
        this.gainNode.disconnect();
      } catch (err) {
        console.error('Error disconnecting gain node:', err);
      }
      this.gainNode = null;
    }
  }
}
