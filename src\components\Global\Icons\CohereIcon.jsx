import * as React from 'react';
const CohereIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    xmlSpace="preserve"
    width={20}
    height={20}
    {...props}
  >
    <path
      d="M6.48 11.92c.533 0 1.6-.027 3.093-.64 1.733-.72 5.147-2 7.627-3.333 1.733-.933 2.48-2.16 2.48-3.813A4.137 4.137 0 0 0 15.547 0h-9.6C2.667 0 0 2.667 0 5.947s2.507 5.973 6.48 5.973M8.107 16c0-1.6.96-3.067 2.453-3.68l3.013-1.253C16.64 9.813 20 12.053 20 15.36c0 2.56-2.08 4.64-4.64 4.64h-3.28c-2.187 0-3.973-1.787-3.973-4"
      style={{
        fillRule: 'evenodd',
        clipRule: 'evenodd',
        fill: 'currentColor'
      }}
    />
    <path
      d="M3.44 12.693A3.45 3.45 0 0 0 0 16.133v.453C0 18.453 1.547 20 3.44 20s3.44-1.547 3.44-3.44v-.453c-.027-1.867-1.547-3.413-3.44-3.413"
      style={{
        fill: 'currentColor'
      }}
    />
  </svg>
);
export default CohereIcon;
