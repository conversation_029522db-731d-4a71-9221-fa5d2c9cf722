import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';

import DCheckbox from '../DCheckbox';
import ChevronDownIcon from '../Icons/ChevronDownIcon';
import CloseIcon from '../Icons/CloseIcon';
import clsx from 'clsx';
import DTransition from '../DTransition';

const DMultiselect = ({
  options,
  selected,
  setSelected,
  error,
  skipSelectAllCheck = false,
  buttonClassName,
  ...props
}) => {
  const toggleSelection = (option) => {
    if (selected.find((item) => item.value === option.value)) {
      setSelected(selected.filter((item) => item.value !== option.value));
    } else {
      setSelected([...selected, option]);
    }
  };

  // If skipSelectAllCheck is true and there's only one option selected,
  // we don't consider it as "all selected" unless explicitly all options are selected
  const isAllSelected =
    skipSelectAllCheck && selected?.length === 1
      ? false
      : selected?.length === options?.length;

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelected(options); // Add all options to selected
    } else {
      setSelected([]); // Clear all selections
    }
  };

  return (
    <div className="w-full flex flex-col gap-size1">
      <Listbox
        value={selected}
        onChange={setSelected}
        multiple
        data-testid={`d-multi-select-${props.name ?? props.id ?? ''}`}
        {...props}
      >
        <div className="relative mt-1">
          <ListboxButton
            className={clsx(
              'flex items-center justify-between w-full min-h-11 cursor-default rounded-size1 bg-white px-size2 py-size1 text-left border border-grey-5 focus:outline-none sm:text-sm',
              buttonClassName,
              error ? 'border-red-500' : ''
            )}
          >
            <div className="flex items-center gap-size1 flex-wrap w-full">
              {skipSelectAllCheck && selected?.length === 1 ? (
                selected?.map((option, idx) => (
                  <div
                    key={idx}
                    className="px-size0 border rounded-size1 border-grey-5 flex items-center gap-size0"
                  >
                    <p className="text-sm font-regular tracking-tight">
                      {option?.label}
                    </p>
                    <CloseIcon
                      className="size-3 cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault();
                        toggleSelection(option);
                      }}
                    />
                  </div>
                ))
              ) : selected?.length === options?.length ? (
                <>All</>
              ) : selected?.length > 0 ? (
                selected?.map((option, idx) => (
                  <div
                    key={idx}
                    className="px-size0 border rounded-size1 border-grey-5 flex items-center gap-size0"
                  >
                    <p className="text-sm font-regular tracking-tight">
                      {option?.label}
                    </p>
                    <CloseIcon
                      className="size-3 cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault();
                        toggleSelection(option);
                      }}
                    />
                  </div>
                ))
              ) : (
                <>Select items</>
              )}
            </div>
            <span>
              <ChevronDownIcon aria-hidden="true" className="size-3" />
            </span>
          </ListboxButton>

          <ListboxOptions
            transition
            anchor="bottom"
            className={clsx(
              'w-[var(--button-width)]',
              'mt-1 !max-h-60 overflow-auto rounded-size1 bg-white p-size2 text-base shadow-lg sm:text-sm border border-grey-5 scrollbar',
              'origin-top transition duration-200 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 overflow-x-hidden'
            )}
          >
            {/* "Select All" Option */}
            <ListboxOption
              key="all"
              value="all"
              className="relative cursor-default select-none py-size1 pt-0 border-b border-grey-5"
              onClick={(e) => {
                e.preventDefault();
                handleSelectAll(!isAllSelected);
              }}
            >
              <DCheckbox
                checked={isAllSelected}
                size="sm"
                onChange={handleSelectAll}
                label="Select All"
                hideError
              />
            </ListboxOption>

            {/* Individual Options */}
            {options?.map((option, idx) => (
              <ListboxOption
                key={idx}
                className={({ active }) =>
                  'relative cursor-default select-none py-size1'
                }
                value={null}
                onClick={(e) => {
                  e.preventDefault();
                  // toggleSelection(option);
                }}
                as="div"
              >
                {({ checked: isSelected }) => (
                  <DCheckbox
                    checked={selected?.some(
                      (item) => item.value === option.value
                    )}
                    size="sm"
                    onChange={() => toggleSelection(option)}
                    label={option.label}
                    hideError
                  />
                )}
              </ListboxOption>
            ))}
          </ListboxOptions>
        </div>
      </Listbox>
      <DTransition show={error}>
        <p className="text-error">{error}</p>
      </DTransition>
    </div>
  );
};

export default DMultiselect;
