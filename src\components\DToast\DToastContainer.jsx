import { Toaster } from 'react-hot-toast';

const DToastContainer = ({ hideInMobile = false, showFixed = false }) => {
  return (
    <Toaster
      position="top-center"
      toastOptions={{
        duration: 5000,
        style: {
          background: '#fff',
          color: '#363636',
          padding: '16px',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        },
        success: {
          iconTheme: {
            primary: '#4CAF50',
            secondary: '#FFFFFF',
          },
        },
        error: {
          iconTheme: {
            primary: '#F44336',
            secondary: '#FFFFFF',
          },
        },
        className: hideInMobile ? 'hidden sm:block' : '',
      }}
    />
  );
};

export default DToastContainer;
