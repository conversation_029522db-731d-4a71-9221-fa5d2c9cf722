import { fn } from '@storybook/test';

import DUpload from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Global/DUpload',
  component: DUpload,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  args: {
    name: '',
    id: '',
    title: '',
    subtitle: '',
    note: 'Max. size 128mb',
    multipleFile: false,
    accept: '',
    onChangeFile: (event) => console.log(event.target.files)
  }
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    title: '',
    subtitle: '',
    note: 'Max. size 128mb',
    onChangeFile: (event) => console.log(event.target.files)
  }
};
export const Advanced = {
  args: {
    title: 'Drop your text file here',
    subtitle: 'Only one URL per line',
    note: '',
    onChangeFile: (event) => console.log(event.target.files)
  }
};
