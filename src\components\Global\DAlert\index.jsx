import React from 'react';
import clsx from 'clsx';

import <PERSON><PERSON><PERSON> from '../DSpinner';
import AlertIcon from '../Icons/AlertIcon';
import CheckIcon from '../Icons/CheckIcon';

const DAlert = ({
  children,
  size = 'sm',
  type = 'explainer',
  state = 'default',
  className = '',
  fullWidth = false,
  classNameWrapper = '',
}) => {
  const classNamesWrapper = clsx(
    'flex',
    'rounded-size1',
    'items-start',
    'gap-size1',
    'p-size1',
    'text-sm',
    'w-full',
    classNameWrapper,
    {
      'border-[1px]': type === 'attentive',
      'text-xs': type === 'explainer' || type === 'input',
      'bg-orange-10 border-orange-20': state === 'alert',
      'bg-green-10 border-green-20': state === 'positive' && type !== 'input',
      'bg-negative-5 border-negative-20':
        state === 'negative' && type !== 'input',
      'text-negative-200': state === 'negative' && type === 'input',
      'bg-grey-5': state === 'info',
    },
    className
  );

  const classNamesIcon = clsx({
    'text-orange-300': state === 'alert',
    'text-green-300': state === 'positive',
    'text-negative-200': state === 'negative',
    'bg-grey-2 text-black': state === 'info',
  });

  const Icon = state === 'positive' ? CheckIcon : AlertIcon;

  return (
    <div className={classNamesWrapper} data-testid={`alert-${state}`}>
      <div className={classNamesIcon}>
        {state === 'pending' ? (
          <DSpinner />
        ) : (
          <Icon
            className="mt-[2px]"
            width={type === 'explainer' || type === 'input' ? 18 : 20}
            height={type === 'explainer' || type === 'input' ? 18 : 20}
          />
        )}
      </div>
      <div className="mt-[1px] leading-normal">{children}</div>
    </div>
  );
};

export default DAlert;
