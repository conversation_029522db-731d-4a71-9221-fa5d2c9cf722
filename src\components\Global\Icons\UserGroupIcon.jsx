import * as React from 'react';

const UserGroupIcon = (props) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.5 5.5C5.5 4.11929 6.61929 3 8 3C9.38071 3 10.5 4.11929 10.5 5.5C10.5 6.88071 9.38071 8 8 8C6.61929 8 5.5 6.88071 5.5 5.5ZM8 4C7.17157 4 6.5 4.67157 6.5 5.5C6.5 6.32843 7.17157 7 8 7C8.82843 7 9.5 6.32843 9.5 5.5C9.5 4.67157 8.82843 4 8 4Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4 10.5C4 9.67157 4.67157 9 5.5 9H10.5C11.3284 9 12 9.67157 12 10.5V12C12 12.5523 11.5523 13 11 13H5C4.44772 13 4 12.5523 4 12V10.5ZM5.5 10C5.22386 10 5 10.2239 5 10.5V12H11V10.5C11 10.2239 10.7761 10 10.5 10H5.5Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.5 6.5C11.5 5.11929 12.6193 4 14 4C15.3807 4 16.5 5.11929 16.5 6.5C16.5 7.88071 15.3807 9 14 9C12.6193 9 11.5 7.88071 11.5 6.5ZM14 5C13.1716 5 12.5 5.67157 12.5 6.5C12.5 7.32843 13.1716 8 14 8C14.8284 8 15.5 7.32843 15.5 6.5C15.5 5.67157 14.8284 5 14 5Z"
      transform="translate(-3 -1) scale(0.85)"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 11.5C10 10.6716 10.6716 10 11.5 10H16.5C17.3284 10 18 10.6716 18 11.5V13C18 13.5523 17.5523 14 17 14H11C10.4477 14 10 13.5523 10 13V11.5ZM11.5 11C11.2239 11 11 11.2239 11 11.5V13H17V11.5C17 11.2239 16.7761 11 16.5 11H11.5Z"
      transform="translate(-3 -1) scale(0.85)"
      fill="currentColor"
    />
  </svg>
);

export default UserGroupIcon; 