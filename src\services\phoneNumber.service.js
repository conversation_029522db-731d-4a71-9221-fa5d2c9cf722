import { DEFAULT_HEADERS } from './constants.service';
import http from './http';


export const getPhoneNumbers = async () => {
    return await http.get(import.meta.env.VITE_APP_BASE_API + 'ai-voices/phone-numbers');
}

export const addPhoneNumber = async (phoneNumber) => {
    return await http.post(import.meta.env.VITE_APP_BASE_API + 'ai-voices/phone-numbers', {
        number: phoneNumber,
    },{
        headers: DEFAULT_HEADERS
    });
}

export const verifyPhoneNumber = async (phoneNumberId) => {
    return await http.post(import.meta.env.VITE_APP_BASE_API + 'ai-voices/phone-numbers/' + phoneNumberId + '/verify', {
    },{
        headers: DEFAULT_HEADERS
    });
}

export const resendVerifyPhoneNumber = async (id) => {
    return await http.post(import.meta.env.VITE_APP_BASE_API + 'ai-voices/phone-numbers/' + id + '/resend-verification');
}

export const getTwilioWebhookUrl = async (id) => {
    return await http.get(import.meta.env.VITE_APP_BASE_API + 'ai-voices/phone-numbers/' + id + '/twilio-webhook-url');
}

export const deletePhoneNumber = async (id) => {
    return await http.delete(import.meta.env.VITE_APP_BASE_API + 'ai-voices/phone-numbers/' + id);
}

export const getPhoneNumber = async (id) => {
    return await http.get(import.meta.env.VITE_APP_BASE_API + 'ai-voices/phone-numbers/' + id);
}

export const assignVoiceToPhoneNumber = async (phoneNumberId, voiceId) => {
    return await http.patch(import.meta.env.VITE_APP_BASE_API + 'ai-voices/' + voiceId, {
        phone_number_ids: [phoneNumberId]
    }, {
        headers: DEFAULT_HEADERS
    });
}