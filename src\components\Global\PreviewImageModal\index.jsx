import React, { useState, useRef, useEffect } from 'react';
import DModal from '../DModal';
import DButtonIcon from '../DButtonIcon';
import ZoomOutIcon from '../Icons/ZoomOutIcon';
import DownloadIcon from '../Icons/DownloadIcon';
import AddIcon from '../Icons/AddIcon';

const PreviewImageModal = ({ isOpen, onClose, image }) => {
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 });
  const imageRef = useRef(null);
  const dragRef = useRef(null);
  
  // Reset zoom and position when opening a new image
  useEffect(() => {
    if (isOpen) {
      setZoomLevel(1);
      setPosition({ x: 0, y: 0 });
    }
  }, [isOpen, image]);
  
  const handleZoomIn = () => {
    setZoomLevel(prev => {
      const newZoom = Math.min(prev + 0.25, 3);
      // Center the image when zooming in
      if (imageRef.current && dragRef.current) {
        const containerRect = dragRef.current.getBoundingClientRect();
        const containerCenterX = containerRect.width / 2;
        const containerCenterY = containerRect.height / 2;
        
        // Adjust position to keep center point fixed
        if (newZoom > prev) {
          const scaleFactor = newZoom / prev;
          setPosition(prevPos => ({
            x: (prevPos.x - containerCenterX) * scaleFactor + containerCenterX,
            y: (prevPos.y - containerCenterY) * scaleFactor + containerCenterY
          }));
        }
      }
      return newZoom;
    });
  };
  
  const handleZoomOut = () => {
    setZoomLevel(prev => {
      const newZoom = Math.max(prev - 0.25, 0.5);
      // If zooming out to 1 or less, reset position
      if (newZoom <= 1) {
        setPosition({ x: 0, y: 0 });
      }
      return newZoom;
    });
  };
  
  const handleDownload = () => {
    if (!image) return;
    
    const link = document.createElement('a');
    link.href = image;
    link.download = `image-${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  const handleMouseDown = (e) => {
    if (zoomLevel > 1) {
      e.preventDefault();
      setIsDragging(true);
      setStartPosition({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };
  
  const handleTouchStart = (e) => {
    if (zoomLevel > 1 && e.touches && e.touches[0]) {
      setIsDragging(true);
      setStartPosition({
        x: e.touches[0].clientX - position.x,
        y: e.touches[0].clientY - position.y
      });
    }
  };
  
  const handleMouseMove = (e) => {
    if (isDragging && zoomLevel > 1) {
      requestAnimationFrame(() => {
        setPosition({
          x: e.clientX - startPosition.x,
          y: e.clientY - startPosition.y
        });
      });
    }
  };
  
  const handleTouchMove = (e) => {
    if (isDragging && zoomLevel > 1 && e.touches && e.touches[0]) {
      e.preventDefault();
      requestAnimationFrame(() => {
        setPosition({
          x: e.touches[0].clientX - startPosition.x,
          y: e.touches[0].clientY - startPosition.y
        });
      });
    }
  };
  
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  const handleMouseLeave = () => {
    setIsDragging(false);
  };
  
  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  return (
    <DModal
      isOpen={isOpen}
      onClose={onClose}
      className="max-w-[90vw] max-h-[90vh]"
      title="Preview Image"
    >
      <div className="flex flex-col items-center justify-center max-h-[75vh]">
        <div
          ref={dragRef}
          className="relative overflow-hidden flex items-center justify-center w-full h-full"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{ cursor: zoomLevel > 1 ? 'move' : 'default' }}
        >
          <img
            ref={imageRef}
            src={image}
            alt="Preview"
            className="max-w-full max-h-full object-contain will-change-transform"
            style={{
              transform: `scale(${zoomLevel}) translate(${position.x / zoomLevel}px, ${position.y / zoomLevel}px)`,
              transition: isDragging ? 'none' : 'transform 0.2s ease-out'
            }}
          />
        </div>
        <div className="flex items-center justify-center gap-size2 mt-size2">
          <DButtonIcon
            onClick={handleZoomOut}
            disabled={zoomLevel <= 0.5}
            className="bg-grey-5 hover:bg-grey-10 text-grey-300"
            title="Zoom out"
          >
            <ZoomOutIcon />
          </DButtonIcon>
          <span className="text-sm font-medium">{Math.round(zoomLevel * 100)}%</span>
          <DButtonIcon
            onClick={handleZoomIn}
            disabled={zoomLevel >= 3}
            className="bg-grey-5 hover:bg-grey-10 text-grey-300"
            title="Zoom in"
          >
            <AddIcon />
          </DButtonIcon>
          <DButtonIcon
            onClick={handleDownload}
            className="bg-grey-5 hover:bg-grey-10 text-grey-300 ml-size2"
            title="Download image"
          >
            <DownloadIcon />
          </DButtonIcon>
        </div>
      </div>
    </DModal>
  );
};

export default PreviewImageModal;
