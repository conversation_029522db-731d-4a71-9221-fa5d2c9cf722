import { useState } from 'react';
import DButton from '../Global/DButton';
import DInput from '../Global/DInput/DInput';
import DInputBlock from '../Global/DInput/DInputBlock';
import DModal from '../Global/DModal';
import * as plansService from '../../services/plans.service';
import useDanteApi from '@/hooks/useDanteApi';

const DAddSeatsModal = ({ open, onClose }) => {
  const { data: addOns } = useDanteApi(plansService.getAddOns);
  const [seats, setSeats] = useState('');
  const [pending, setPending] = useState(false);

  const handleAddSeats = async () => {
    try {
      setPending(true);
      const response = await plansService.getCheckoutSessionAddon(
        addOns?.results?.find((addon) => addon.key === 'extra_seat')?.id,
        seats
      );

      if (response.status === 200) {
        window.open(response.data.checkout_session.url, '_blank');
        setSeats('');
        onClose();
      }
    } catch (error) {
      console.error(error);
    } finally {
      setPending(false);
    }
  };

  return (
    <DModal
      isOpen={open}
      onClose={onClose}
      title="Add seats"
      footer={
        <div className="flex gap-size1 w-full">
          <DButton variant="grey" fullWidth onClick={onClose}>
            Cancel
          </DButton>
          <DButton
            variant="dark"
            fullWidth
            onClick={handleAddSeats}
            disabled={seats === '' || seats === null || seats === 0}
            loading={pending}
          >
            Add seats
          </DButton>
        </div>
      }
    >
      <div className="flex flex-col gap-size5">
        <DInputBlock label="Number of seats">
          <DInput
            type="number"
            placeholder="1"
            value={seats || ''}
            min={0}
            onChange={(e) => {
              let value = Math.max(0, Number(e.target.value) || 0);
              setSeats(value);
            }}
          />
        </DInputBlock>
      </div>
    </DModal>
  );
};

export default DAddSeatsModal;
