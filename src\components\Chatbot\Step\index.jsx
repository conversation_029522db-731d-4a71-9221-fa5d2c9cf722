import CircledCheck from '../../Global/Icons/CircledCheck';

import './index.css';

const Step = ({ label, active, completed, onClick, className = '', ...props }) => {
  return (
    <button
      className={`dbutton flex items-center gap-size0 p-size2 rounded-full w-full ${className} ${
        active
          ? 'bg-purple-100 text-purple-200'
          : completed
          ? 'bg-green-5 text-black'
          : 'bg-transparent text-grey-20'
      }`}
      onClick={onClick}
      {...props}
    >
      {active ? (
        <div className="step_loader"></div>
      ) : completed ? (
        <CircledCheck className="text-green-400" />
      ) : (
        <CircledCheck className="text-grey-20" />
      )}
      <span className="whitespace-nowrap">{label}</span>
    </button>
  );
};

export default Step;
