:root {
  --dante-loader-bg-color-start: hsl(0, 0%, 57.25%);
  --dante-loader-bg-color-mid: hsl(0, 0%, 78%);
  --spin-color-default: rgba(136, 136, 136, 0.2);
  --spin-color-halfway: rgba(136, 136, 136, 0.7);
  --spin-color-quarter: rgba(136, 136, 136, 0.5);
  --primary-color: #888888; /* Example primary color */
}

.dante-skeleton {
  animation: danteSkeletonLoading 2s ease-in-out 0.5s infinite;
}

@keyframes danteSkeletonLoading {
  0%,
  100% {
    background-color: var(--dante-loader-bg-color-start);
  }
  50% {
    background-color: var(--dante-loader-bg-color-mid);
  }
}

.dante-loader {
  pointer-events: bounding-box;
  background-color: #f8f8f8;
  position: absolute; /* Adjusted to absolute to make top, left, right, bottom work */
  inset: 0; /* Shorthand for top: 0; left: 0; right: 0; bottom: 0; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.dante-loader .dante-loader-spinning {
  font-size: 8px;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  position: relative;
  text-indent: -9999em;
  animation: danteSpinningLoading 1.1s infinite ease;
  transform: translateZ(0);
}
.dante-loader.hidden {
  opacity: 0;
  display: none;
}

@keyframes danteSpinningLoading {
  0%,
  100% {
    box-shadow: 0em -2.6em 0em 0em #888888, 1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2),
      2.5em 0em 0 0em rgba(136, 136, 136, 0.2), 1.75em 1.75em 0 0em rgba(136, 136, 136, 0.2),
      0em 2.5em 0 0em rgba(136, 136, 136, 0.2), -1.8em 1.8em 0 0em rgba(136, 136, 136, 0.2),
      -2.6em 0em 0 0em rgba(136, 136, 136, 0.5), -1.8em -1.8em 0 0em rgba(136, 136, 136, 0.7);
  }
  12.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(136, 136, 136, 0.7), 1.8em -1.8em 0 0em #888888,
      2.5em 0em 0 0em rgba(136, 136, 136, 0.2), 1.75em 1.75em 0 0em rgba(136, 136, 136, 0.2),
      0em 2.5em 0 0em rgba(136, 136, 136, 0.2), -1.8em 1.8em 0 0em rgba(136, 136, 136, 0.2),
      -2.6em 0em 0 0em rgba(136, 136, 136, 0.2), -1.8em -1.8em 0 0em rgba(136, 136, 136, 0.5);
  }
  25% {
    box-shadow: 0em -2.6em 0em 0em rgba(136, 136, 136, 0.5),
      1.8em -1.8em 0 0em rgba(136, 136, 136, 0.7), 2.5em 0em 0 0em #888888,
      1.75em 1.75em 0 0em rgba(136, 136, 136, 0.2), 0em 2.5em 0 0em rgba(136, 136, 136, 0.2),
      -1.8em 1.8em 0 0em rgba(136, 136, 136, 0.2), -2.6em 0em 0 0em rgba(136, 136, 136, 0.2),
      -1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2);
  }
  37.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(136, 136, 136, 0.2),
      1.8em -1.8em 0 0em rgba(136, 136, 136, 0.5), 2.5em 0em 0 0em rgba(136, 136, 136, 0.7),
      1.75em 1.75em 0 0em #888888, 0em 2.5em 0 0em rgba(136, 136, 136, 0.2),
      -1.8em 1.8em 0 0em rgba(136, 136, 136, 0.2), -2.6em 0em 0 0em rgba(136, 136, 136, 0.2),
      -1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2);
  }
  50% {
    box-shadow: 0em -2.6em 0em 0em rgba(136, 136, 136, 0.2),
      1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2), 2.5em 0em 0 0em #888888,
      1.75em 1.75em 0 0em rgba(136, 136, 136, 0.7), 0em 2.5em 0 0em rgba(136, 136, 136, 0.5),
      -1.8em 1.8em 0 0em rgba(136, 136, 136, 0.2), -2.6em 0em 0 0em rgba(136, 136, 136, 0.2),
      -1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2);
  }
  62.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(136, 136, 136, 0.2),
      1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2), 2.5em 0em 0 0em rgba(136, 136, 136, 0.2),
      1.75em 1.75em 0 0em rgba(136, 136, 136, 0.5), 0em 2.5em 0 0em rgba(136, 136, 136, 0.7),
      -1.8em 1.8em 0 0em #888888, -2.6em 0em 0 0em rgba(136, 136, 136, 0.2),
      -1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2);
  }
  75% {
    box-shadow: 0em -2.6em 0em 0em rgba(136, 136, 136, 0.2),
      1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2), 2.5em 0em 0 0em rgba(136, 136, 136, 0.2),
      1.75em 1.75em 0 0em rgba(136, 136, 136, 0.2), 0em 2.5em 0 0em rgba(136, 136, 136, 0.5),
      -1.8em 1.8em 0 0em rgba(136, 136, 136, 0.7), -2.6em 0em 0 0em #888888,
      -1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2);
  }
  87.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(136, 136, 136, 0.2),
      1.8em -1.8em 0 0em rgba(136, 136, 136, 0.2), 2.5em 0em 0 0em rgba(136, 136, 136, 0.2),
      1.75em 1.75em 0 0em rgba(136, 136, 136, 0.2), 0em 2.5em 0 0em rgba(136, 136, 136, 0.2),
      -1.8em 1.8em 0 0em rgba(136, 136, 136, 0.5), -2.6em 0em 0 0em rgba(136, 136, 136, 0.7),
      -1.8em -1.8em 0 0em #888888;
  }
}
