const UserIcon = (props) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.85427 6C6.85427 4.34315 8.19743 3 9.85429 3C11.5112 3 12.8543 4.34315 12.8543 6C12.8543 7.65686 11.5112 9.00001 9.85429 9.00001C8.19743 9.00001 6.85427 7.65686 6.85427 6ZM9.85429 2C7.64514 2 5.85427 3.79086 5.85427 6C5.85427 7.623 6.82088 9.02024 8.20983 9.64743L7.4702 9.71467C5.36539 9.90601 3.61192 11.4078 3.09932 13.4582L3.05547 13.6336C2.8973 14.2663 3.08268 14.9355 3.54382 15.3967C4.57042 16.4233 5.9628 17 7.41463 17H12.294C13.7458 17 15.1382 16.4233 16.1648 15.3967C16.6259 14.9355 16.8113 14.2663 16.6531 13.6336L16.6093 13.4582C16.0967 11.4078 14.3432 9.90601 12.2384 9.71467L11.4988 9.64743C12.8877 9.02024 13.8543 7.623 13.8543 6C13.8543 3.79086 12.0634 2 9.85429 2ZM9.27548 10.5547C9.66056 10.5197 10.048 10.5197 10.4331 10.5547L12.1479 10.7106C13.8291 10.8634 15.2297 12.063 15.6391 13.7007L15.683 13.8761C15.756 14.168 15.6704 14.4768 15.4577 14.6896C14.6186 15.5286 13.4806 16 12.294 16H7.41463C6.22801 16 5.09 15.5286 4.25093 14.6896C4.03817 14.4768 3.95265 14.168 4.02562 13.8761L4.06947 13.7007C4.47891 12.063 5.87951 10.8634 7.56074 10.7106L9.27548 10.5547Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default UserIcon;
