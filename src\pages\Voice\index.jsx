import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import DLoading from '@/components/DLoading';
import VoiceDashboard from '@/components/Voice/Dashboard';
import { VOICE_DASHBOARD_DEFAULT_VOICES } from '@/constants';
import useDanteApi from '@/hooks/useDanteApi';
import { getPhoneNumbers } from '@/services/phoneNumber.service';
import { getVoices } from '@/services/voice.service';
import FirstVoiceSuccessModal from '@/components/Voice/FirstVoiceSuccessModal';
import GlobalModals from '@/components/GlobalModals';
import GetPhoneNumberModal from '@/components/Voice/GetPhoneNumberModal';

const Voice = () => {
  const location = useLocation();
  const { data: voices, refetch: refetchVoices, loading: loadingVoices } = useDanteApi(getVoices);
  const { data: phoneNumbers, refetch: refetchPhoneNumbers, loading: loadingPhoneNumbers } = useDanteApi(getPhoneNumbers);
  const [showFirstVoiceModal, setShowFirstVoiceModal] = useState(false);
  const [showGetPhoneNumberModal, setShowGetPhoneNumberModal] = useState(false);
  const [firstVoiceModalFinished, setFirstVoiceModalFinished] = useState(false);
  const id = location.state?.id;
  useEffect(() => {
    // Check if we just came from voice creation and this is the first voice
    const fromVoiceCreation = location.state?.fromVoiceCreation;
    
    const isFirstVoice = voices?.results?.length === 1;
    if (fromVoiceCreation && isFirstVoice) {
      setTimeout(() => {
        setShowFirstVoiceModal(true);
        window.history.replaceState({}, document.title, location.pathname);
      }, 1000);
    }
  }, [location, voices]);

  useEffect(() => {
    if(phoneNumbers?.results?.length === 0) {
      setTimeout(() => {
        setShowGetPhoneNumberModal(true);
      }, 1000);
    }
  }, [phoneNumbers]);

  if(loadingVoices || loadingPhoneNumbers) {
    return <DLoading show={true} />
  }

  return (
    <>
      <VoiceDashboard
        voices={voices}
        phoneNumbers={phoneNumbers}
        refetchPhoneNumbers={refetchPhoneNumbers}
        refetchVoices={refetchVoices}
      />

      <FirstVoiceSuccessModal
        isOpen={showFirstVoiceModal}
        onClose={() => {
          setShowFirstVoiceModal(false);
          setFirstVoiceModalFinished(true);
        }}
        id={id}
      />
      
      <GetPhoneNumberModal
        isOpen={!showFirstVoiceModal && voices?.results?.length > 0 && showGetPhoneNumberModal && !firstVoiceModalFinished}
        onClose={() => setShowGetPhoneNumberModal(false)}
        id={id}
      />
      <GlobalModals />
    </>
  );
};

export default Voice;
