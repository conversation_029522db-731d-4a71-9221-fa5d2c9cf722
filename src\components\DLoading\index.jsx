import { Transition } from '@headlessui/react';
import DLoaderTraining from '../Global/DLoaderTraining';

const DLoading = ({ show = false, style = {} }) => {
  return (
    <Transition show={show}>
      <div className="transition data-[closed]:opacity-0 duration-300 h-screen w-full flex items-center justify-center h-full" style={style}>
        <DLoaderTraining />
      </div>
    </Transition>
  );
};

export default DLoading;
