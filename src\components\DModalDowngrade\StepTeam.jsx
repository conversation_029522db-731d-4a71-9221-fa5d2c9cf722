import { getTeamMembers } from '@/services/teamManagement.service';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { useState, useEffect } from 'react';
import DButton from '../Global/DButton';
import { removeTeamMembers } from '@/services/plans.service';
import DCheckbox from '../Global/DCheckbox';
import { useUserStore } from '@/stores/user/userStore';
import UserGroupIcon from '../Global/Icons/UserGroupIcon';

const StepTeam = ({
  newTier,
  setTitle,
  changesBetweenTiers,
  handleNextStep,
  handleClose,
  isLoading,
  setIsLoading,
}) => {
  const [usersSelected, setUsersSelected] = useState([]);
  const [allTeamMembers, setAllTeamMembers] = useState([]);

  const [error, setError] = useState('');
  const team = useTeamManagementStore();
  const { user } = useUserStore();

  const getAllTeamMembers = async () => {
    try {
      const teamId = team.teams.find((t) => t.owner_id === user.id)?.id;
      const response = await getTeamMembers(teamId);
      if (response.status === 200) {
        setAllTeamMembers(
          response.data.results.filter((member) => {
            return member.user_id !== user.id;
          })
        );
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleNext = async () => {
    try {
      setIsLoading(true);
      let newError = '';
      let hasError = false;

      let usersToRemove = [];
      if (newTier === 'cancel') {
        usersToRemove = allTeamMembers.map((member) => member.user_id);
      } else {
        if (
          usersSelected.length >=
          changesBetweenTiers.nr_of_team_members_affected
        ) {
          usersToRemove = usersSelected;
        } else {
          newError = `Please select at least ${
            changesBetweenTiers.nr_of_team_members_affected -
            usersSelected.length
          } users to remove`;
          hasError = true;
        }
      }

      setError(newError);
      if (hasError) {
        return;
      }

      const response = await removeTeamMembers({
        current_tier: user.tier_type,
        new_tier: newTier === 'cancel' ? 'free' : newTier,
        remove_user_ids: usersToRemove,
        check: true,
      });
      if (response.status === 204) {
        handleNextStep('FINAL');
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectUser = (user_id) => {
    if (usersSelected.includes(user_id)) {
      setUsersSelected(usersSelected.filter((id) => id !== user_id));
    } else {
      setUsersSelected([...usersSelected, user_id]);
    }
  };

  useEffect(() => {
    getAllTeamMembers();
    setTitle('');
  }, [changesBetweenTiers, team?.isOwner?.id]);

  return (
    <>
      {/* Header with title and close button */}
      <div className="absolute top-[-45px] left-0 right-0 -mt-14 bg-grey-5 rounded-t-size1 border border-grey-5 border-b-0 flex items-center py-size1 px-size3">
        <div className="flex items-center gap-size2">
          <div className="bg-purple-5 rounded-full p-[6px] flex items-center justify-center">
            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-purple-200">
              <UserGroupIcon className="w-4 h-4 text-white" />
            </div>
          </div>
          <span className="font-medium text-sm">Remove Team Members</span>
        </div>
        <button
          onClick={handleClose}
          className="ml-auto text-grey-50 hover:text-grey-75 text-xl font-bold"
          aria-label="Close"
        >
          ×
        </button>
      </div>

      {newTier !== 'cancel' && (
        <div className="flex flex-col gap-size2 mb-size4">
          <header className="flex flex-col gap-size1">
            <h3 className="text-lg font-semibold">
              The {newTier} tier allows a maximum of {changesBetweenTiers.total_members} team {changesBetweenTiers.total_members === 1 ? 'member' : 'members'}. Please select {changesBetweenTiers.nr_of_team_members_affected} team member{changesBetweenTiers.nr_of_team_members_affected === 1 ? '' : 's'} to remove, to proceed with your downgrade.
            </h3>
            <p className="text-sm">
              {usersSelected.length}/
              {changesBetweenTiers.nr_of_team_members_affected}
            </p>
          </header>
          <div className="flex flex-col gap-size2 ">
            {allTeamMembers.map((member) => (
              <DCheckbox
                key={member.user_id}
                label={`${member.user_data.first_name} ${member.user_data.last_name}`}
                checked={usersSelected.includes(member.user_id)}
                onChange={() => handleSelectUser(member.user_id)}
                hideError
              />
            ))}
          </div>
        </div>
      )}

      {newTier === 'cancel' && (
        <div className="flex flex-col gap-size4 pb-size4">
          <header className="flex flex-col gap-size2">
            <div className="flex flex-col gap-size2 text-grey-75">
              <h2 className="text-lg font-semibold">By proceeding with cancellation:</h2>
              <ul className="list-disc pl-size4">
                <li>All team members will lose access to the team workspace</li>
                <li>Team data and settings will be permanently deleted</li>
                <li>This action cannot be reversed</li>
              </ul>
            </div>
          </header>
        </div>
      )}
      {error && <p className="text-error">{error}</p>}
      <footer className="flex flex-col gap-size2">
        <DButton
          variant="grey"
          size="lg"
          onClick={handleNext}
          fullWidth
          loading={isLoading}
          disabled={
            newTier !== 'cancel' &&
            usersSelected.length <
              changesBetweenTiers.nr_of_team_members_affected
          }
        >
          Continue canceling
        </DButton>
        <DButton
          variant="outlined"
          size="lg"
          onClick={handleClose}
          fullWidth
          disabled={isLoading}
        >
          I don't want to cancel
        </DButton>
      </footer>
    </>
  );
};

export default StepTeam;
