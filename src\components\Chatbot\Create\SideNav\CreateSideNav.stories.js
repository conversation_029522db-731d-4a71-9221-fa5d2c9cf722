import CreateSideNav from './CreateSideNav';

export default {
  title: 'Chatbot/SideNav',
  component: CreateSideNav,
  parameters: {},
  tags: ['autodocs'],
  argTypes: {},
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {}
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {}
};
