const NoZapIcon = (props) => {
  return (
    <svg
      width={20}
      height={20}
      {...props}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.27103 0.862931C7.39153 0.930107 7.45068 1.0708 7.41438 1.2039L6.39273 4.94996H10.125C10.2444 4.94996 10.3525 5.02084 10.4001 5.1304C10.4477 5.23996 10.4258 5.36733 10.3443 5.45466L8.18853 7.76428L10.7121 10.2878C10.8292 10.405 10.8292 10.5949 10.7121 10.7121C10.5949 10.8293 10.405 10.8293 10.2878 10.7121L1.28782 1.71209C1.17066 1.59494 1.17066 1.40499 1.28782 1.28783C1.40498 1.17067 1.59493 1.17067 1.71208 1.28783L4.22101 3.79675L6.90563 0.92027C6.99976 0.819414 7.15054 0.795754 7.27103 0.862931ZM4.64552 4.22127L5.70116 5.27691C5.69799 5.24177 5.701 5.20593 5.71052 5.17103L6.51619 2.21691L4.64552 4.22127ZM5.973 5.54875L7.76402 7.33976L9.43457 5.54996H5.99995C5.99091 5.54996 5.98192 5.54955 5.973 5.54875ZM3.40914 5.10614C3.53027 5.21919 3.53682 5.40903 3.42377 5.53015L2.5653 6.44996H4.62845C4.79414 6.44996 4.92845 6.58428 4.92845 6.74996C4.92845 6.91565 4.79414 7.04996 4.62845 7.04996H1.87495C1.75549 7.04996 1.64742 6.97909 1.59981 6.86953C1.5522 6.75997 1.57412 6.6326 1.65563 6.54527L2.98513 5.12077C3.09818 4.99964 3.28802 4.99309 3.40914 5.10614ZM5.78487 7.53803C5.94472 7.58162 6.03897 7.74654 5.99538 7.90638L5.48358 9.78329L6.52815 8.66425C6.6412 8.54313 6.83104 8.5366 6.95216 8.64966C7.07328 8.76272 7.07981 8.95255 6.96675 9.07367L5.09425 11.0797C5.00012 11.1805 4.84935 11.2042 4.72886 11.137C4.60838 11.0698 4.54923 10.9291 4.58552 10.796L5.41652 7.74854C5.46011 7.58869 5.62503 7.49444 5.78487 7.53803Z"
      />
    </svg>
  );
};

export default NoZapIcon;
