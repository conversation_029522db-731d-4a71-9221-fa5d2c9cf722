import { useEffect, useState } from 'react';
import DButtonIcon from '../DButtonIcon';
import DInputBlock from '../DInput/DInputBlock';
import DSelect from '../DSelect';
import DeleteIcon from '../Icons/DeleteIcon';
import DragIcon from '../Icons/DragIcon';
import DInput from '../DInput/DInput';
import DTextArea from '../DInput/DTextArea';
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from '@headlessui/react';
import ChevronDownIcon from '../Icons/ChevronDownIcon';

const LeadGenInput = ({
  item,
  onEdit,
  onDelete,
  className,
  error,
  dragHandleProps,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newData, setNewData] = useState(item);
  const [defaultOpen, setDefaultOpen] = useState(!item?.input_label);
  /**
   * Whenever user changes a field, update local state and immediately inform
   * the parent so the parent can keep the "source of truth" in sync.
   */
  const handleChange = (field, value) => {
    const updatedItem = { ...newData, [field]: value };
    setNewData(updatedItem);
    onEdit?.(item?.id, updatedItem);
  };

  return (
    <Disclosure
      as="div"
      className={`p-size2 ${
        isEditing ? 'bg-grey-2' : 'bg-transparent'
      } ${className}`}
      defaultOpen={defaultOpen}
    >
      {({ open }) => (
        <>
          <DisclosureButton className="flex justify-between items-center w-full">
            <div className="flex gap-size1 items-center">
              <div {...dragHandleProps}>
                <DragIcon />
              </div>
              <p className="text-sm font-regular tracking-tight">
                {newData?.input_label || '()'}{' '}
                <span className="text-grey-20 text-xs font-regular tracking-tight">
                  ({newData?.input_type})
                </span>
              </p>
            </div>
            <div className="flex gap-size1 items-center">
              <ChevronDownIcon
                className={`transition-transform duration-200 size-3 ${
                  open ? 'rotate-180' : 'rotate-0'
                }`}
              />
              <DButtonIcon
                variant="outlined"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(item?.id);
                }}
              >
                <DeleteIcon />
              </DButtonIcon>
            </div>
          </DisclosureButton>
          <DisclosurePanel
            transition
            className="origin-top transition duration-200 ease-out data-[closed]:-translate-y-6 data-[closed]:opacity-0"
          >
            {' '}
            <div className="flex flex-col gap-size4 py-size4 bg-grey-5 px-size0 mt-size1">
              <DInputBlock label="Input type">
                <DSelect
                  options={[
                    {
                      value: 'text',
                      label: 'Text',
                    },
                    {
                      value: 'email',
                      label: 'Email',
                    },
                    {
                      value: 'phone',
                      label: 'Phone',
                    },
                    {
                      value: 'custom',
                      label: 'Custom',
                    },
                  ]}
                  value={newData?.input_type}
                  onChange={(value) => handleChange('input_type', value)}
                />
              </DInputBlock>
              <DInputBlock label="Label">
                <div className="flex flex-col gap-size0">
                  <DInput
                    value={newData?.input_label}
                    onChange={(e) =>
                      handleChange('input_label', e.target.value)
                    }
                  />
                  <p className="text-xs font-regular tracking-tight text-grey-20">
                    *This will label the data type on your end.
                  </p>
                </div>
              </DInputBlock>
              <DInputBlock label="Message">
                <DTextArea
                  value={newData?.input_placeholder}
                  onChange={(e) =>
                    handleChange('input_placeholder', e.target.value)
                  }
                  placeholder="e.x. Please enter your email address."
                />
              </DInputBlock>
            </div>
          </DisclosurePanel>
        </>
      )}
    </Disclosure>
  );
};

export default LeadGenInput;
