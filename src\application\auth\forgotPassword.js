import * as authService from '@/services/auth.service';
import { useUserStore } from '@/stores/user/userStore';

import getUserInfo from '../user/getUserInfo';
import getUserProfile from '../user/getUserProfile';

const forgotPasswordUseCase = async ({ email }) => {
  const saveAuthDetail = useUserStore.getState().saveAuthDetail;
  const setUser = useUserStore.getState().setUser;

  try {
    const response = await authService.forgotPassword({
      email
    });
    let userInfo;

    const res = response.data;

    if (response.status === 200) {
      const auth = {};
      auth.access_token = res.access_token;
      saveAuthDetail(auth);

      userInfo = await getUserInfo(res.access_token);

      auth.user_id = userInfo.id;
      auth.first_name = userInfo.first_name;
      auth.last_name = userInfo.last_name;
      auth.email = userInfo.email;
      auth.date_created = userInfo.date_created;
      auth.login_count = userInfo.login_count;

      const userProfile = await getUserProfile();

      saveAuthDetail(auth);
      setUser({ ...userInfo, ...userProfile });
      return { data: { ...res, ...userInfo } };
    }

    return {};
  } catch (error) {
    console.log(error);
    return error;
  }
};

export default forgotPasswordUseCase;
