import React, { useEffect, useState } from 'react';
import { Line } from 'react-chartjs-2';
import { DateTime } from 'luxon';
import 'chart.js/auto';

const defaultDataChart = {
  labels: [],
  datasets: [],
};

let width, height, gradient;
function getGradient(ctx, chartArea, status = 'Active') {
  const chartWidth = chartArea.right - chartArea.left;
  const chartHeight = chartArea.bottom - chartArea.top;
  if (!gradient || width !== chartWidth || height !== chartHeight) {
    // Create the gradient because this is either the first render
    // or the size of the chart has changed
    width = chartWidth;
    height = chartHeight;
    gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
    gradient.addColorStop(
      0,
      status === 'Active' ? 'rgba(130, 117, 247, 0)' : 'rgba(0, 0, 0, 0)'
    );

    gradient.addColorStop(
      1,
      status === 'Active' ? 'rgba(130, 117, 247, 0.1)' : 'rgba(0, 0, 0, 0.2)'
    );
  }

  return gradient;
}
const ChatbotShortcutsChart = ({ options = {}, data = [], status }) => {
  const [correctData, setCorrectData] = useState(defaultDataChart);
  const [dates, setDates] = useState([' ', ' ']);
  // Track whether the theme is dark
  const [isDarkTheme, setIsDarkTheme] = useState(localStorage.getItem('theme') === 'dark');

  // Listen for theme changes via custom or storage events
  useEffect(() => {
    const handleThemeChange = () => {
      setIsDarkTheme(localStorage.getItem('theme') === 'dark');
    };

    // Listen for a custom 'themeChanged' event and the native 'storage' event
    window.addEventListener('themeChanged', handleThemeChange);
    window.addEventListener('storage', handleThemeChange);

    return () => {
      window.removeEventListener('themeChanged', handleThemeChange);
      window.removeEventListener('storage', handleThemeChange);
    };
  }, []);

  useEffect(() => {
    const labels = [];
    const newDates = [...dates];
    // Adjust the background color based on the theme.
    const dataset = {
      data: [],
      label: '',
      borderRadius: {
        bottomLeft: 0,
        bottomRight: 0,
        topLeft: 100,
        topRight: 100,
      },
      borderWidth: 0,
      // Change the background color according to the current theme
      backgroundColor: isDarkTheme ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
      fill: true,
      pointStyle: false,
    };

    data?.forEach((date, i) => {
      const formattedDate = DateTime.fromISO(date.date).toFormat('MMM dd');
      labels.push(formattedDate);
      if (i === 0) {
        newDates[0] = formattedDate;
      }
      if (i === data.length - 1) {
        newDates[1] = formattedDate;
      }
      dataset.data.push(date.value);
    });

    setCorrectData({
      labels,
      datasets: [dataset],
    });
    setDates(newDates);
  }, [data, isDarkTheme]); // Re-run when the data or theme changes

  return (
    <div className="flex flex-col gap-size2 w-full bg-grey-2 rounded-size0 min-h-[103px] px-size1 py-size2">
      <div
        className="flex flex-col gap-size2"
        style={{ position: 'relative', width: '100%', aspectRatio: '300/50' }}
      >
        <div className="flex justify-between">
          <div className="value text-xs text-grey-20">{dates[0]}</div>
          <div className="value text-xs text-grey-20">{dates[1]}</div>
        </div>
        <Line
          options={{
            responsive: true,
            aspectRatio: 300 / 50,
            layout: {
              autoPadding: false,
              padding: {
                left: -8,
                right: -8,
              },
            },
            plugins: {
              legend: false,
              title: false,
              tooltip: false,
            },
            scales: {
              x: {
                grid: { display: false },
                ticks: { display: false, maxTicksLimit: 3, padding: 0 },
                border: { display: false },
              },
              y: {
                display: false,
                grid: { display: false },
                ticks: { padding: 0 },
              },
            },
            ...options,
          }}
          data={correctData}
        />
      </div>
    </div>
  );
};

export default ChatbotShortcutsChart;
