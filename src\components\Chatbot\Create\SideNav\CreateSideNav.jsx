import { useEffect, useRef, useState } from 'react';

import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';

import { getCompletedSteps, getTotalSteps } from '../../../../helpers/stepsFunc.js';
import Step from '../../Step/index.jsx';
import { createSteps, customizeSteps } from '../steps.js';

import '../index.css';

const CreateSideNav = () => {
  //store
  const steps = useChatbotStore((state) => state.steps);
  const currentStep = useChatbotStore((state) => state.currentStep);
  const setCurrentStep = useChatbotStore((state) => state.setCurrentStep);

  const createSteps = steps.createSteps;
  const customizeSteps = steps.customizeSteps;

  const isAboveSm = useIsAboveBreakpoint('sm');

  const totalCreateSteps = getTotalSteps(createSteps);
  const completedCreateSteps = getCompletedSteps(createSteps);

  const totalCustomizeSteps = getTotalSteps(customizeSteps);
  const completedCustomizeSteps = getCompletedSteps(customizeSteps);

  const handleStepClick = (stepId) => {
    setCurrentStep(stepId);
  };

  return (
    <div className="create_nav_wrapper w-full md:max-w-[216px] md:flex md:flex-col gap-size5 md:py-size5 md:px-size1 md:h-full">
      <span className="text-xl font-medium tracking-tight hidden md:block">Create AI Chatbot</span>
      <div className="w-full  h-px bg-grey-5 hidden md:block"></div>
      {(isAboveSm || (!isAboveSm && currentStep <= 2)) && (
        <div className="create_nav-create-steps flex flex-col gap-size3 mb-size5 md:mb-0">
          <div className="flex items-center justify-between">
            <span className="text-lg font-regular tracking-tight">Train</span>
            <span className="text-sm text-grey-20 tracking-tight">
              ({completedCreateSteps}/{totalCreateSteps})
            </span>
          </div>
          <div className="no-scrollbar flex md:flex-col gap-size1 overflow-auto md:overflow-hidden">
            {createSteps.map((step) => (
              <Step
                {...step}
                key={step.id}
                onClick={() => handleStepClick(step.id)}
                className="max-w-[170px] md:max-w-auto"
              />
            ))}
          </div>
        </div>
      )}
      <div className="h-px w-full bg-grey-5 hidden md:block"></div>
      {(isAboveSm || (!isAboveSm && currentStep > 2)) && (
        <div className="create_nav-customize-steps flex flex-col gap-size3 mb-size5 md:mb-0">
          <div className="flex items-center justify-between">
            <span className="text-lg font-regular tracking-tight">Customize</span>
            <span className="text-sm text-grey-20 tracking-tight">
              ({completedCustomizeSteps}/{totalCustomizeSteps})
            </span>
          </div>
          <div className="no-scrollbar flex md:flex-col gap-size1 overflow-auto md:overflow-hidden">
            {customizeSteps.map((step) => (
              <Step {...step} key={step.id} onClick={() => handleStepClick(step.id)} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateSideNav;
