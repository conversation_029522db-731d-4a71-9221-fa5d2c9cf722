import { useUserStore } from '@/stores/user/userStore';
import { useNavigate } from 'react-router-dom';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';
import AnimatedAddChatbotIcon from '@/components/Global/Icons/PlusBoxIcon';
import checkEnv from '@/helpers/environmentCheck';
import { trackCreateChatbotClick } from '@/helpers/analytics';
import './index.css'

const FirstTimePage = () => {
  const { user } = useUserStore((state) => state);
  const navigate = useNavigate();

  return (
    <div className="h-full w-full relative">
      <div className="h-full flex flex-col items-center justify-center">
        <div className='absolute top-[20%] right-[0px] left-[0px] mx-auto w-full flex items-center justify-center'>
          <DFullLogo className="w-[120px]" />
        </div>

        <div className="bg-white rounded-size1 w-[300px] h-[300px]">
          <button
            className={'dbutton flex flex-col items-center justify-center rounded-size1 w-full h-full text-grey-20 text-lg md:text-xl transition-all duration-300 !hover:bg-gray-50'}
            onClick={() => {
              if (checkEnv()) {
                trackCreateChatbotClick({ user_id: user?.email });
              }
              navigate('/chatbot/create');
            }}
          >
            <AnimatedAddChatbotIcon className="w-[40px] h-[40px] icon-transition" />
            <span className="mt-4 text-grey-50 font-medium text-xs">New AI Chatbot</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default FirstTimePage;
