import { execSync } from 'child_process';
import { createHash } from 'crypto';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';
import tailwindcss from 'tailwindcss';
import { defineConfig } from 'vite';
import svgr from 'vite-plugin-svgr';

import react from '@vitejs/plugin-react-swc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const commitHash = execSync('git rev-parse --short HEAD').toString().trim();

const generateHash = (input, length = 6) => {
  return createHash('md5').update(input).digest('hex').substr(0, length);
};

// Adding a timestamp to ensure uniqueness for each build
const buildTimestamp = new Date().toISOString().replace(/[:.]/g, '-');

export default defineConfig({
  define: {
    COMMIT_HASH: JSON.stringify(commitHash),
    BUILD_TIMESTAMP: JSON.stringify(buildTimestamp) // Add this line
  },
  plugins: [svgr(), react(
  )],
  build: {
    minify: false, // disable minification only for test chat bubble
    assetsInlineLimit: 1024,
    rollupOptions: {
      input: {
        // all routes arrive in `/embed/xxxx`, will need to be handled by embed.html
        embed: resolve(__dirname, 'embed.html'),
        // all routes arrive in `/share/xxxx`, will need to be handled by share.html
        share: resolve(__dirname, 'share.html'),
        // Catch-all route
        main: resolve(__dirname, 'index.html'),
      },
      output: {
        entryFileNames: `assets/[name]-[hash]-${buildTimestamp}.js`, // Modified line
        chunkFileNames: (chunkInfo) => {
          const codeConcat = Object.values(chunkInfo.moduleIds)
            .map((m) => m.code)
            .join();
          return `assets/[name]-${generateHash(codeConcat)}-${buildTimestamp}.js`; // Modified line
        },
        assetFileNames: (assetInfo) =>
          `assets/[name]-${generateHash(assetInfo.source)}-${buildTimestamp}.[ext]` // Modified line
      }
    },
    modulePreload: false,
    outDir: 'build',
    cssCodeSplit: true,
    emptyOutDir: true,
    sourcemap: true
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    fs: {
      strict: true
    }
  },
  css: {
    postcss: {
      plugins: [tailwindcss()]
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/'),
      '@components': resolve(__dirname, 'src/components'),
      '@assets': resolve(__dirname, 'src/assets'),
      '@features': resolve(__dirname, 'src/features'),
      '@hooks': resolve(__dirname, 'src/hooks'),
      '@services': resolve(__dirname, 'src/services')
    }
  }
});
