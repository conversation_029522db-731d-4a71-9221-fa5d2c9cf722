import React from 'react';
import './BlobAnimation.css'; // Reusing the existing CSS

const BlobButton = ({ className = '', color = 'default' }) => {
  // Get color theme based on provided prop
  const getColorClassName = () => {
    switch (color) {
      case 'user':
        return 'user-speaking';
      case 'bot':
        return 'bot-speaking';
      case 'calling':
        return 'calling';
      default:
        return 'idle';
    }
  };

  // Simple animation speed and scale functions
  const getAnimationSpeed = () => {
    return '8s';
  };

  const getScaleFactor = () => {
    return 1;
  };

  return (
    <div className={`blob-animation-container ${className}`}>
      <div className={`blobs ${getColorClassName()}`} style={{
        transform: `scale(${getScaleFactor()})`,
        transition: 'transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) 0.2s'
      }}>
        <svg viewBox="0 0 1200 1200" style={{ width: '100%', height: '100%' }}>
          <g className="blob blob-1" style={{ '--animation-duration': getAnimationSpeed() }}>
            <path d="M 100 600 q 0 -500, 500 -500 t 500 500 t -500 500 T 100 600 z" />
          </g>
          <g className="blob blob-2" style={{ '--animation-duration': getAnimationSpeed() }}>
            <path d="M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z" />
          </g>
          <g className="blob blob-3" style={{ '--animation-duration': getAnimationSpeed() }}>
            <path d="M 100 600 q 0 -400, 500 -500 t 400 500 t -500 500 T 100 600 z" />
          </g>
          <g className="blob blob-4" style={{ '--animation-duration': getAnimationSpeed() }}>
            <path d="M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z" />
          </g>
          <g className="blob blob-1 alt" style={{ '--animation-duration': getAnimationSpeed() }}>
            <path d="M 100 600 q 0 -500, 500 -500 t 500 500 t -500 500 T 100 600 z" />
          </g>
          <g className="blob blob-2 alt" style={{ '--animation-duration': getAnimationSpeed() }}>
            <path d="M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z" />
          </g>
          <g className="blob blob-3 alt" style={{ '--animation-duration': getAnimationSpeed() }}>
            <path d="M 100 600 q 0 -400, 500 -500 t 400 500 t -500 500 T 100 600 z" />
          </g>
          <g className="blob blob-4 alt" style={{ '--animation-duration': getAnimationSpeed() }}>
            <path d="M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z" />
          </g>
        </svg>
      </div>
    </div>
  );
};

export default BlobButton; 