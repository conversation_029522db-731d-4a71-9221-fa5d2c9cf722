import * as authService from '@/services/auth.service';
import * as userService from '@/services/user.service';
import { useUserStore } from '@/stores/user/userStore';
import getUserInfo from '../user/getUserInfo';
import getUserProfile from '../user/getUserProfile';
import { updateUserDataLayer, trackLogin } from '@/helpers/analytics';

// Helper function to pad numbers with leading zeros
const padZero = (num) => {
  return num.toString().padStart(2, '0');
};

// Format date including time (for dates already in UTC from backend)
const formatDateWithTime = (date) => {
  return `${padZero(date.getMonth() + 1)}/${padZero(date.getDate())}/${date.getFullYear()} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
};

// Helper function to format date with time in UTC (for new Date() calls)
const formatDateWithTimeUTC = (date) => {
  return `${padZero(date.getUTCMonth() + 1)}/${padZero(date.getUTCDate())}/${date.getUTCFullYear()} ${padZero(date.getUTCHours())}:${padZero(date.getUTCMinutes())}:${padZero(date.getUTCSeconds())}`;
};

const loginUseCase = async ({ username, password, otp_code }) => {
  const saveAuthDetail = useUserStore.getState().saveAuthDetail;
  const setUser = useUserStore.getState().setUser;

  try {
    const response = await authService.login({ username, password, otp_code });
    let userInfo;

    const res = response.data;

    if (response.status === 200) {
      const auth = {};
      auth.access_token = res?.access_token;
      auth.date_created = res?.date_created;

      const replaceUrlResponse = await userService.replaceUrl({
        from_new_design: true,
        access_token: res?.access_token
      });

      if(replaceUrlResponse?.status === 200 && replaceUrlResponse.data.redirect === true){
        window.location.href = replaceUrlResponse.data.redirect_url;
        return;
      }
      saveAuthDetail(auth);


      const onboardingStatus = await userService.getOnboardingStatus({ access_token: res?.access_token });

      userInfo = await getUserInfo(res.access_token);
      const userProfile = await getUserProfile();

      auth.user_id = userInfo.id;
      auth.first_name = userInfo.first_name;
      auth.last_name = userInfo.last_name;
      auth.email = userInfo.email;
      auth.date_created = userInfo.date_created;
      auth.login_count = userInfo.login_count;

      setUser({ ...userInfo, ...userProfile });

      // Format previous login date if available
      let previousLoginDate = '';
      if (res.previous_login_date) {
        const prevLogin = new Date(res.previous_login_date);
        previousLoginDate = formatDateWithTime(prevLogin);
      }

      // Track login event
      trackLogin({
        email: userInfo.email,
        total_logins: userInfo.login_count,
        last_login_date: previousLoginDate
      });

      // Update dataLayer with user information
      const updatedUser = { ...userInfo, ...userProfile };
      updateUserDataLayer(updatedUser, auth);

      return { data: { ...res, ...userInfo }, onboardingCompleted: onboardingStatus.data.onboarding_completed, status: response.status };
    } else if (response.status === 428) {
      return { status: 428 };
    } else if (response.status === 412) {
      return { status: 412 };
    }
    return {};
  } catch (error) {
    const { response } = error;
    // Request OTP
    if (response?.status === 428) {
      return { status: 428 };
    }
    console.error(error);
    return error;
  }
};

export default loginUseCase;
