import { render, fireEvent, screen, cleanup } from '@testing-library/react';
import DTooltip from './index';
import { describe, it, expect, beforeEach } from 'vitest';

describe('DTooltip Component', () => {
  // Ensure cleanup before each test to remove previous renders
  beforeEach(() => {
    cleanup(); // Cleans up any previous renders
  });

  it('renders the children element', () => {
    render(<DTooltip content="Tooltip content">Hover me</DTooltip>);
    expect(screen.getByText('Hover me')).toBeInTheDocument();
  });

  it('displays tooltip content on hover', () => {
    render(<DTooltip content="Tooltip content">Hover me</DTooltip>);

    const triggerElement = screen.getByText('Hover me');

    // Simulate mouse hover
    fireEvent.mouseEnter(triggerElement);

    // Check if tooltip content is displayed
    expect(screen.getByText('Tooltip content')).toBeInTheDocument();
  });

  it('hides tooltip content when mouse leaves', () => {
    render(<DTooltip content="Tooltip content">Hover me</DTooltip>);

    const triggerElement = screen.getByText('Hover me');

    // Simulate mouse hover
    fireEvent.mouseEnter(triggerElement);
    expect(screen.getByText('Tooltip content')).toBeInTheDocument();

    // Simulate mouse leave
    fireEvent.mouseLeave(triggerElement);

    // Tooltip content should not be visible after mouse leave
    expect(screen.queryByText('Tooltip content')).not.toBeInTheDocument();
  });

  it('renders tooltip in specified position', () => {
    render(
      <DTooltip content="Tooltip content" position="bottom start">
        Hover me
      </DTooltip>
    );

    const triggerElement = screen.getByText('Hover me');

    // Simulate mouse hover
    fireEvent.mouseEnter(triggerElement);

    // Check if tooltip content is displayed
    const tooltip = screen.getByText('Tooltip content');
    expect(tooltip).toBeInTheDocument();

    // Verify custom position class or attribute if applicable
    expect(tooltip).toHaveAttribute('anchor', 'bottom start');
  });
});
