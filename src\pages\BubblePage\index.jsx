import { useEffect, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import convertThemeToCSSVariablesStyle from '@/helpers/convertThemeToCSSVariables';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import * as customizationService from '@/services/customization.service';
import generateGoogleFonts from '@/helpers/generateGoogleFonts';
import { DANTE_THEME_CHAT } from '@/constants';
import StyleTag from '@/components/StyleTag';
import BubblePopup from '@/components/BubblePopup';
import DTransition from '@/components/Global/DTransition';
import BubbleLoadingWrapper from '@/components/BubbleLoadingWrapper';

const BubblePage = () => {
  const [searchParams] = useSearchParams();

  const { data: customizationData, isLoading } = useDanteApi(
    customizationService.getSharedChatbotCustomizationById,
    [],
    {},
    {
      kb_id: searchParams.get('kb_id'),
      token: searchParams.get('token'),
    }
  );

  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const resetSelectedChatbot = useChatbotStore(
    (state) => state.resetSelectedChatbot
  );
  const resetCustomization = useCustomizationStore((state) => state.reset);

  const setCustomization = useCustomizationStore(
    (state) => state.setChatbotCustomization
  );

  const customization = useCustomizationStore(
    (state) => state.chatbotCustomization
  );

  const saveSignUpBtn = useChatbotStore((state) => state.saveSignUpBtn);

  useEffect(() => {
    resetSelectedChatbot();
    resetCustomization();
  }, []);

  useEffect(() => {
    const kb_id = searchParams.get('kb_id') || '';
    const token = searchParams.get('token') || '';
    const bubble_open = searchParams.get('bubbleopen') === 'true';
    const llmModel =
      searchParams.get('model_type') || searchParams.get('modeltype') || '';

    setSelectedChatbot({ kb_id, token, bubble_open, llmModel });
  }, [searchParams, setSelectedChatbot]);

  // Validate required parameters
  const kb_idValid = selectedChatbot?.kb_id === searchParams.get('kb_id');
  const tokenValid = selectedChatbot?.token === searchParams.get('token');

  const customizationFontFamily = useMemo(() => {
    return generateGoogleFonts(
      customization.font_name || DANTE_THEME_CHAT.font_family
    );
  }, [customization]);

  useEffect(() => {
    if (customizationData) {
      setCustomization(customizationData);
    }
  }, [customizationData]);

  if (!kb_idValid || !tokenValid) {
    return null; // Return null if required parameters are invalid
  }

  return (
    <>
      <StyleTag tag="body" tempCustomizationData={customization} customImport={customizationFontFamily} />
      <BubbleLoadingWrapper
        config={{ ...selectedChatbot, ...customization }}
        isLoading={isLoading}
        customizationData={customizationData}
        customizationFontFamily={customizationFontFamily}
        isPreviewMode={false}
        isInApp={false}
        initialShouldFetchCustomization={false}
      />
      <DTransition
        show={saveSignUpBtn}
        className="absolute bottom-0 left-0 right-0 h-full flex flex-col gap-size1 backdrop-blur-sm justify-end"
      >
        <BubblePopup />
      </DTransition>
    </>
  );
};

export default BubblePage;
