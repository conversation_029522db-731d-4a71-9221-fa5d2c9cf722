import { fn } from '@storybook/test';

import DSwitch from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Global/DSwitch',
  component: DSwitch,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    checked: {
      default: false,
      control: { type: 'boolean' }
    },
    disabled: {
      default: false,
      control: { type: 'boolean' }
    }
  },
  args: {
    checked: false,
    disabled: false,
    onChange: fn()
  }
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    disabled: false,
    checked: false,
    label: 'Checkbox'
  }
};
export const Checked = {
  args: {
    disabled: false,
    checked: true,
    label: 'Checkbox'
  }
};

export const DefaultDisabled = {
  args: {
    disabled: true,
    checked: false,
    label: 'Checkbox'
  }
};
export const CheckedDisabled = {
  args: {
    disabled: true,
    checked: true,
    label: 'Checkbox'
  }
};
