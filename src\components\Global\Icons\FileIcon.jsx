const FileIcon = (props) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.5 1C1.67157 1 1 1.67157 1 2.5V11.5C1 12.3284 1.67157 13 2.5 13H3V8.5C3 7.67157 3.67157 7 4.5 7H9.5C10.3284 7 11 7.67157 11 8.5V13H11.5C12.3284 13 13 12.3284 13 11.5V4.41421C13 4.28161 12.9473 4.15443 12.8536 4.06066L9.93934 1.14645C9.84557 1.05268 9.7184 1 9.58579 1H2.5ZM10 13V8.5C10 8.22386 9.77614 8 9.5 8H4.5C4.22386 8 4 8.22386 4 8.5V13H10ZM0 2.5C0 1.11929 1.11929 0 2.5 0H9.58579C9.98361 0 10.3651 0.158035 10.6464 0.43934L13.5607 3.35355C13.842 3.63486 14 4.01639 14 4.41421V11.5C14 12.8807 12.8807 14 11.5 14H2.5C1.11929 14 0 12.8807 0 11.5V2.5Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default FileIcon;
