// components/OTPForm.js

import { useState } from 'react';
import DButton from '@/components/Global/DButton';
import OTPInputs from '../OTPInputs';

const OTPForm = ({
  error,
  handleSubmit,
  handleChange,
  handleGoBack,
  length = 6,
  pending = false,
}) => {
  const [otpCode, setOtpCode] = useState('');

  // Callback to receive OTP code from OTPInputs
  const handleOtpChange = (code) => {
    setOtpCode(code);
    handleChange({ target: { name: 'otp_code', value: code } });
  };

  return (
    <form
      className="flex flex-col gap-size5 items-center w-full"
      onSubmit={handleSubmit}
    >
      <div className="w-full text-center">
        <h1 className="text-3xl">Verify It’s You</h1>
        <h2 className="text-lg text-grey-50 mt-2">
          Open your authenticator app to get the code
        </h2>
      </div>

      <div className="flex flex-col gap-size3 w-full">
        {/* Reusable OTP Input Boxes */}
        <OTPInputs length={length} onChange={handleOtpChange} handleSubmit={handleSubmit}/>

        {/* Action Buttons */}
        <div className="flex gap-size3 w-full">
          <DButton
            variant="grey"
            size="sm"
            className="w-max"
            onClick={handleGoBack}
          >
            Go Back
          </DButton>
          <DButton
            type="submit"
            variant="dark"
            size="sm"
            fullWidth
            loading={pending}
          >
            Verify
          </DButton>
        </div>

        {/* Error Message */}
        {error && <p className="text-error mt-2">{error}</p>}
      </div>
    </form>
  );
};

export default OTPForm;
