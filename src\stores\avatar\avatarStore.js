import { create } from 'zustand';

export const useAvatarStore = create((set) => ({
  avatars: [],
  avatarStep: 0,
  selectedAvatar: {},
  saveAvatars: (avatars) => set({ avatars }),
  setSelectedAvatar: (selectedAvatar) => set({ selectedAvatar }),
  saveSelectedAvatar: (selectedAvatar) => set({ selectedAvatar }),
  resetAvatars: () => set({ avatars: [] }),
  resetSelectedAvatar: () => set({ selectedAvatar: {} }),
  resetAvatar: () => set({ avatar: {} })
}));
