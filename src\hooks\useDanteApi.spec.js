import { describe, expect, it, vi } from 'vitest';

import { act, renderHook } from '@testing-library/react-hooks';

import useDante<PERSON><PERSON> from './useDanteApi';

// Mocking the service function
const mockServiceFunction = vi.fn();

describe('useDanteApi', () => {
  it('should return loading state initially', async () => {
    // Mock the service function to resolve after a delay
    mockServiceFunction.mockResolvedValueOnce({ data: 'test data' });

    // Use renderHook to render the hook
    const { result, waitForNextUpdate } = renderHook(() =>
      useDanteApi(mockServiceFunction, 'arg1', 'arg2')
    );

    // Initial state should have isLoading true and other states null or undefined
    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBeNull();
    expect(result.current.error).toBeNull();
    expect(result.current.response).toBeNull();

    // Wait for the next update when the API call finishes
    await waitForNextUpdate();

    // After the API call completes
    expect(result.current.isLoading).toBe(false);
    expect(result.current.data).toBe('test data');
    expect(result.current.error).toBeNull();
    expect(result.current.response).toEqual({ data: 'test data' });
  });

  it('should set error state when the service function throws an error', async () => {
    // Mock the service function to reject with an error
    mockServiceFunction.mockRejectedValueOnce(new Error('Failed to fetch'));

    const { result, waitForNextUpdate } = renderHook(() =>
      useDanteApi(mockServiceFunction, 'arg1', 'arg2')
    );

    // Initially, isLoading should be true
    expect(result.current.isLoading).toBe(true);

    // Wait for the next update when the API call finishes
    await waitForNextUpdate();

    // After the API call fails
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe('Failed to fetch');
    expect(result.current.response).toBeNull();
    expect(result.current.data).toBeNull();
  });

  it('should return correct data when serviceFunction is called with different arguments', async () => {
    // Mock different data based on arguments
    mockServiceFunction.mockImplementationOnce((arg1, arg2) =>
      Promise.resolve({ data: `${arg1}-${arg2}` })
    );

    const { result, waitForNextUpdate } = renderHook(() =>
      useDanteApi(mockServiceFunction, 'foo', 'bar')
    );

    await waitForNextUpdate();

    // After the API call completes, expect the data to be based on arguments passed
    expect(result.current.data).toBe('foo-bar');
    expect(result.current.response).toEqual({ data: 'foo-bar' });
  });
});
