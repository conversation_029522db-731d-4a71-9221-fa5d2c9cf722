import http from './http';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';

export const getStatistics = (kb_id, date_from, date_to, timezone) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'statistics/' + kb_id, {
    params: {
      date_from,
      date_to,
      timezone,
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const getGeneralData = (kb_id, timezone) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'statistics/' + kb_id + '/general', {
    params: {
      timezone,
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};
