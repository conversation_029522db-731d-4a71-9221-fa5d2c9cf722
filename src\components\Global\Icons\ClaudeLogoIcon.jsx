import * as React from 'react';

const ClaudeLogoIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g>
      <path
        d="M5.746 17.377c.929 0 1.843-.015 2.754.008.314.008.368-.195.442-.4.282-.789.572-1.575.823-2.372.106-.338.268-.457.615-.452q2.513.025 5.022.002c.318-.003.466.095.566.42.246.8.563 1.577.82 2.374.097.298.222.437.554.429.869-.025 1.738-.008 2.602-.008.129-.206.011-.369-.043-.523-1.738-4.785-3.486-9.565-5.215-14.351-.138-.385-.326-.52-.728-.502-.757.034-1.517.017-2.277.005-.268-.003-.449.034-.56.338-1.786 4.932-3.583 9.86-5.375 14.793-.015.04-.002.089-.002.237m7.133-11.487c.622 1.766 1.188 3.377 1.757 4.985.077.22.218.442-.195.44-1.138-.008-2.28-.003-3.492-.003zM0 17.377c.972 0 1.862-.009 2.751.005.245.005.357-.097.437-.317Q5.873 9.658 8.563 2.257c.022-.058.002-.132.002-.245-.891 0-1.78.018-2.671-.006-.434-.012-.434.335-.525.585C3.791 6.902 2.225 11.217.657 15.532c-.215.592-.423 1.185-.657 1.843"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ClaudeLogoIcon;
