import React, { useEffect, useState } from 'react';
import DesktopPlans from '@/components/DesktopPlans';
import MobilePlans from '@/components/MobilePlans';
import useDanteApi from '@/hooks/useDanteApi';
import * as plansService from '@/services/plans.service';
import DLoading from '@/components/DLoading';
const AllPlans = ({ handleUpgrade, handleDowngrade }) => {
    const { data: plans } = useDanteApi(plansService.getPlans);
    const { data: tiers } = useDanteApi(plansService.getTiers);

    const [period, setPeriod] = useState('monthly');
    const [selectedMessages, setSelectedMessages] = useState(10000);
    const [selectedPlan, setSelectedPlan] = useState('Starter');

    if (!plans || !tiers) {
        return <DLoading show={true} />
    }

    return  <div className="container mx-auto">
        <div className="hidden md:block">
            <DesktopPlans
                tiers={tiers ? tiers[period] : []}
                plans={plans}
                period={period}
                setPeriod={setPeriod}
                selectedMessages={selectedMessages}
                setSelectedMessages={setSelectedMessages}
                onUpgrade={handleUpgrade}
                onDowngrade={handleDowngrade}
            />
        </div>
        <div className="block md:hidden">
            <MobilePlans
                tiers={tiers ? tiers[period] : []}
                plans={plans}
                period={period}
                setPeriod={setPeriod}
                selectedPlan={selectedPlan}
                setSelectedPlan={setSelectedPlan}
                selectedMessages={selectedMessages}
                setSelectedMessages={setSelectedMessages}
                onUpgrade={handleUpgrade}
                onDowngrade={handleDowngrade}
            />
        </div>
    </div>
};

export default AllPlans;