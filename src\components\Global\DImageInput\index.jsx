import { useRef } from 'react';

import ImageIcon from '../Icons/ImageIcon';

const DImageInput = ({ onChange, value }) => {
  const fileInputRef = useRef(null);

  return (
    <div className="flex gap-size1 rounded-size1 border border-grey-5 p-size2">
      {value ? (
        <div className="flex items-center gap-size1">
          <img 
            src={typeof value === 'string' ? value : URL.createObjectURL(value)} 
            alt="Uploaded image" 
            className="h-8 w-8 object-cover rounded-size0"
          />
          <div
            className="text-sm font-medium tracking-tight cursor-pointer text-black"
            onClick={() => fileInputRef.current.click()}
          >
            Change image
          </div>
        </div>
      ) : (
        <>
          <ImageIcon />
          <div
            className="text-sm font-medium tracking-tight cursor-pointer text-grey-20"
            onClick={() => fileInputRef.current.click()}
          >
            Upload image
          </div>
        </>
      )}
      <input 
        type="file" 
        className="hidden" 
        ref={fileInputRef} 
        onChange={onChange}
        accept="image/png,image/jpeg,image/jpg" 
      />
    </div>
  );
};

export default DImageInput;
