import DTooltip from '../Global/DTooltip';
import InfoIcon from '../Global/Icons/InfoIcon';
import DCheckbox from '../Global/DCheckbox';

const FilterCard = ({
  number,
  label,
  tooltip,
  showCheckbox = true,
  onClick,
  checked,
  className,
}) => {
  const handleCardClick = () => {
    onClick(!checked);
  };

  const handleCheckboxChange = (e) => {
    e.stopPropagation();
    onClick(!checked);
  };

  return (
    <div
      className={`flex justify-between items-start p-size3 w-full ${
        showCheckbox ? 'cursor-pointer' : ''
      } ${className} ${checked ? 'bg-purple-100' : 'bg-grey-2'}`}
      onClick={handleCardClick}
    >
      <div className="flex flex-col gap-size1 w-full">
        <p className="text-2xl font-medium">{number}</p>
        <div className="flex items-center gap-size0">
          <p className="text-sm font-regular tracking-tight text-grey-50">
            {label}
          </p>
          {tooltip && (
            <DTooltip content={tooltip}>
              <InfoIcon className="size-3 text-grey-20" />
            </DTooltip>
          )}
        </div>
      </div>
      {showCheckbox && (
        <DCheckbox
          className="!justify-end"
          onChange={handleCheckboxChange}
          checked={checked}
          style={{ width: 'auto' }}
        />
      )}
    </div>
  );
};

export default FilterCard;
