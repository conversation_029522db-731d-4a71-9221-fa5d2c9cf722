const convertCustomCss = (knowledgeBase) => {
      let outputString = knowledgeBase.custom_css;
  
      const lines = outputString?.split('\n');
      let insideKeyframes = false;
      let braceCount = 0;
      const processedLines = [];
  
      const keyframesStartRegex = /^\s*@(-webkit-)?keyframes\s+/;
      const openBraceRegex = /{/g;
      const closeBraceRegex = /}/g;
  
      lines?.forEach((line) => {
        // Check for the start of a keyframes block
        if (keyframesStartRegex.test(line)) {
          insideKeyframes = true;
        }
  
        // Update the brace count before processing the line
        const openBraces = (line.match(openBraceRegex) || []).length;
        const closeBraces = (line.match(closeBraceRegex) || []).length;
        braceCount += openBraces - closeBraces;
  
        if (insideKeyframes) {
          // We're inside a keyframes block, so just add the line as is
          processedLines.push(line);
  
          // If we've exited the keyframes block
          if (braceCount <= 0) {
            insideKeyframes = false;
            braceCount = 0; // Reset brace count after exiting keyframes
          }
        } else {
          // We're outside a keyframes block, so process the line
          // Match property declarations and add !important
          const propertyRegex = /([^\s][^:]*?):\s*([^;{]+)(;?)/g;
          const selectorRegex = /^([^{}]+){/; // Matches the start of a selector block
  
          if (selectorRegex.test(line)) {
            // Handle lines that start with selectors
            processedLines.push(line);
          } else {
            // Handle property declarations
            const newLine = line.replace(propertyRegex, (match, propName, propValue, semicolon) => {
              // Avoid adding !important to pseudo-elements or selector parts
              if (propName.includes(':')) {
                return match; // Skip adding !important to selectors or pseudo-elements
              }
  
              // Avoid adding !important if it's already there
              if (propValue.includes('!important')) {
                return `${propName}: ${propValue.trim()}${semicolon}`;
              } else {
                return `${propName}: ${propValue.trim()} !important${semicolon}`;
              }
            });
            processedLines.push(newLine);
          }
        }
      });
  
      outputString = processedLines.join('\n');
  
      return outputString;
};

  export default convertCustomCss;