import DLoaderTraining from '@/components/Global/DLoaderTraining';
import { acceptInvitationMemberOrganization } from '@/services/human-handover-organization';
import { useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import useToast from '@/hooks/useToast';

const VerifyHumanHandoverEmail = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { addSuccessToast } = useToast();
  const token = searchParams.get('token');
  const organization_id = searchParams.get('organization_id');

  const validateInvitation = async () => {
    try {
      const response = await acceptInvitationMemberOrganization(
        organization_id,
        token
      );
      console.log(response);
      if (response.status === 200) {
        navigate('/log-in');
        addSuccessToast({ message: 'Your invitation has been accepted.' });
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    validateInvitation();
  }, [token, organization_id]);

  return (
    <div className="h-screen w-screen flex items-center justify-center">
      <DLoaderTraining />
    </div>
  );
};

export default VerifyHumanHandoverEmail;
