import { useCallback, useRef } from 'react';

const useDebounce = (func, delay) => {
  const timerRef = useRef(null);

  const debouncedFunction = useCallback((...args) => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    timerRef.current = setTimeout(() => {
      func(...args);
    }, delay);
  }, [func, delay]);

  return debouncedFunction;
};

export default useDebounce;
