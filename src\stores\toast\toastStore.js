import { STATUS } from '@/constants';
import { create } from 'zustand';

const initialState = {
  toasts: [
    // {
    //   id: '1',
    //   message: 'This is a test toast',
    //   type: STATUS.SUCCESS,
    //   display: 'show',
    // },
  ],
  timers: {},
};

const useToastStore = create((set, get) => ({
  ...initialState,

  addToast: (toast) => {
    const existingToast = useToastStore.getState().toasts.some((t) => t.message === toast.message);
    if (existingToast) return;

    const toastWithId = {
      ...toast,
      display: 'show',
      id: toast.id || Date.now().toString(),
      state: toast.type === STATUS.SUCCESS ? 'positive' : (toast.type === STATUS.FAILURE || toast.type === STATUS.ERROR) ? 'negative' : toast.type === STATUS.WARNING ? 'alert' : undefined,
    };

    // Clear any existing timers for this toast
    const { timers } = get();
    if (timers[toastWithId.id]) {
      clearTimeout(timers[toastWithId.id]);
    }

    // Remove any existing toasts to ensure only one is shown at a time
    set((state) => ({
      toasts: [toastWithId],
    }));
    
    // Note: We're no longer setting up auto-removal timeouts here
    // as the component handles this with the countdown animation
  },

  removeToast: (toastId) => {
    // Clear any existing timers for this toast
    const { timers } = get();
    if (timers[toastId]) {
      clearTimeout(timers[toastId]);
    }
    
    set((state) => ({
      toasts: state.toasts.filter((toast) => toast.id !== toastId),
      timers: { ...state.timers, [toastId]: null },
    }));
  },

  updateToast: (toastId, updatedToast) =>
    set((state) => ({
      toasts: state.toasts.map((t) => (t.id === toastId ? { ...t, ...updatedToast } : t)),
    })),
    
  reset: () => {
    // Clear all timers
    const { timers } = get();
    Object.values(timers).forEach(timer => {
      if (timer) clearTimeout(timer);
    });
    
    set({ ...initialState });
  }
}));

export default useToastStore;
