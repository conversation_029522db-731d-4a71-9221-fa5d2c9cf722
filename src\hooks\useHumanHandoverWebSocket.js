import { useEffect, useRef, useState, useCallback } from 'react';
import { HumanHandoverWebSocketService } from '@/services/websocket';
import { useUserStore } from '@/stores/user/userStore';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';

/**
 * Hook for managing human handover WebSocket connections
 *
 * @param {Object} config - Configuration for the WebSocket
 * @param {string} config.conversationId - ID of the conversation
 * @param {string} config.token - Authentication token
 * @param {boolean} config.isInApp - Whether this is in the app (vs. shared/embedded)
 * @param {boolean} config.isInHumanHandoverApp - Whether this is in the human handover app
 * @param {Function} config.onMessageReceived - Callback for when a message is received
 * @param {Function} config.onAgentJoined - Callback for when an agent joins
 * @param {Function} config.onAgentResolved - Callback for when an agent resolves the conversation
 * @param {Object} [config.wsRef] - Optional ref to store the WebSocket instance
 * @returns {Object} WebSocket state and methods
 */
const useHumanHandoverWebSocket = (config) => {
  const internalWsRef = useRef(null);
  const wsRef = config.wsRef || internalWsRef;
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState(null);
  const userData = useUserStore((state) => state.user);
  const liveAgentData = useHumanHandoverStore((state) => state.userOrganization);
  const setLiveAgentData = useHumanHandoverStore((state) => state.setUserOrganization);

  // Initialize WebSocket connection
  const connect = useCallback((conversationId) => {
    if (!conversationId) {
      console.error('Cannot connect: Missing conversation ID');
      setError(new Error('Missing conversation ID'));
      return;
    }

    // Don't connect if already connecting or connected
    if (isConnecting || (wsRef.current && wsRef.current.getState().isConnected)) {
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      // Close any existing connection
      if (wsRef.current) {
        wsRef.current.disconnect();
        wsRef.current = null;
      }

      // Create WebSocket URL
      const wsUrl = `${import.meta.env.VITE_APP_BASE_WEBSOCKET}/human-handover/conversations/${conversationId}/chat`;

      // Create WebSocket service
      wsRef.current = new HumanHandoverWebSocketService(
        {
          url: wsUrl,
          authToken: config.token,
          isShared: !config.isInApp,
          isInHumanHandoverApp: config.isInHumanHandoverApp
        },
        {
          onOpen: () => {
            setIsConnected(true);
            setIsConnecting(false);
          },
          onClose: (event) => {
            setIsConnected(false);
            setIsConnecting(false);
          },
          onError: (event) => {
            setError(event.error || new Error('WebSocket error'));
            setIsConnecting(false);
          },
          onMessageReceived,
          onAgentJoined,
          onAgentResolved
        }
      );

      // Connect to the WebSocket server
      wsRef.current.connect().catch((error) => {
        console.error('Error connecting to WebSocket:', error);
        setError(error);
        setIsConnecting(false);
      });
    } catch (error) {
      console.error('Error initializing WebSocket:', error);
      setError(error);
      setIsConnecting(false);
    }
  }, [config]);

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.disconnect();
      wsRef.current = null;
      setIsConnected(false);
    }
  }, []);

  // Send a message
  const sendMessage = useCallback((text, isQuickResponse = false, images = []) => {
    if (!wsRef.current) {
      console.error('Cannot send message: WebSocket not initialized');
      return false;
    }

    const additionalData = {};

    // Add agent info if in human handover app
    if (config.isInHumanHandoverApp && liveAgentData && config.isInApp) {
      additionalData.agent_name = liveAgentData.name;
      additionalData.agent_profile_pic = liveAgentData.image;
    }

    return wsRef.current.sendUserMessage(text, isQuickResponse, images, additionalData);
  }, [config.isInHumanHandoverApp, userData, liveAgentData]);

  // Send a control message (human_handover_taken or human_handover_resolved)
  const sendControlMessage = useCallback((type, conversationId, additionalData = {}) => {
    if (!wsRef.current) {
      console.error('Cannot send control message: WebSocket not initialized');
      return false;
    }

    return wsRef.current.sendControlMessage(type, conversationId, additionalData);
  }, []);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (wsRef.current) {
        wsRef.current.disconnect();
        wsRef.current = null;
      }
    };
  }, []);

  const onMessageReceived = useCallback((data) => {
    if (config.onMessageReceived) {
      console.log('data', data, config.isInApp);
      if (!config.isInApp) {
        setLiveAgentData({
          agent_name: data.data.agent_name,
          agent_profile_pic: data.data.agent_profile_pic
        });
      }
      config.onMessageReceived(data);
    }
  }, [config, setLiveAgentData]);

  const onAgentJoined = useCallback((data) => {
    if (config.onAgentJoined) {
      // Get the current liveAgentData from the store to ensure it's up-to-date
      const currentLiveAgentData = useHumanHandoverStore.getState().userOrganization;
      config.onAgentJoined({ data, infoAgent: currentLiveAgentData });
    }
  }, [config]);

  const onAgentResolved = useCallback((data) => {
    if (config.onAgentResolved) {
      // Get the current liveAgentData from the store to ensure it's up-to-date
      const currentLiveAgentData = useHumanHandoverStore.getState().userOrganization;
      config.onAgentResolved({ data, infoAgent: currentLiveAgentData });
    }
  }, [config]);

  return {
    connect,
    disconnect,
    sendMessage,
    sendControlMessage,
    isConnected,
    isConnecting,
    error
  };
};

export default useHumanHandoverWebSocket;
