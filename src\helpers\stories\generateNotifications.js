import { STATUS } from '@/constants';

export const fakeNotificationTasks = [
  {
    type: STATUS.WORKING,
    id: '1a2b3c4d-5e6f-7g8h-9i0j-k1l2m3n4o5p6',
    title: 'Working on project updates',
    status: STATUS.WORKING,
    updated_at: new Date('2024-10-29')
  },
  {
    type: STATUS.FAILURE,
    id: '7g8h9i0j-1a2b3c4d-5e6f-k1l2m3n4o5p6',
    title: 'Failed to deploy changes',
    status: STATUS.FAILURE,
    updated_at: new Date('2024-10-28')
  },
  {
    type: STATUS,
    id: '9i0j7g8h-5e6f-1a2b3c4d-k1l2m3n4o5p6',
    title: 'Reviewing project status',
    status: STATUS,
    updated_at: new Date('2024-10-27')
  }
];

export const fakeNotificationUpdates = [
  {
    is_read: true,
    id: '5e6f7g8h-9i0j-1a2b3c4d-k1l2m3n4o5p6',
    message: 'Project updates reviewed and approved.',
    created_at: new Date('2024-10-25'),
    link: 'https://example.com/update1',
    link_text: 'Read more'
  },
  {
    is_read: false,
    id: '1a2b3c4d-5e6f7g8h-9i0j-k1l2m3n4o5p6',
    message: 'New changes pushed to repository.',
    created_at: new Date('2024-10-26'),
    link: 'https://example.com/update2',
    link_text: 'Details'
  },
  {
    is_read: false,
    id: '9i0j1a2b-5e6f7g8h-3c4dk1l2m3n4o5p6',
    message: 'Deployment failed due to server error.',
    created_at: new Date('2024-10-27'),
    link: 'https://example.com/update3',
    link_text: 'Troubleshoot'
  }
];
