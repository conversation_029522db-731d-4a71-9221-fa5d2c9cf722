import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest';

import { render } from '@testing-library/react';

import App from './App';

describe('App Component', () => {
  let originalLocation;

  beforeEach(() => {
    // Clear all mocks and localStorage before each test
    vi.clearAllMocks();
    localStorage.clear();

    // Store the original window.location so we can restore it later
    originalLocation = window.location;

    // Mock the window.location object
    delete window.location;
    window.location = {
      origin: 'https://staging.dante-ai.com',
      href: '',
    };
  });

  afterEach(() => {
    // Restore the original window.location after each test
    window.location = originalLocation;
  });

  const mockLocationOrigin = (origin) => {
    window.location.origin = origin;
  };

  test('should redirect to Google if wrong password is entered on correct origin', () => {
    // Mock window.location.origin to 'https://staging.dante-ai.com'
    mockLocationOrigin('https://staging.dante-ai.com');

    // Mock prompt to return incorrect password
    window.prompt = vi.fn().mockReturnValue('wrongPassword');
    window.alert = vi.fn();

    render(<App />);

    // Asserting prompt and alert were called
    expect(window.prompt).toHaveBeenCalled();
    expect(window.alert).toHaveBeenCalledWith(
      'Incorrect password. You are not authorized to use this app.'
    );

    // Assert that window.location.href was set to Google
    expect(window.location.href).toBe('https://www.google.com/');
  });

  test('should store auth time if correct password is entered on correct origin', () => {
    // Mock window.location.origin to 'https://staging.dante-ai.com'
    mockLocationOrigin('https://staging.dante-ai.com');

    // Mock prompt to return correct password
    window.prompt = vi.fn().mockReturnValue('Dante@111');
    window.alert = vi.fn();

    render(<App />);

    // Assert prompt was called
    expect(window.prompt).toHaveBeenCalled();

    // Assert localStorage was updated with the current time
    const authTime = localStorage.getItem('lastAuthTime');
    expect(authTime).toBeTruthy();
  });

  test('should not prompt for password if already authenticated on correct origin', () => {
    // Mock window.location.origin to 'https://staging.dante-ai.com'
    mockLocationOrigin('https://staging.dante-ai.com');

    const mockTime = new Date().getTime();
    localStorage.setItem('lastAuthTime', mockTime.toString());

    // Render the component without expecting any prompt
    render(<App />);

    // Assert prompt and alert were not called
    expect(window.prompt).not.toHaveBeenCalled();
    expect(window.alert).not.toHaveBeenCalled();
  });

  test('should prompt for password if auth expired on correct origin', () => {
    // Mock window.location.origin to 'https://staging.dante-ai.com'
    mockLocationOrigin('https://staging.dante-ai.com');

    const mockExpiredTime = new Date().getTime() - 3 * 60 * 60 * 1000; // 3 hours ago
    localStorage.setItem('lastAuthTime', mockExpiredTime.toString());

    // Mock prompt
    window.prompt = vi.fn().mockReturnValue('Dante@111');

    render(<App />);

    // Assert prompt was called due to expired auth
    expect(window.prompt).toHaveBeenCalled();
  });

  test('should not prompt if origin is not staging or cloudfront', () => {
    // Mock window.location.origin to 'https://some-other-origin.com'
    mockLocationOrigin('https://some-other-origin.com');

    // Mock prompt and alert to ensure they are not called
    window.prompt = vi.fn();
    window.alert = vi.fn();

    render(<App />);

    // Assert prompt and alert were not called since the origin is not valid
    expect(window.prompt).not.toHaveBeenCalled();
    expect(window.alert).not.toHaveBeenCalled();
  });
});
