import { useState, useEffect } from 'react';

import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DModal from '@/components/Global/DModal';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { useUserStore } from '@/stores/user/userStore';
import clsx from 'clsx';
import UserPlusIcon from '@/components/Global/Icons/UserPlusIcon';
import LinkIcon from '@/components/Global/Icons/LinkIcon';

const AddMember = ({
  open,
  onClose,
  onSubmit,
  newMember,
  setNewMember,
  roles,
  loading,
  error,
  teamOwner,
  teamData,
  setOpenEditRole,
  setOpenDeleteRole,
  setSelectedRole,
  setOpenAddRole,
  teams
}) => {
  const [showAdditionalFields, setShowAdditionalFields] = useState(false);
  const { user } = useUserStore();
  const chatbots = useChatbotStore((state) => state.chatbots);

  // Check if email is valid to enable the invite button
  useEffect(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailRegex.test(newMember.email)) {
      setTimeout(() => setShowAdditionalFields(true), 150);
      
      // Set default values when email is valid
      if (roles?.length > 0 && !newMember.team_role_id) {
        // Set the first role as default
        setNewMember({ 
          ...newMember, 
          team_role_id: roles[2].id 
        });
      }
      
      if (chatbots?.length > 0 && (!newMember.allowed_kbs || newMember.allowed_kbs.length === 0)) {
        // Give access to all chatbots by default
        setNewMember({
          ...newMember,
          all_knowledge_bases: false,
          allowed_kbs: [],
          team_role_id: roles[2].id
        });
      }
    } else {
      setShowAdditionalFields(false);
    }
  }, [newMember.email, roles, chatbots]);

  const handleCopyProjectLink = () => {
    // Logic to copy project link to clipboard
    navigator.clipboard.writeText(window.location.href);
  };

  return (
    <DModal
      title="Invite collaborators"
      isOpen={open}
      onClose={onClose}
      subtitle={
        <div className="flex flex-col gap-size0">
          <p className="text-sm text-grey-50">
            Add teammates to your Dante AI workspace.
          </p>
          <p className="text-sm text-grey-50">
            Invite others to build and manage AI Chatbots with you.
          </p>
          <p className="text-sm text-grey-50">
            You can assign roles, set message limits, and control access from the <a href={`/team-management/${teams?.find(team => team.owner_id === teamOwner)?.id}`} className="text-purple-60">Team Dashboard</a>.
          </p>
        </div>
      }
    >
      <div className="flex flex-col gap-size4">
        {/* Email Input with Invite Button */}
        <div className="flex gap-size2">
          <div className="flex-grow relative">
            <DInput
              placeholder="Enter email address..."
              value={newMember.email}
              onChange={(e) => setNewMember({ ...newMember, email: e.target.value })}
              error={error?.email}
              className="pr-24"
            />
            {/* Role Dropdown */}
            {/* <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center">
              <div className="flex items-center gap-1 cursor-pointer text-gray-600">
                <span>Editor</span>
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div> */}
          </div>
          <div className="min-w-[100px]">
            <DButton
              onClick={onSubmit}
              variant="dark"
              size="sm"
              loading={loading}
              disabled={!showAdditionalFields}
              fullWidth
              className={clsx(
                'transition-all duration-300 h-full',
                showAdditionalFields ? 'opacity-100' : 'opacity-50'
              )}
            >
              Invite
            </DButton>
          </div>
        </div>

        {/* Owner Details - Always at bottom */}
        <div className="border-t border-grey-5 pt-size4 mt-auto">
          <p className="text-sm text-grey-50 mb-size3">Members</p>
          
          <div className="flex flex-col gap-size3">
            {/* Other Team Members */}
            {teamData?.filter(member => member.id !== teamOwner).map((member) => (
              <div key={member.id} className="flex items-center gap-size2">
                <img
                  src={member.user_data?.profile_image || member.profile_image}
                  alt={member.user_data?.first_name || member.full_name}
                  className="w-8 h-8 rounded-full object-cover bg-grey-5"
                />
                <div className="flex flex-col">
                  <p className="text-sm font-medium">
                    {member.user_data?.first_name || member.full_name} {member.user_data?.last_name || ''}
                  </p>
                  <p className="text-xs text-grey-50">
                    {member.user_data?.email}
                  </p>
                </div>
                <span className="ml-auto text-xs text-grey-50">
                  {member.user_data?.id === teamOwner?.id ? 'Owner' : 'Member'}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Copy Project Link Button */}
        {/* <DButton
          onClick={handleCopyProjectLink}
          variant="outline"
          fullWidth
          size="lg"
          className="mt-size4"
        >
          <div className="flex items-center justify-center gap-size2 text-purple-60">
            <LinkIcon className="w-5 h-5" />
            <span>Copy Project Link</span>
          </div>
        </DButton> */}
      </div>
    </DModal>
  );
};

export default AddMember;
