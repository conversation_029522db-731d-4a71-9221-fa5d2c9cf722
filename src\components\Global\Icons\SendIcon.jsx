import * as React from 'react';
const SendIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M3.89 19c-1.038 0-1.696-.35-2.114-.767-.816-.811-1.391-2.536.445-6.2l.808-1.596c.101-.213.101-.656 0-.867L2.22 7.973C.376 4.31.96 2.575 1.776 1.773 2.585.96 4.328.378 8.001 2.214l7.939 3.95c1.976.977 3.06 2.343 3.06 3.838s-1.086 2.86-3.052 3.838l-7.939 3.95C6.21 18.686 4.874 19 3.891 19m.002-16.61c-.501 0-.89.12-1.132.36-.676.665-.408 2.39.705 4.595l.808 1.606c.296.6.296 1.505 0 2.104l-.808 1.597c-1.113 2.214-1.381 3.93-.705 4.595.668.673 2.403.406 4.629-.701l7.938-3.95c1.457-.72 2.282-1.67 2.282-2.602s-.835-1.882-2.29-2.602l-7.94-3.94C5.97 2.75 4.754 2.39 3.892 2.39"
      fill="currentColor"
    />
    <path
      d="M12.103 10.695H7.095a.703.703 0 0 1-.695-.693c0-.378.316-.691.695-.691h5.008c.381 0 .697.313.697.691a.696.696 0 0 1-.697.693"
      fill="currentColor"
    />
  </svg>
);
export default SendIcon;
