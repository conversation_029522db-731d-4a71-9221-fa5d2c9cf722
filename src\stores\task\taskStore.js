// src/stores/task/taskStore.js
import { create } from 'zustand';

import { getTaskById } from '../../services/task.service'; // Ensure correct import path
import { TASK_POLLING_INTERVAL } from '@/constants';

const useTaskStore = create((set, get) => ({
  tasks: {}, // Dynamic key-value pairs for each task type
  pollingIntervals: new Map(), // To store intervals for each task

  /**
   * Adds a task to a specific task type.
   *
   * @param {string} type - The type of task (e.g., 'Suggestion', 'Training').
   * @param {Object} task - The task object to add.
   */
  addTask: (type, task) =>
    set((state) => ({
      tasks: {
        ...state.tasks,
        [type]: [...(state.tasks[type] || []), task]
      }
    })),

  /**
   * Removes a task from a specific task type by task ID.
   *
   * @param {string} type - The type of task.
   * @param {string} taskId - The ID of the task to remove.
   */
  removeTask: (type, taskId) =>
    set((state) => ({
      tasks: {
        ...state.tasks,
        [type]: (state.tasks[type] || []).filter((task) => task.id !== taskId)
      }
    })),

  /**
   * Updates an existing task by task ID.
   *
   * @param {string} type - The type of task.
   * @param {string} taskId - The ID of the task to update.
   * @param {Object} updatedFields - The fields to update in the task.
   */
  updateTask: (type, taskId, updatedFields) =>
    set((state) => ({
      tasks: {
        ...state.tasks,
        [type]: (state.tasks[type] || []).map((task) =>
          task.id === taskId ? { ...task, ...updatedFields } : task
        )
      }
    })),

  /**
   * Starts polling a task until its status is SUCCESS or a terminal state.
   *
   * @param {string} type - The type of task.
   * @param {string} taskId - The ID of the task to poll.
   * @param {number} [intervalTime=3000] - Polling interval in milliseconds.
   */
  startPollingTask: (type, taskId, intervalTime = TASK_POLLING_INTERVAL) => {
    const { pollingIntervals } = get();
    if (pollingIntervals.has(taskId)) {
      // Already polling this task
      return;
    }

    const intervalId = setInterval(async () => {
      try {
        const response = await getTaskById(taskId);
        const taskData = response.data;

        // Update task status and result in the store
        get().updateTask(type, taskId, { status: taskData.state, result: taskData.result });

        // Check for terminal states
        if (['SUCCESS', 'FAILURE', 'STOPPED'].includes(taskData.state)) {
          get().stopPollingTask(taskId);
        }
      } catch (error) {
        console.error(`Error polling task ${taskId}:`, error);
        get().stopPollingTask(taskId);
      }
    }, intervalTime);

    // Store the interval ID
    set((state) => {
      const newPollingIntervals = new Map(state.pollingIntervals);
      newPollingIntervals.set(taskId, intervalId);
      return { pollingIntervals: newPollingIntervals };
    });
  },

  /**
   * Stops polling a specific task.
   *
   * @param {string} taskId - The ID of the task to stop polling.
   */
  stopPollingTask: (taskId) => {
    const { pollingIntervals } = get();
    const intervalId = pollingIntervals.get(taskId);
    if (intervalId) {
      clearInterval(intervalId);
      set((state) => {
        const newPollingIntervals = new Map(state.pollingIntervals);
        newPollingIntervals.delete(taskId);
        return { pollingIntervals: newPollingIntervals };
      });
    }
  },

  /**
   * Clears all polling intervals. Useful for cleanup.
   */
  clearAllPolling: () => {
    const { pollingIntervals } = get();
    pollingIntervals.forEach((intervalId) => {
      clearInterval(intervalId);
    });
    set({ pollingIntervals: new Map() });
  },

  /**
   * Resets all tasks to an empty state.
   */
  resetTasks: () => set({ tasks: {} }) // Reset all task types
}));

export default useTaskStore;
