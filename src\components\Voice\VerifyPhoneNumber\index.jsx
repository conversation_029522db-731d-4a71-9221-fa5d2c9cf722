import DButton from '@/components/Global/DButton';
import DButtonIcon from '@/components/Global/DButtonIcon';
import DModal from '@/components/Global/DModal';
import CheckIcon from '@/components/Global/Icons/CheckIcon';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import useDanteApi from '@/hooks/useDanteApi';
import { getTwilioWebhookUrl, verifyPhoneNumber } from '@/services/phoneNumber.service';
import { useEffect, useState } from 'react';

const VerifyPhoneNumber = ({ open, onClose, phoneNumber, refetchData}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [twilioWebhookUrl, setTwilioWebhookUrl] = useState('');
    const [copied, setCopied] = useState(false);

    const handleCopy = (url) => {
        navigator.clipboard.writeText(url);
        setCopied(true);
        setTimeout(() => {
            setCopied(false);
        }, 2000);
    }

    const handleVerify = async () => {
        setIsLoading(true);

        try{
            const response = await verifyPhoneNumber(phoneNumber);
            if(response.status === 200){
                setTimeout(() => {
                    if (refetchData) {
                        refetchData();
                    }
                    onClose();
                }, 5000);
            }
        } catch (error) {
            console.log(error);
        }
    }

    useEffect(() => {
        const getWebhookUrl = async () => {
            const response = await getTwilioWebhookUrl(phoneNumber);
            if(response.status === 200){
                setTwilioWebhookUrl(response.data.url);
            }
        }
        if(open){
            getWebhookUrl();
        }
    }, [open]);

    return <DModal
        title="Verify phone number"
        isOpen={open}
        onClose={onClose}
        subtitle={<p>Please enter the URL below in your <a href="https://www.twilio.com/" target="_blank" rel="noopener noreferrer" className="text-black underline font-medium">Twilio</a> phone number settings for verification.<br/> If you need help, follow our <a href="https://www.dante-ai.com/guides/step-1-set-up-a-twilio-phone-number" target="_blank" rel="noopener noreferrer" className="text-black underline font-medium">step-by-step guide</a>.</p> }
        footer={
            <div className="flex gap-size1 items-center w-full">
                <DButton variant="outlined" onClick={onClose} fullWidth>Cancel</DButton>
                <DButton variant="dark" fullWidth onClick={handleVerify} loading={isLoading}>Verify</DButton>
            </div>
        }
    >
        <div className="flex items-center justify-between w-full py-size2 px-size1 rounded-size1 border border-grey-5">
            <p className="text-base font-regular truncate">{twilioWebhookUrl}</p>
            <DButtonIcon size="sm" onClick={() => handleCopy(twilioWebhookUrl)}>
                {copied ? <CheckIcon /> : <CopyIcon />}
            </DButtonIcon>
        </div>
    </DModal>
}

export default VerifyPhoneNumber;