import * as React from 'react';
const ThumbDownIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.294 16.386a1.03 1.03 0 0 1-.945.614 2.103 2.103 0 0 1-2.103-2.103V12.6a.15.15 0 0 0-.15-.15H5.819a2.45 2.45 0 0 1-2.422-2.823l.7-4.55A2.45 2.45 0 0 1 6.52 3h8.727a1.8 1.8 0 0 1 1.8 1.8v4.55a1.8 1.8 0 0 1-1.8 1.8h-1.528a.15.15 0 0 0-.137.09zM12.796 4H6.519a1.45 1.45 0 0 0-1.433 1.23l-.7 4.55a1.45 1.45 0 0 0 1.433 1.67h2.277c.635 0 1.15.515 1.15 1.15v2.297c0 .61.494 1.103 1.103 1.103a.03.03 0 0 0 .031-.02l2.287-5.147a1.2 1.2 0 0 1 .129-.22zm1 6.15V4h1.45a.8.8 0 0 1 .8.8v4.55a.8.8 0 0 1-.8.8z"
      fill="currentColor"
    />
  </svg>
);
export default ThumbDownIcon;
