// From https://casraf.dev/2023/11/create-your-own-use-optimistic-hook/
import { useState, useRef, useEffect, useCallback } from 'react';

/**
 * @template T, A
 * @typedef {function(T, A): T} StateUpdater
 */

/**
 * Custom hook for handling optimistic state updates.
 *
 * @template T, A
 * @param {T} initial - The initial state value.
 * @param {StateUpdater<T, A>=} merge - Optional function to merge the current state with the new state.
 * @returns {[
 *   T,
 *   React.Dispatch<React.SetStateAction<A>>,
 *   {
 *     done: () => void,
 *     rollback: () => void,
 *     pending: boolean,
 *     run: (promise: () => Promise<unknown> | unknown) => void
 *   }
 * ]}
 */
export function useOptimistic(initial, merge) {
  const [state, setState] = useState(initial);
  const rollbackState = useRef(null);
  const [pending, setPending] = useState(false);

  useEffect(() => {
    setPending(false);
    setState(initial);
  }, [initial]);

  /**
   * Updates the state optimistically.
   * @param {React.SetStateAction<A>} value - The new state or a function to compute the new state.
   */
  const update = useCallback(
    (value) => {
      const newValue = typeof value === 'function' ? value(state) : value;
      const merged = merge ? merge(state, newValue) : newValue;
      rollbackState.current = state;
      setState(merged);
      setPending(true);
    },
    [state, merge]
  );

  /**
   * Rolls back to the previous state.
   */
  const rollback = useCallback(() => {
    if (rollbackState.current == null) {
      return;
    }
    setState(rollbackState.current);
    rollbackState.current = null;
    setPending(false);
  }, []);

  /**
   * Marks the optimistic update as complete.
   */
  const done = useCallback(() => {
    rollbackState.current = null;
    setPending(false);
  }, []);

  /**
   * Runs an asynchronous operation and handles rollback on failure.
   * @param {() => Promise<unknown> | unknown} cb - The asynchronous operation to run.
   */
  const run = useCallback(
    async (cb) => {
      try {
        await cb();
        done();
      } catch (_) {
        rollback();
      }
    },
    [done, rollback]
  );

  return [state, update, { done, rollback, pending, run }];
}
