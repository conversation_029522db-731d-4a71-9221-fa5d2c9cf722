import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import DButton from '@/components/Global/DButton';
import XIcon from '@/components/Global/Icons/XIcon';

const Cancel = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const timer = setTimeout(() => {
      navigate('/');
    }, 5000);

    return () => clearTimeout(timer);
  }, [navigate]);

  return (
    <>
      <div className="flex justify-center items-center min-h-screen bg-white text-black">
        <div className="text-center flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center">
              <XIcon className="w-8 h-8 text-red-600" />
            </div>
          </div>
          <p className="text-2xl">Your subscription was not completed.</p>
          <p className="text-gray-600">The payment was cancelled or failed to process.</p>
          <DButton
            disableRipple
            className="w-36 mx-auto"
            onClick={() => navigate('/')}
            variant="contained"
          >
            Return to Dante AI
          </DButton>
          <p className="text-sm text-gray-500">
            <small>You'll be automatically redirected in a few seconds</small>
          </p>
        </div>
      </div>
    </>
  );
};

export default Cancel;
