import * as React from 'react';
const BookIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.556 3.834a.98.98 0 0 0-.982.981v8.649c.297-.17.634-.26.982-.26h9.87v-9.37zm-1.982.981v10.37a1.98 1.98 0 0 0 1.97 1.982l-1.97-1.982 1.982 1.982h10.37a.5.5 0 1 0 0-1H5.556a.982.982 0 0 1-.008-1.963h10.378a.5.5 0 0 0 .489-.606.5.5 0 0 0 .011-.106V3.778a.944.944 0 0 0-.944-.945H5.556a1.98 1.98 0 0 0-1.982 1.982m4.204.982a.5.5 0 0 0 0 1h4.444a.5.5 0 0 0 0-1z"
      fill="currentColor"
    />
  </svg>
);
export default BookIcon;
