import React, { useState, useEffect, useRef } from 'react';
import './BlobAnimation.css';

const BlobAnimation = ({ speaking, userAudioLevel, smoothedBotLevel, isCalling = false, className = '' }) => {
  // State for super-smoothed audio levels
  const [superSmoothedUserLevel, setSuperSmoothedUserLevel] = useState(0);
  const [superSmoothedBotLevel, setSuperSmoothedBotLevel] = useState(0);

  // Refs for animation control
  const animationFrameRef = useRef(null);
  const lastUpdateTimeRef = useRef(0);
  const userAudioHistoryRef = useRef([]);
  const botAudioHistoryRef = useRef([]);

  // Animation settings
  const historyLength = 10; // Length of moving average window
  const updateInterval = 100; // Check more frequently, but only update every 2 values
  const audioValueSkip = 2; // Only animate every 2nd audio value
  const audioValueCountRef = useRef(0); // Counter for audio values

  // Moving average filter for super-smooth audio levels
  useEffect(() => {
    const processAudioLevels = (timestamp) => {
      // Continue animation loop
      animationFrameRef.current = requestAnimationFrame(processAudioLevels);

      // Throttle updates based on time
      if (timestamp - lastUpdateTimeRef.current < updateInterval) return;
      lastUpdateTimeRef.current = timestamp;

      // Increment audio value counter - only process every 2nd audio value
      audioValueCountRef.current = (audioValueCountRef.current + 1) % audioValueSkip;
      if (audioValueCountRef.current !== 0) return;

      // Process user audio with moving average
      if (speaking === 'user') {
        // Add current level to history
        userAudioHistoryRef.current.push(userAudioLevel);

        // Keep history at fixed length
        if (userAudioHistoryRef.current.length > historyLength) {
          userAudioHistoryRef.current.shift();
        }

        // Calculate moving average
        const avgUserLevel = userAudioHistoryRef.current.reduce((sum, val) => sum + val, 0) /
                            userAudioHistoryRef.current.length;

        // Update with the averaged value (we're already skipping every other audio value)
        setSuperSmoothedUserLevel(avgUserLevel);
      } else {
        // Reset history when not speaking
        userAudioHistoryRef.current = [];
        // Decay smoothly when not speaking
        if (superSmoothedUserLevel > 0.05) {
          setSuperSmoothedUserLevel(prev => prev * 0.9);
        }
      }

      // Process bot audio with moving average
      if (speaking === 'bot') {
        // Add current level to history
        botAudioHistoryRef.current.push(smoothedBotLevel);

        // Keep history at fixed length
        if (botAudioHistoryRef.current.length > historyLength) {
          botAudioHistoryRef.current.shift();
        }

        // Calculate moving average
        const avgBotLevel = botAudioHistoryRef.current.reduce((sum, val) => sum + val, 0) /
                           botAudioHistoryRef.current.length;

        // Apply additional smoothing for bot audio - more gradual changes
        setSuperSmoothedBotLevel(prev => {
          // Use a stronger smoothing factor for bot audio (80% previous, 20% new)
          return prev * 0.8 + avgBotLevel * 0.2;
        });
      } else {
        // Reset history when not speaking
        botAudioHistoryRef.current = [];
        // Decay smoothly when not speaking
        if (superSmoothedBotLevel > 0.05) {
          setSuperSmoothedBotLevel(prev => prev * 0.9);
        }
      }
    };

    // Start animation loop
    animationFrameRef.current = requestAnimationFrame(processAudioLevels);

    // Cleanup
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [speaking, userAudioLevel, smoothedBotLevel]);

  // Get audio level based on speaking state
  const getAudioLevel = () => {
    if (speaking === 'user') {
      return superSmoothedUserLevel;
    } else if (speaking === 'bot') {
      return superSmoothedBotLevel;
    } else if (isCalling) {
      return 0.5; // Medium fixed level for calling
    }
    return 0.2; // Low level for idle
  };

  // Get color theme based on speaking state
  const getColorClassName = () => {
    if (speaking === 'user') {
      return 'user-speaking';
    } else if (speaking === 'bot') {
      return 'bot-speaking';
    } else if (isCalling) {
      return 'calling';
    }
    return 'idle';
  };

  // Calculate size for each circle based on audio level
  const audioLevel = getAudioLevel();
  const radius1 = 50 + audioLevel * 20; // Center circle
  const radius2 = 100 + audioLevel * 40; // Middle circle
  const radius3 = 150 + audioLevel * 60; // Outer circle

  return (
    <div className={`circle-animation-container ${className}`}>
      <div className={`circles ${getColorClassName()}`}>
        <svg 
          viewBox="0 0 400 400" 
          style={{ width: '100%', height: '100%' }}
        >
          {/* Outer ripple circle */}
          <circle 
            cx="200" 
            cy="200" 
            r={radius3}
            className="circle-outer"
          />
          
          {/* Middle ripple circle */}
          <circle 
            cx="200" 
            cy="200" 
            r={radius2}
            className="circle-middle"
          />
          
          {/* Inner solid circle */}
          <circle 
            cx="200" 
            cy="200" 
            r={radius1}
            className="circle-inner"
          />
        </svg>
      </div>
    </div>
  );
};

export default BlobAnimation;
