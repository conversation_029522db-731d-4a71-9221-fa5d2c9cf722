import { describe, expect, it, vi } from 'vitest';

import * as authService from '@/services/auth.service';
import { useUserStore } from '@/stores/user/userStore';

import getUserInfo from '../user/getUserInfo';

import loginGoogleUseCase from './loginGoogle';

// Mock functions in useUserStore, authService, and getUserInfo
vi.mock('@/stores/user/userStore', () => ({
  useUserStore: {
    getState: vi.fn()
  }
}));

vi.mock('@/services/auth.service', () => ({
  loginWithGoogle: vi.fn()
}));

vi.mock('../user/getUserInfo', () => ({
  default: vi.fn()
}));

describe('loginGoogleUseCase', () => {
  it('should save auth details and user info on successful Google login', async () => {
    const mockSaveAuthDetail = vi.fn();
    const mockSetUser = vi.fn();

    // Mock user store functions
    useUserStore.getState.mockReturnValue({
      saveAuthDetail: mockSaveAuthDetail,
      setUser: mockSetUser
    });

    // Mock the response from authService and getUserInfo
    authService.loginWithGoogle.mockResolvedValue({
      status: 200,
      data: { access_token: 'test_access_token' }
    });

    const mockUserInfo = {
      id: 1,
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      date_created: '2023-01-01',
      login_count: 5
    };
    getUserInfo.mockResolvedValue(mockUserInfo);

    const result = await loginGoogleUseCase({
      access_token: 'google_access_token',
      rewardful_referral: 'referral_code',
      contact_consent: true,
      kb_id: 'kb123',
      isLanding: true
    });

    expect(mockSaveAuthDetail).toHaveBeenCalledWith({
      access_token: 'test_access_token',
      user_id: mockUserInfo.id,
      first_name: mockUserInfo.first_name,
      last_name: mockUserInfo.last_name,
      email: mockUserInfo.email,
      date_created: mockUserInfo.date_created,
      login_count: mockUserInfo.login_count
    });
    expect(mockSetUser).toHaveBeenCalledWith(mockUserInfo);
    expect(result.data).toEqual({ access_token: 'test_access_token', ...mockUserInfo });
  });

  it('should return an empty object if the response status is not 200', async () => {
    authService.loginWithGoogle.mockResolvedValue({
      status: 400,
      data: {}
    });

    const result = await loginGoogleUseCase({
      access_token: 'google_access_token',
      rewardful_referral: 'referral_code',
      contact_consent: true,
      kb_id: 'kb123',
      isLanding: true
    });

    expect(result).toEqual({});
  });

  it('should return an error object if the API call fails', async () => {
    const error = new Error('Login with Google failed');
    authService.loginWithGoogle.mockRejectedValue(error);

    const result = await loginGoogleUseCase({
      access_token: 'google_access_token',
      rewardful_referral: 'referral_code',
      contact_consent: true,
      kb_id: 'kb123',
      isLanding: true
    });

    expect(result).toEqual(error);
  });
});
