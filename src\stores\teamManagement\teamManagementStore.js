import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const initialState = {
  teamName: '',
  owner: {
    id: null,
  },
  selectedTeam: null,
  userMode: true,
  teams: [],
};

const useTeamManagementStore = create(
  persist(
    (set) => ({
      ...initialState,
      setTeamName: (teamName) => set({ teamName }),
      setOwner: (owner) => set({ owner }),
      setTeams: (teams) => set({ teams }),
      setSelectedTeam: (selectedTeam) => set({ selectedTeam }),
      setUserMode: (userMode) => set({ userMode }),
      reset: () => set({ ...initialState })
    }),
    {
      name: 'team-management',
      storage: createJSONStorage(() => localStorage)
    }
  )
);

export default useTeamManagementStore;
