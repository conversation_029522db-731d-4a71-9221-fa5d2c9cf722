import { useState } from 'react';
import DModal from '@/components/Global/DModal';
import DInput from '@/components/Global/DInput/DInput';
import DButton from '@/components/Global/DButton';
import OTPInputs from '@/components/OTPInputs';
import DAlert from '@/components/Global/DAlert';
import { addPhoneNumber, getTwilioWebhookUrl, verifyPhoneNumber } from '@/services/phoneNumber.service';
import DButtonIcon from '@/components/Global/DButtonIcon';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import CheckIcon from '@/components/Global/Icons/CheckIcon';
import { useNavigate } from 'react-router-dom';

const AddPhoneNumberForm = ({ isOpen, onClose, refetchPhoneNumbers }) => {
    const navigate = useNavigate();
    const [phoneNumber, setPhoneNumber] = useState('');
    const [phoneNumberId, setPhoneNumberId] = useState(null);
    const [verifyScreen, setVerifyScreen] = useState(false);
    const [error, setError] = useState({
        phoneNumber: null,
        otp: null,
    });
    const [isLoading, setIsLoading] = useState(false);
    const [copied, setCopied] = useState(false);
    const [twilioWebhookUrl, setTwilioWebhookUrl] = useState('');
    const [isVerifying, setIsVerifying] = useState(false);

    const handleAdd = async () => {
        setIsLoading(true);
        if (phoneNumber.length === 0) {
            setIsLoading(false);
            setError({
                phoneNumber: 'Please enter a phone number',
                otp: null,
            });
            return;
        }

        try{
            const response = await addPhoneNumber(phoneNumber);
            if(response.status === 200){
                setPhoneNumberId(response.data.id);
                const responseTwilio = await getTwilioWebhookUrl(response.data.id);
                if(responseTwilio.status === 200){
                    setTwilioWebhookUrl(responseTwilio.data.url);
                    setVerifyScreen(true);
                    setIsLoading(false);
                }
            }
        } catch (error) {
            console.log(error);
        } finally {
            setIsLoading(false);
        }
    }

    const handleVerify = async () => {
        setIsVerifying(true);
        try{
            const response = await verifyPhoneNumber(phoneNumberId);
            if(response.status === 200){
                setTimeout(() => {
                    if(refetchPhoneNumbers){
                        refetchPhoneNumbers();
                    }
                    setIsVerifying(false);
                    onClose();
                    navigate('/phone-numbers');
                }, 5000);
            }
        } catch (error) {
            setIsVerifying(false);
            setError({
                phoneNumber: null,
                otp: error.response.data.message,
            });
        } finally {
            setIsVerifying(false);
        }
    }

    const handleResend = () => {
        console.log('resend');
    }

    const handleCopy = (text) => {
        navigator.clipboard.writeText(text);
        setCopied(true);
        setTimeout(() => {
            setCopied(false);
        }, 2000);
    }

    return <DModal
        title={verifyScreen ? 'Verify new number' : 'Add new number'}
        isOpen={isOpen}
        onClose={() => {
            setPhoneNumberId(null);
            setPhoneNumber('');
            setVerifyScreen(false);
            onClose();
        }}
        subtitle={verifyScreen ? 
            <p>Please enter the URL below in your <a href="https://www.twilio.com/" target="_blank" rel="noopener noreferrer" className="text-black underline font-medium">Twilio</a> phone number settings for verification.<br/> If you need help, follow our <a href="https://www.dante-ai.com/guides/step-1-set-up-a-twilio-phone-number" target="_blank" rel="noopener noreferrer" className="text-black underline font-medium">step-by-step guide</a>.</p> 
            : 
            <p>Create your custom phone number on <a href="https://www.twilio.com/" target="_blank" rel="noopener noreferrer" className="text-black underline font-medium">Twilio</a> and paste it below. <br/> Need help? Check our <a href="https://www.dante-ai.com/guides/step-1-set-up-a-twilio-phone-number" target="_blank" rel="noopener noreferrer" className="text-black underline font-medium">step-by-step guide</a>.</p>
        }
        footer={
            <div className="flex flex-col gap-size1 w-full">
                <div className="flex gap-size1 items-center w-full">
                    <DButton variant="outlined" onClick={() => {
                        setPhoneNumberId(null);
                        setVerifyScreen(false);
                        onClose();
                    }} fullWidth>Cancel</DButton>
                    {!verifyScreen && <DButton variant="dark" fullWidth onClick={handleAdd} loading={isLoading}>Add</DButton>}
                    {verifyScreen && <DButton variant="dark" fullWidth onClick={handleVerify} loading={isVerifying}>Verify</DButton>}
                </div>
                {/* {verifyScreen && <div className="flex flex-col gap-size5 w-full mt-size7">
                    <p className="text-base font-regular ">Didn’t receive the code? <span className="text-purple-300 font-medium" onClick={handleResend}>Resend</span></p>
                    <DAlert state='positive' className="!w-full">
                        Code is successfully sent to your phone number.
                    </DAlert>
                </div>} */}
            </div>
        }
    >
        <div className="flex flex-col gap-size5 w-full mb-size3">
            {verifyScreen && 
                <div className="flex items-center justify-between w-full py-size2 px-size1 rounded-size1 border border-grey-5">
                    <p className="text-base font-regular truncate no-wrap">{twilioWebhookUrl}</p>
                    <DButtonIcon  size="sm" onClick={() => handleCopy(twilioWebhookUrl)}>
                        {copied ? <CheckIcon /> : <CopyIcon />}
                    </DButtonIcon>
                </div>}
            {verifyScreen && <div className="w-full h-px bg-grey-5"></div>}
            {!verifyScreen && <div className="flex flex-col gap-size1">
                <div className="flex flex-col">
                    <p className="text-base font-regular">Phone number</p>
                    <p className="text-xs text-grey-50 tracking-tight font-light">
                       Please include the country code and area code (e.g. ****** 780 3514).
                    </p>
                </div>
                <DInput 
                    placeholder="****** 567 890"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    error={error.phoneNumber}
                />
                <p className="text-sm text-grey-50 tracking-tight font-light">Don't have a Twilio account? <a href="/phone-numbers/purchase" target="_blank" rel="noopener noreferrer" className="text-black underline font-medium">Purchase a new number via Dante AI</a>.</p>
            </div>}
        </div>

    </DModal>
};

export default AddPhoneNumberForm;