/**
 * Validates a password based on common security criteria:
 * - At least one uppercase letter
 * - At least one lowercase letter
 * - At least one number
 * - At least one symbol
 * - Minimum length of 8 characters
 *
 * @param {string} value - The password to validate.
 * @returns {Object} An object containing the validation status and any error messages.
 * @returns {boolean} return.isValid - True if the password is valid; otherwise, false.
 * @returns {string[]} return.errorMessages - Array of error messages, empty if the password is valid.
 */

const validatePassword = (value) => {
  // If the password is empty, don't show any validation errors
  if (!value) {
    return {
      isValid: false,
      errorMessages: []
    };
  }

  const hasUpperCase = /[A-Z]/;
  const hasLowerCase = /[a-z]/;
  const hasNumber = /[0-9]/;
  const hasSymbol = /[$&+,:;=?@#|'<>.^*()%!-]/;
  const minLength = 8;

  const cases = [];

  cases.push({ case: 'Password should contain at least one uppercase letter', status: !hasUpperCase.test(value) });
  cases.push({ case: 'Password should contain at least one lowercase letter', status: !hasLowerCase.test(value) });
  cases.push({ case: 'Password should contain at least one number', status: !hasNumber.test(value) });
  cases.push({ case: 'Password should contain at least one symbol', status: !hasSymbol.test(value) });
  cases.push({ case: `Password should be at least ${minLength} characters long`, status: value.length < minLength });

  return {
    isValid: !cases.find((c) => c.status === true),
    errorMessages: cases
  };
};

export default validatePassword;
