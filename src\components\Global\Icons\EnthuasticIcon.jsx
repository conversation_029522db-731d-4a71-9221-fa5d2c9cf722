const EnthuasticIcon = (props) => {
  return (
    <svg
      width="24"
      height="14"
      viewBox="0 0 24 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M22.5 13.25V11.75C22.5 8.96523 21.3938 6.29451 19.4246 4.32538C17.4555 2.35625 14.7848 1.25 12 1.25C9.21523 1.25 6.54451 2.35625 4.57538 4.32538C2.60625 6.29451 1.5 8.96523 1.5 11.75V13.25M19.5 13.25V11.75C19.5 9.76088 18.7098 7.85322 17.3033 6.4467C15.8968 5.04018 13.9891 4.25 12 4.25C10.0109 4.25 8.10322 5.04018 6.6967 6.4467C5.29018 7.85322 4.5 9.76088 4.5 11.75V13.25M16.5 13.25V11.75C16.5 10.5565 16.0259 9.41193 15.182 8.56802C14.3381 7.72411 13.1935 7.25 12 7.25C10.8065 7.25 9.66193 7.72411 8.81802 8.56802C7.97411 9.41193 7.5 10.5565 7.5 11.75V13.25"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default EnthuasticIcon;
