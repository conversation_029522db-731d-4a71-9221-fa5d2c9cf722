const MicrophoneIcon = (props) => (
    <svg
      width={12}
      height={14}
      viewBox="0 0 12 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.0425 1.54415C5.29644 1.2902 5.64086 1.14754 5.99999 1.14754C6.35912 1.14754 6.70354 1.2902 6.95748 1.54415C7.21142 1.79809 7.35409 2.14251 7.35409 2.50165V7.00004C7.35409 7.35917 7.21142 7.7036 6.95748 7.95754C6.70354 8.21148 6.35912 8.35415 5.99999 8.35415C5.64086 8.35415 5.29644 8.21148 5.0425 7.95754C4.78856 7.7036 4.64589 7.35917 4.64589 7.00004V2.50165C4.64589 2.14251 4.78856 1.79809 5.0425 1.54415ZM5.99999 0C5.33651 0 4.70021 0.263567 4.23107 0.732718C3.76192 1.20187 3.49836 1.83817 3.49836 2.50165V7.00004C3.49836 7.66351 3.76192 8.29982 4.23107 8.76897C4.70021 9.23812 5.33652 9.50169 5.99999 9.50169C6.66347 9.50169 7.29977 9.23812 7.76892 8.76897C8.23806 8.29982 8.50163 7.66351 8.50163 7.00004V2.50165C8.50163 1.83817 8.23806 1.20187 7.76892 0.732718C7.29977 0.263567 6.66347 0 5.99999 0ZM2.07541 5.71471C2.07541 5.39783 1.81852 5.14094 1.50164 5.14094C1.18476 5.14094 0.927872 5.39783 0.927872 5.71471V6.99996C0.927872 8.34518 1.46225 9.6353 2.41346 10.5865C3.22843 11.4015 4.29219 11.9105 5.42623 12.0396V12.8525H3.42951C3.11262 12.8525 2.85574 13.1093 2.85574 13.4262C2.85574 13.7431 3.11262 14 3.42951 14H8.57048C8.88737 14 9.14425 13.7431 9.14425 13.4262C9.14425 13.1093 8.88737 12.8525 8.57048 12.8525H6.57376V12.0396C7.7078 11.9105 8.77155 11.4015 9.58653 10.5865C10.5377 9.6353 11.0721 8.34518 11.0721 6.99996V5.71471C11.0721 5.39783 10.8152 5.14094 10.4983 5.14094C10.1815 5.14094 9.92458 5.39783 9.92458 5.71471V6.99996C9.92458 8.04084 9.5111 9.03908 8.77509 9.77509C8.03909 10.5111 7.04086 10.9246 5.99999 10.9246C4.95913 10.9246 3.9609 10.5111 3.22489 9.77509C2.48889 9.03908 2.07541 8.04084 2.07541 6.99996V5.71471Z"
        fill="currentColor"
      />
    </svg>
  );
  export default MicrophoneIcon;