import { fn } from '@storybook/test';

import DBadge from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Global/DBadge',
  component: DBadge,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    hiddenLabel: {
      default: false,
      control: { type: 'boolean' }
    },
    type: {
      options: ['active', 'paused'],
      control: { type: 'radio' }
    }
  },
  args: {
    hiddenLabel: false
  }
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Active = {
  args: {
    type: 'active',
    label: 'Active'
  }
};

export const Paused = {
  args: {
    type: 'paused',
    label: 'Paused'
  }
};
export const ActiveHiddenLabel = {
  args: {
    type: 'active',
    label: 'Active',
    hiddenLabel: true
  }
};

export const PausedHiddenLabel = {
  args: {
    type: 'paused',
    label: 'Paused',
    hiddenLabel: true
  }
};
