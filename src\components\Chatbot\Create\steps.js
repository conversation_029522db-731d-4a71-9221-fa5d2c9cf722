import { StepEnum } from './StepEnum';

export const createSteps = [
  {
    id: StepEnum.CHATBOT_NAME,
    label: 'AI Chatbot name',
    active: true,
    completed: false,
    skippable: false,
    pending: false,
    progress_label: '1. Set name'
  },
  {
    id: StepEnum.KNOWLEDGE,
    label: 'Knowledge',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: '2. Set memory'
  },
  {
    id: StepEnum.PERSONALITY_CREATE,
    label: 'Personality',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: '3. Set personality'
  },
  {
    id: StepEnum.REVIEW_CREATE,
    label: 'Review',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: '4. Submit'
  }
];

export const customizeSteps = [
  // {
  //   id: StepEnum.PERSONALITY_CUSTOMIZE,
  //   label: 'Personality',
  //   active: true,
  //   completed: false,
  //   skippable: false,
  //   pending: false
  // },
  {
    id: StepEnum.SETUP,
    label: 'Set-up',
    active: true,
    completed: false,
    skippable: false,
    pending: false
  },
  {
    id: StepEnum.STYLING,
    label: 'Styling',
    active: false,
    completed: false,
    skippable: false,
    pending: true
  },
  {
    id: StepEnum.POWER_UPS,
    label: 'Power-ups',
    active: false,
    completed: false,
    skippable: false,
    pending: true
  },
  {
    id: StepEnum.TABS,
    label: 'Tabs',
    active: false,
    completed: false,
    skippable: false,
    pending: true
  },
  {
    id: StepEnum.REVIEW_CUSTOMIZE,
    label: 'Review',
    active: false,
    completed: false,
    skippable: false,
    pending: true
  },
  {
    id: StepEnum.REALTIME_VOICE,
    label: 'Realtime Voice',
    active: false,
    completed: false,
  }
];

export const voiceSteps = [
  {
    id: StepEnum.VOICE_NAME,
    label: 'Voice Setup',
    active: true,
    completed: false,
    skippable: false,
    pending: false,
    progress_label: 'Set name'
  },
  {
    id: StepEnum.CHATBOT_SELECTION,
    label: 'AI Chatbot Selection',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: 'Set Memory'
  },
  {
    id: StepEnum.PHONE_NUMBERS,
    label: 'Phone Numbers',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: 'Set numbers'
  },
  {
    id: StepEnum.VOICE_MODE,
    label: 'Mode',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: 'Set mode'
  },
  {
    id: StepEnum.VOICE_TO_TEXT,
    label: 'Voice to Text',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: 'Set voice to text'
  },
  {
    id: StepEnum.LLM,
    label: 'LLM',
    active: false,
    completed: false,
    skippable: false,
    pending: true
  },
  {
    id: StepEnum.TEXT_TO_VOICE,
    label: 'Text to Voice',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: 'Set text to voice'
  },
  {
    id: StepEnum.AI_VOICE,
    label: 'AI Voice',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: 'Set AI voice'
  },
  {
    id: StepEnum.GREETING_MESSAGE,
    label: 'Greeting Message',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: 'Set message'
  },
  {
    id: StepEnum.PERSONALITY_PROMPT,
    label: 'Personality Prompt',
    active: false,
    completed: false,
    skippable: false,
    pending: true,
    progress_label: 'Set personality'
  },
]