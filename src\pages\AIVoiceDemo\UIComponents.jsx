import React, { useEffect } from 'react';
import { getCircleSize, getCircleColor, getGlowEffect, getOuterRings, pulseKeyframes } from './VisualizationUtils';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';

// Audio visualization component with enhanced animations
export const AudioVisualizer = ({ speaking, userAudioLevel, smoothedBotLevel, isCalling = false }) => {
  // Strict rules for showing calling animation - never show when bot is speaking or we have bot audio
  const showCallingAnimation = isCalling && (!speaking || speaking === 'user') && smoothedBotLevel < 0.05;

  const rings = getOuterRings(speaking, userAudioLevel, smoothedBotLevel, getCircleSize, showCallingAnimation);

  // Insert keyframes for the pulsing animation
  useEffect(() => {
    // Check if the style element already exists
    const existingStyle = document.getElementById('pulse-keyframes');
    if (!existingStyle) {
      const styleElement = document.createElement('style');
      styleElement.id = 'pulse-keyframes';
      styleElement.innerHTML = pulseKeyframes;
      document.head.appendChild(styleElement);

      // Clean up on unmount
      return () => {
        document.head.removeChild(styleElement);
      };
    }
  }, []);

  // Phone icon for calling state
  const PhoneIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-white animate-pulse" viewBox="0 0 20 20" fill="currentColor">
      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
    </svg>
  );

  // Determine animation based on speaking state
  const getAnimation = () => {
    if (speaking === 'bot') return 'glow 2s infinite ease-in-out';
    if (speaking === 'user') return 'wave 3s infinite ease-in-out';
    return '';
  };

  return (
    <div className="flex-grow flex items-center justify-center w-full py-4">
      <div className="relative flex items-center justify-center">
        {rings.map(ring => (
          <div
            key={ring.key}
            className="absolute rounded-full"
            style={ring.style}
          />
        ))}
        <div
          className="rounded-full flex items-center justify-center"
          style={{
            width: `${getCircleSize(speaking, userAudioLevel, smoothedBotLevel, showCallingAnimation)}px`,
            height: `${getCircleSize(speaking, userAudioLevel, smoothedBotLevel, showCallingAnimation)}px`,
            backgroundColor: getCircleColor(speaking, showCallingAnimation),
            transition: 'all 400ms cubic-bezier(0.34, 1.56, 0.64, 1)',
            boxShadow: getGlowEffect(speaking, userAudioLevel, smoothedBotLevel, showCallingAnimation),
            willChange: 'width, height, transform, box-shadow',
            animation: getAnimation()
          }}
        >
          {showCallingAnimation && <PhoneIcon />}
        </div>
      </div>
    </div>
  );
};

// Control buttons component
export const ControlButtons = ({
  isStartButtonDisabled,
  isStopButtonDisabled,
  startAudio,
  stopAudio,
  isMuted,
  toggleMute
}) => (
  <div className="flex space-x-4 mb-16">
    <button
      className={`py-3 px-6 rounded-full font-medium transition-all duration-200 shadow-md
        ${isStartButtonDisabled
          ? (isStopButtonDisabled
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed' // Both disabled
              : 'bg-red-500 hover:bg-red-600 text-white hover:shadow-lg') // Stop mode
          : 'bg-gray-800 hover:bg-gray-900 text-white hover:shadow-lg'}`} // Start mode
      disabled={isStartButtonDisabled && isStopButtonDisabled}
      onClick={isStartButtonDisabled ? () => stopAudio(true) : startAudio}
    >
      {isStartButtonDisabled && !isStopButtonDisabled ? 'Stop' : 'Call'}
    </button>
    <button
      className={`py-3 px-6 rounded-full font-medium transition-all duration-200 shadow-md
        ${isStopButtonDisabled
          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
          : isMuted
            ? 'bg-yellow-500 hover:bg-yellow-600 text-white hover:shadow-lg'
            : 'bg-gray-500 hover:bg-gray-600 text-white hover:shadow-lg'}`}
      disabled={isStopButtonDisabled}
      onClick={toggleMute}
      aria-label={isMuted ? 'Unmute Microphone' : 'Mute Microphone'}
      title={isMuted ? 'Unmute Microphone' : 'Mute Microphone'}
    >
      {isMuted ? (
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5.586 7.586a2 2 0 102.828 2.828L5.586 7.586zm5.656 5.656a2 2 0 01-2.828-2.828l2.828 2.828z" clipRule="evenodd" />
            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 011-1v2a1 1 0 01-1-1zm7 1a1 1 0 01-1 1v2a1 1 0 011-1v-2zm-3-8a7 7 0 00-5.468 11.37l9.838-9.838A7 7 0 0011 2z" />
          </svg>
          Unmute
        </div>
      ) : (
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
          </svg>
          Mute
        </div>
      )}
    </button>
  </div>
);

// Header component
export const Header = () => {
  return (
    <div className="w-full mb-8 flex justify-center">
      <div className="w-full max-w-lg flex justify-center">
        <DFullLogo mode="light" size="lg" />
      </div>
    </div>
  );
};
