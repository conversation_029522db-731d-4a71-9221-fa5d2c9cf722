import React, { useEffect, useState } from 'react';
import EmbedRouter from '@/router/embed';
import { useTimezone } from '@/hooks/useTimezone';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';

const hardcodedPassword = 'Dante@111';
const passwordKey = 'lastAuthTime';
const expires = 2 * 60 * 60 * 1000; // 2 hours

const App = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [checkedAuth, setCheckedAuth] = useState(false);
  const [inputPassword, setInputPassword] = useState('');
  const { timezone, offset } = useTimezone();
  const setTimezoneInfo = useTimezoneStore((state) => state.setTimezoneInfo);

  const checkAuthentication = () => {
    const lastAuthTime = localStorage.getItem(passwordKey);
    if (!lastAuthTime) return false;
    const currentTime = new Date().getTime();
    return currentTime - parseInt(lastAuthTime, 10) < expires;
  };

  useEffect(() => {
    setTimezoneInfo({ timezone, offset });
  }, [timezone, offset, setTimezoneInfo]);


  useEffect(() => {
    if (
      window.location.href.includes('https://app.dante-ai.com/') ||
      window.location.href.includes('//localhost')
    ) {
      // On the official domain: skip password
      setIsAuthenticated(true);
      setCheckedAuth(true);
    } else {
      // Anywhere else: do the password check
      const alreadyAuthenticated = checkAuthentication();
      setIsAuthenticated(alreadyAuthenticated);
      setCheckedAuth(true);
    }
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (inputPassword === hardcodedPassword) {
      localStorage.setItem(passwordKey, new Date().getTime().toString());
      setIsAuthenticated(true);
    } else {
      alert('Incorrect password. You are not authorized to use this app.');
      window.location.href = 'https://www.google.com/';
    }
  };

  if (!checkedAuth) {
    return null; // or a loading spinner if desired
  }

  if (!isAuthenticated) {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          fontFamily: 'Arial, sans-serif',
        }}
      >
        <h2>Please Enter the Password</h2>
        <form
          onSubmit={handleSubmit}
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
            width: '200px',
          }}
        >
          <input
            type="password"
            value={inputPassword}
            onChange={(e) => setInputPassword(e.target.value)}
            placeholder="Password"
            style={{ padding: '8px', fontSize: '16px' }}
          />
          <button
            type="submit"
            style={{ padding: '8px', fontSize: '14px', cursor: 'pointer' }}
          >
            Submit
          </button>
        </form>
      </div>
    );
  }

  return <EmbedRouter />;
};

export default App;
