import React, { useEffect, useState } from 'react';

import { DANTE_THEME_CHAT } from '@/constants';
import useDante<PERSON><PERSON> from '@/hooks/useDanteApi';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import * as chatbotService from '@/services/chatbot.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import { useUserStore } from '@/stores/user/userStore';

import Conversations from '../../Conversations';
import ChatManager from '../ChatManager';
import clsx from 'clsx';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import { Transition } from '@headlessui/react';

export const DanteFAQ = ({
  showChat,
  setShowChat,
  firstMessage,
  setFirstMessage,
}) => {
  //store
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );

  const { auth } = useUserStore((state) => state);

  const { data, isLoading } = useDanteApi(
    chatbotService.getChatbotById,
    [],
    {},
    import.meta.env.VITE_APP_DANTE_FAQ_KB_ID
  );

  //states
  const [showConversations, setShowConversations] = useState(false);

  const resetCurrentConversation = useConversationStore(
    (state) => state.resetCurrentConversation
  );

  const handleSubmit = () => {
    setShowChat(true);
    setLayoutTitle('');
    setShowConversations(false);
  };

  useEffect(() => {
    setSelectedChatbot(data?.results);
  }, [data, setSelectedChatbot]);

  useEffect(() => {
    if (!showChat) {
      resetCurrentConversation();
    }
  }, [showChat]);

  return (
    <Transition show={true}>
      <div
        className={`transition-all duration-500 data-[closed]:opacity-0 data-[enter]:delay-200 data-[leave]:duration-1000 flex flex-col justify-between ${
          showChat ? 'h-full max-h-full' : 'h-auto mt-size4'
        }`}
      >
        <div
          className={['transition-all duration-500 h-full', 'grow-0'].join(' ')}
        >
          <LayoutRightSidebar
            showSidebar={false}
            RightSidebar={(handleRightSidebar) =>
              showConversations && (
                <Conversations
                  onClose={() => {
                    setShowConversations(false);
                    handleRightSidebar();
                  }}
                />
              )
            }
          >
            {(handleRightSidebar) => (
              <div
                className={clsx(
                  'transition-all duration-200 data-[closed]:opacity-0 data-[enter]:delay-200 flex flex-col gap-size6 md:min-h-0 md:h-full max-w-screen-md mx-auto w-full',
                  showChat ? 'bg-white rounded-size1 border border-grey-10 h-full' : ''
                )}
              >
                <ChatManager
                  isDanteFaq
                  config={{
                    ...DANTE_THEME_CHAT,
                    kb_id: import.meta.env.VITE_APP_DANTE_FAQ_KB_ID,
                    access_token: auth.access_token,
                    name: 'Dante FAQ',
                  }}
                  isInApp={true}
                  hiddenConversation={!showChat}
                  showMenuBtn={false}
                  showCloseBtn={showChat}
                  handleCloseButton={() => setShowChat(false)}
                  handleOpenDanteConversations={() => {
                    setShowConversations(true);
                    handleRightSidebar();
                  }}
                  handleOpenDanteFaq={() => {
                    handleSubmit();
                  }}
                />
              </div>
            )}
          </LayoutRightSidebar>
        </div>
      </div>
    </Transition>
  );
};

export default DanteFAQ;
