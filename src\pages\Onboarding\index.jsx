import { useState } from 'react';
import LayoutPublic from '@/layouts/LayoutPublic';
import { SignUpStepsEnum } from '@/components/Onboarding/signUpSteps';
import DTransition from '@/components/Global/DTransition';
import RegisterProfile from '@/components/Onboarding/RegisterProfile';
import RegisterTeam from '@/components/Onboarding/RegisterTeam';
import RegisterCompany from '@/components/Onboarding/RegisterCompay';

const Onboarding = () => {
  const [currentStep, setCurrentStep] = useState(SignUpStepsEnum.PROFILE);
  const [userData, setUserData] = useState({});
  const [teamData, setTeamData] = useState({
    teammates: [
      {
        email: '',
        role_id: import.meta.env.VITE_APP_DANTE_SIGNUP_TEAM_ROLE_ID,
        max_credits_available: import.meta.env
          .VITE_APP_DANTE_SIGNUP_TEAM_MAX_CREDITS,
      },
    ],
  });
  const [companyData, setCompanyData] = useState({});
  const className = 'relative overflow-hidden';

  const handleChange = (e, name) => {
    if (name === 'userData') {
      setUserData({ ...userData, ...e });
    }
  };

  return (
    <LayoutPublic
      leftSide={
        <>
          <DTransition
            unmount={true}
            type="slide"
            show={currentStep === SignUpStepsEnum.PROFILE}
            className="h-full"
          >
            <div className={`${className}`}>
              <RegisterProfile
                setCurrentStep={setCurrentStep}
                handleChange={handleChange}
                userData={userData}
                setUserData={setUserData}
              />
            </div>
          </DTransition>
          <DTransition
            unmount={true}
            type="slide"
            show={currentStep === SignUpStepsEnum.TEAM}
            className="h-full"
          >
            <div className={`${className}`}>
              <RegisterTeam
                setCurrentStep={setCurrentStep}
                handleChange={handleChange}
                userData={userData}
                setTeamData={setTeamData}
                teamData={teamData}
              />
            </div>
          </DTransition>
          <DTransition
            unmount={true}
            type="slide"
            show={currentStep === SignUpStepsEnum.BRANDING}
            className="h-full"
          >
            <div className={`${className}`}>
              <RegisterCompany
                setCurrentStep={setCurrentStep}
                handleChange={handleChange}
                userData={userData}
                setCompanyData={setCompanyData}
                companyData={companyData}
              />
            </div>
          </DTransition>
        </>
      }
    />
  );
};

export default Onboarding;
