import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '@/stores/user/userStore';
import useLayoutStore from '@/stores/layout/layoutStore';

import DButton from '../Global/DButton';
import LayoutOnBoarding from './LayoutOnBoarding';
import { SignUpStepsEnum } from './signUpSteps';
import DInputBlock from '../Global/DInput/DInputBlock';
import DInput from '../Global/DInput/DInput';
import DColorInput from '../Global/DColorInput';
import DUploadImage from '../Global/DUploadImage';
import LinkIcon from '../Global/Icons/LinkIcon';

import { DANTE_THEME_CHAT } from '@/constants';
import isValidURL from '@/helpers/validURL';
import compareObjects from '@/helpers/compareObjects';
import { createCompany } from '@/services/company.service';
import * as userService from '@/services/user.service';
import DAlert from '../Global/DAlert';

export default function RegisterCompany({ setCurrentStep }) {
  const navigate = useNavigate();
  const user = useUserStore((state) => state.user);
  const { setWelcomeModal } = useLayoutStore();

  const defaultCompanyData = {
    brand_color: DANTE_THEME_CHAT.brand_color,
    element_color: DANTE_THEME_CHAT.element_color,
    surface_color: DANTE_THEME_CHAT.surface_color,
    website: '',
    logo: '',
    icon: '',
  };

  const [errors, setErrors] = useState({});
  const [status, setStatus] = useState(false);
  const [companyData, setCompanyData] = useState({
    ...defaultCompanyData,
    website: '',
  });
  const [loading, setLoading] = useState(false);
  const [logoPreview, setLogoPreview] = useState(null);
  const [iconPreview, setIconPreview] = useState(null);
  const [iconFile, setIconFile] = useState(null);
  const [logoFile, setLogoFile] = useState(null);
  const [imageError, setImageError] = useState({
    logo: '',
    icon: '',
  });

  const formData = new FormData();

  const handleImageChange = (e, field) => {
    const file = e.target.files[0];
    if (file) {
      setCompanyData((prev) => ({ ...prev, [field]: file }));
      const previewURL = URL.createObjectURL(file);
      if (field === 'logo') {
        setLogoPreview(previewURL);
        setLogoFile(file);
      }
      if (field === 'icon') {
        setIconPreview(previewURL);
        setIconFile(file);
      }
    }
  };

  const updateField = (field, value) => {
    setCompanyData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSetImageError = (field, error) => {
    setImageError((prev) => ({ ...prev, [field]: error }));
  };

  const validateData = () => {
    const validationErrors = {};
    if (!isValidURL(companyData.website)) {
      validationErrors.website = 'Invalid URL';
    }
    // if (!companyData.logo) validationErrors.logo = 'Logo is required';
    // if (!companyData.icon) validationErrors.icon = 'Icon is required';
    if (imageError.logo) validationErrors.logo = imageError.logo;
    if (imageError.icon) validationErrors.icon = imageError.icon;

    setErrors(validationErrors);
    return Object.keys(validationErrors).length === 0;
  };

  const changeOnboarding = async () => {
    try {
      await userService.updateOnboardingStatus({
        onboarding_completed: true,
      });
    } catch (error) {
      console.error('Error changing onboarding:', error);
    }
  };

  const handleSubmit = async () => {
    if (!status) {
      changeOnboarding();
      setWelcomeModal({ isOpen: true, show: true });
      navigate('/');
      return;
    }
    if (!validateData()) return;

    if (companyData.logo instanceof File) {
      formData.append('file', companyData.logo);
      const response = await userService.uploadFile(formData);
      if (response.status === 200) {
        companyData.logo = response?.data?.url;
      }
      formData.delete('file');
    }
    if (companyData.icon instanceof File) {
      formData.append('file', companyData.icon);
      const response = await userService.uploadFile(formData);
      if (response.status === 200) {
        companyData.icon = response?.data?.url;
      }
      formData.delete('file');
    }

    try {
      setLoading(true);
      const response = await createCompany({
        ...companyData,
      });

      if (response.status === 200) {
        changeOnboarding();
        setWelcomeModal({ isOpen: true, show: true });
        navigate('/');
      }
    } catch (error) {
      console.error('Error creating company:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setStatus(!compareObjects(defaultCompanyData, companyData));
  }, [companyData]);

  return (
    <LayoutOnBoarding
      title="Yours from the start"
      alert={`You are registering with ${user?.email}`}
    >
      <DInputBlock label="Company website" name="website" required={status}>
        <DInput
          iconPlacement="pre"
          icon={<LinkIcon />}
          name="website"
          type="text"
          placeholder="Enter your company website"
          value={companyData.website}
          onFocus={(e) => {
            if (e.target.value === '') {
              updateField('website', 'https://');
            }
          }}
          onChange={(e) => {
            updateField('website', e.target.value);

            if (isValidURL(e.target.value) || !e.target.value) {
              setErrors((prev) => ({ ...prev, website: '' }));
            } else {
              setErrors((prev) => ({ ...prev, website: 'Invalid URL' }));
            }
          }}
          error={errors.website}
        />
      </DInputBlock>

      <div className="flex flex-col sm:flex-row gap-size3">
        {['Brand_color', 'Element_color', 'Surface_color'].map((field) => (
          <DInputBlock
            key={field.toLocaleLowerCase()}
            label={field.replace('_', ' ')}
            name={field.toLocaleLowerCase()}
          >
            <DColorInput
              value={companyData[field.toLocaleLowerCase()]}
              onChange={(color) =>
                updateField(field.toLocaleLowerCase(), color.hex)
              }
            />
          </DInputBlock>
        ))}
      </div>

      <div className="flex flex-col gap-size2">
        <div className="flex flex-col sm:flex-row gap-size3">
          <DUploadImage
            name="logo"
            label="Company Logo"
            imageUrl={logoPreview}
            imageFile={logoFile}
            handleImageChange={(e) => handleImageChange(e, 'logo')}
            maxSize={200 * 1024}
            ratioWith={10}
            ratioHeight={3}
            maxWidth={200}
            maxHeight={66}
            error={errors.logo}
            setImageError={handleSetImageError}
          />

          <DUploadImage
            name="icon"
            label="AI Chatbot Icon"
            imageUrl={iconPreview}
            imageFile={iconFile}
            handleImageChange={(e) => handleImageChange(e, 'icon')}
            maxSize={150 * 1024}
            ratioWith={1}
            ratioHeight={1}
            maxWidth={200}
            maxHeight={200}
            error={errors.icon}
            setImageError={handleSetImageError}
          />
        </div>

        <DAlert className="!pl-0 -ml-[1px]">
          If no custom header image or logo is uploaded during signup,
          Dante-AI&apos;s default images will be applied automatically. These
          can be personalized anytime via the Branding Menu.
        </DAlert>
      </div>

      <div className="flex gap-size3 w-full mt-size8">
        <DButton
          variant="grey"
          size="lg"
          className="w-max whitespace-nowrap"
          onClick={() => setCurrentStep(SignUpStepsEnum.TEAM)}
        >
          Go Back
        </DButton>
        <DButton
          type="submit"
          variant="dark"
          size="lg"
          fullWidth
          onClick={handleSubmit}
          loading={loading}
        >
          {status ? 'Complete' : 'Complete and style later'}
        </DButton>
      </div>
    </LayoutOnBoarding>
  );
}
