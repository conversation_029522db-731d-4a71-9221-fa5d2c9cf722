const MessengerColorIcon = () => {
  return (
    <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2408_109422)">
        <path
          d="M16.333 0C7.32101 0 0.333008 6.604 0.333008 15.52C0.333008 20.184 2.24501 24.216 5.35701 27C5.61701 27.232 5.77701 27.56 5.78501 27.912L5.87301 30.76C5.87942 30.9696 5.9372 31.1743 6.04127 31.3563C6.14535 31.5383 6.29254 31.6919 6.4699 31.8037C6.64726 31.9155 6.84936 31.982 7.05844 31.9974C7.26753 32.0128 7.47719 31.9766 7.66901 31.892L10.845 30.492C11.113 30.372 11.417 30.352 11.701 30.428C13.161 30.828 14.713 31.044 16.333 31.044C25.345 31.044 32.333 24.44 32.333 15.524C32.333 6.608 25.345 0 16.333 0Z"
          fill="url(#paint0_radial_2408_109422)"
        />
        <path
          d="M6.72551 20.0598L11.4255 12.6038C11.6023 12.3231 11.8354 12.0821 12.1101 11.896C12.3847 11.7099 12.6949 11.5828 13.0212 11.5226C13.3475 11.4625 13.6826 11.4706 14.0056 11.5466C14.3285 11.6225 14.6322 11.7646 14.8975 11.9638L18.6375 14.7678C18.8045 14.8928 19.0076 14.96 19.2162 14.9593C19.4248 14.9586 19.6274 14.89 19.7935 14.7638L24.8415 10.9318C25.5135 10.4198 26.3935 11.2278 25.9455 11.9438L21.2415 19.3958C21.0647 19.6765 20.8316 19.9175 20.557 20.1036C20.2823 20.2897 19.9721 20.4168 19.6458 20.477C19.3196 20.5371 18.9844 20.529 18.6614 20.453C18.3385 20.3771 18.0348 20.235 17.7695 20.0358L14.0295 17.2318C13.8625 17.1068 13.6594 17.0396 13.4508 17.0403C13.2423 17.041 13.0396 17.1096 12.8735 17.2358L7.82551 21.0678C7.15351 21.5798 6.27351 20.7758 6.72551 20.0598Z"
          fill="white"
        />
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_2408_109422"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(5.69301 32) scale(35.2)"
        >
          <stop stopColor="#0099FF" />
          <stop offset="0.6" stopColor="#A033FF" />
          <stop offset="0.9" stopColor="#FF5280" />
          <stop offset="1" stopColor="#FF7061" />
        </radialGradient>
        <clipPath id="clip0_2408_109422">
          <rect width="32" height="32" fill="white" transform="translate(0.333008)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default MessengerColorIcon;
