const SubscriptionIcon = (props) => {
    return <svg width="28" height="22" viewBox="0 0 28 22" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path fillRule="evenodd" clipRule="evenodd" d="M10.8004 2.19998C10.3586 2.19998 10.0004 2.55815 10.0004 2.99998V11.1052L12.5158 9.42824C13.3779 8.85348 14.4571 8.70895 15.4401 9.03661C16.4428 9.37083 17.2295 10.1576 17.5638 11.1602L17.5969 11.2596C17.8294 11.9572 17.8391 12.7044 17.6333 13.4H25.2004C25.6422 13.4 26.0004 13.0418 26.0004 12.6V6.19998H12.4004C11.9586 6.19998 11.6004 5.8418 11.6004 5.39998C11.6004 4.95815 11.9586 4.59998 12.4004 4.59998H26.0004V2.99998C26.0004 2.55815 25.6422 2.19998 25.2004 2.19998H10.8004ZM15.7634 13.685L15.9722 13.2674C16.2056 12.8008 16.244 12.2605 16.079 11.7656L16.0459 11.6662C15.8709 11.1413 15.459 10.7295 14.9342 10.5545C14.4196 10.383 13.8546 10.4586 13.4033 10.7595L7.24415 14.8656C6.87653 15.1107 6.37983 15.0114 6.13475 14.6437C5.88967 14.2761 5.98901 13.7794 6.35663 13.5343L8.40039 12.1718V4.0246L7.0543 4.36112C6.63231 4.46662 6.24691 4.68482 5.93933 4.9924L1.76608 9.16566C1.45366 9.47808 0.947125 9.47808 0.634705 9.16566C0.322286 8.85324 0.322286 8.34671 0.634705 8.03429L4.80796 3.86103C5.3206 3.3484 5.96292 2.98472 6.66625 2.80889L8.48851 2.35332C8.77081 1.34194 9.69896 0.599976 10.8004 0.599976H25.2004C26.5259 0.599976 27.6004 1.67449 27.6004 2.99998V12.6C27.6004 13.9255 26.5259 15 25.2004 15H16.681L8.68102 21.4H1.20039C0.758563 21.4 0.400391 21.0418 0.400391 20.6C0.400391 20.1581 0.758563 19.8 1.20039 19.8H8.11977L15.7634 13.685Z" fill="currentColor" />
    </svg>
}

export default SubscriptionIcon;