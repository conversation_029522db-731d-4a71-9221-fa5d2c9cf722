import { useEffect, useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import DButton from '@/components/Global/DButton';
import DTable from '@/components/Global/DTable';
import DSelect from '@/components/Global/DSelect';
import AddIcon from '@/components/Global/Icons/AddIcon';
import ShoppingCartIcon from '@/components/Global/Icons/ShoppingCartIcon';
import AddPhoneNumberForm from '@/components/Voice/AddPhoneNumberForm';
import LayoutMain from '@/layouts/LayoutMain';
import DBadge from '@/components/Global/DBadge';
import { STATUS } from '@/constants';
import DModalImage from '@/components/Global/DModalImage';
import ModalImage from '@/assets/voice/modal_image.png'
import useDanteApi from '@/hooks/useDanteApi';
import { deletePhoneNumber, getPhoneNumbers, assignVoiceToPhoneNumber } from '@/services/phoneNumber.service';
import { getVoices } from '@/services/voice.service';
import DLoading from '@/components/DLoading';
import DButtonIcon from '@/components/Global/DButtonIcon';
import ResetIcon from '@/components/Global/Icons/ResetIcon';
import useToast from '@/hooks/useToast';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import VerifyPhoneNumber from '@/components/Voice/VerifyPhoneNumber';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import PhoneNumberAssignedModal from '@/components/Voice/PhoneNumberAssignedModal';
import featureCheck from '@/helpers/tier/featureCheck';

const PhoneNumbers = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { data: phoneNumbers, isLoading: isLoadingPhoneNumbers, error, refetch } = useDanteApi(getPhoneNumbers);
    const { data: voices, isLoading: isLoadingVoices } = useDanteApi(getVoices);

    // Get the just_purchased query parameter
    const searchParams = new URLSearchParams(location.search);
    const justPurchasedNumber = searchParams.get('just_purchased');

    // Ref to track if we've already processed the newly purchased number to avoid multiple API calls
    const processedPurchasedNumber = useRef(false);
    // Ref to track if we need to refetch after voice assignment
    const shouldRefetch = useRef(false);

    const [addPhoneNumberFormOpen, setAddPhoneNumberFormOpen] = useState(false);
    const [modalImageOpen, setModalImageOpen] = useState(false);

    const [phoneNumbersList, setPhoneNumbersList] = useState([]);
    const [verifyPhoneNumberOpen, setVerifyPhoneNumberOpen] = useState(false);
    const [verifyPhoneNumber, setVerifyPhoneNumber] = useState(null);

    const [openDeleteModal, setOpenDeleteModal] = useState(false);
    const [deletePhoneNumberId, setDeletePhoneNumberId] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false);

    // For the phone number assigned modal
    const [showAssignedModal, setShowAssignedModal] = useState(false);
    const [assignedPhoneNumber, setAssignedPhoneNumber] = useState('');
    const [assignedVoiceId, setAssignedVoiceId] = useState(null);

    // For tracking if we're updating a voice assignment
    const [isUpdatingVoice, setIsUpdatingVoice] = useState(false);

    const { addSuccessToast, addErrorToast } = useToast();

    const columns = [
        {
            label: 'Phone number',
            key: 'phone_number',
            showName: true,
            minWidth: '200px'
        },
        {
            label: 'AI Voice Agent',
            key: 'ai_voice',
            showName: true,
            minWidth: '200px'
        },
        {
            label: 'Status',
            key: 'status',
            showName: true,
            minWidth: '200px'
        },
        {
            label: 'Actions',
            key: 'actions',
            showName: true,
            minWidth: '200px'
        }
    ]

    const handleDeletePhoneNumber = async () => {
        setIsDeleting(true);
        try{
            const response = await deletePhoneNumber(deletePhoneNumberId);
            if(response.status === 200){
                addSuccessToast({ message: 'Phone number deleted' });
                setOpenDeleteModal(false);
                refetch();
            }
        } catch (error) {
            addErrorToast({ message: error.response.data.message });
        } finally {
            setIsDeleting(false);
        }
    }

    // Function to handle voice agent assignment - modified to avoid circular dependencies
    const handleAssignVoice = async (phoneNumberId, voiceId, phoneNumberStr, skipRefetch = false) => {
        setIsUpdatingVoice(true);
        try {
            const response = await assignVoiceToPhoneNumber(phoneNumberId, voiceId);
            if (response.status === 200) {
                addSuccessToast({ message: 'Voice agent assigned successfully' });
                
                // If this is the first voice agent and it was just purchased, show the assigned modal
                if (justPurchasedNumber && phoneNumberStr === justPurchasedNumber) {
                    setAssignedPhoneNumber(phoneNumberStr);
                    setAssignedVoiceId(voiceId);
                    setShowAssignedModal(true);
                }
                
                // Only refetch if explicitly requested (normal user actions, not auto-assignment)
                if (!skipRefetch) {
                    shouldRefetch.current = true;
                }
            }
        } catch (error) {
            addErrorToast({ message: 'Failed to assign voice agent' });
        } finally {
            setIsUpdatingVoice(false);
            
            // If requested, perform the refetch but outside the state update cycle
            if (shouldRefetch.current) {
                setTimeout(() => {
                    refetch();
                    shouldRefetch.current = false;
                }, 0);
            }
        }
    };

    // Separate effect to handle auto-assigning voice to newly purchased number
    // This will run only once when both phone numbers and voices are loaded
    useEffect(() => {
        if (
            justPurchasedNumber && 
            phoneNumbers?.results && 
            voices?.results?.length > 0 && 
            !processedPurchasedNumber.current &&
            !isUpdatingVoice
        ) {
            const justPurchasedPhoneNumber = phoneNumbers.results.find(
                phone => phone.number === `+${justPurchasedNumber}`
            );

            if (justPurchasedPhoneNumber && !justPurchasedPhoneNumber.ai_voice) {
                processedPurchasedNumber.current = true; // Mark as processed to avoid multiple calls
                
                // Auto-assign the first voice to the just purchased number - skip refetch to avoid extra API calls
                const firstVoice = voices.results[0];
                handleAssignVoice(justPurchasedPhoneNumber.id, firstVoice.id, justPurchasedPhoneNumber.number, true);
                
                // Remove the query parameter from the URL without triggering a navigation
                const newUrl = window.location.pathname;
                window.history.replaceState({}, '', newUrl);
            }
        }
    }, [phoneNumbers, voices, justPurchasedNumber, isUpdatingVoice]);

    // Separate effect to format phone numbers for display
    // This will run when phone numbers, voices, or updating voice state changes
    useEffect(() => {
        if (phoneNumbers?.results && voices?.results) {
            const list = phoneNumbers.results.map((phoneNumber) => {
                // Check if this is the recently purchased number from the query parameter
                const isNew = justPurchasedNumber && phoneNumber?.number === `+${justPurchasedNumber}`;

                return {
                    id: phoneNumber?.id,
                    phone_number: (
                        <div className="flex items-center gap-size1">
                            <span className="font-mono">{phoneNumber?.number}</span>
                            {isNew && (
                                <DBadge type={STATUS.NEW} label="New" size="sm" showIcon={false}/>
                            )}
                        </div>
                    ),
                    ai_voice: (
                        <div className="">
                            <DSelect
                                options={voices?.results?.map(voice => ({
                                    label: voice.name,
                                    value: voice.id
                                })) || []}
                                value={phoneNumber?.ai_voice?.id}
                                onChange={(value) => handleAssignVoice(phoneNumber.id, value, phoneNumber.number)}
                                placeholder="Select a voice agent"
                                disabled={isUpdatingVoice}
                                selectedChild={phoneNumber?.ai_voice?.name || 'Select a voice agent'}
                            />
                        </div>
                    ),
                    status: (
                        <>
                            <DBadge
                                type={phoneNumber?.status?.toUpperCase()}
                                label={phoneNumber?.status?.charAt(0).toUpperCase() + phoneNumber?.status?.slice(1)}
                            />
                        </>
                    ),
                    actions: (
                        <div className="flex items-center gap-size2 justify-start w-full">
                            {phoneNumber?.status === 'unverified' && (
                                <DButton
                                    size="sm"
                                    variant="outlined"
                                    onClick={() => {
                                        setVerifyPhoneNumber(phoneNumber?.id);
                                        setVerifyPhoneNumberOpen(true);
                                    }}
                                >
                                    Resend code
                                </DButton>
                            )}
                            <DButton
                                size="sm"
                                variant="outlined"
                                onClick={() => {
                                    setOpenDeleteModal(true);
                                    setDeletePhoneNumberId(phoneNumber.id);
                                }}
                            >
                                Delete
                            </DButton>
                            <DButton
                                size="sm"
                                variant="outlined"
                                onClick={() => {
                                    navigate(`/voice/${phoneNumber?.ai_voice?.id}/conversations`)
                                }}
                            >
                                View Live Conversations
                            </DButton>
                        </div>
                    )
                };
            });

            setPhoneNumbersList(list);
        }
    }, [phoneNumbers, voices, isUpdatingVoice, justPurchasedNumber]);

    if (isLoadingPhoneNumbers || isLoadingVoices) {
        return <DLoading show={true} />
    }

    return <LayoutMain title="Phone numbers">
      <div className="3xl:max-w-[1200px] 3xl:mx-auto">
        <div className="flex justify-between gap-size2 mb-5">
          <p className="text-sm text-grey-75 font-light mb-size1">Manage your phone numbers for AI Voice Agents. You can add your own number or purchase a new one.</p>
          <div className="flex flex-col sm:flex-row gap-size2">
              <DButton
                  variant="dark"
                  size="sm"
                  onClick={() => {
                    if (featureCheck('buy_phone_number')) {
                      setAddPhoneNumberFormOpen(true)
                    } 
                  }}
              >
                  <AddIcon />
                  <span className="text-sm font-medium">Add Your Number</span>
              </DButton>
              <DButton
                  variant="outlined"
                  size="sm"
                  onClick={() => navigate('/phone-numbers/purchase')}
              >
                  <ShoppingCartIcon className="w-4 h-4"/>
                  <span className="text-sm font-medium">Purchase New Number</span>
              </DButton>
          </div>
        </div>
        <div className="flex flex-col gap-size3">
            <DTable
                columns={columns}
                data={phoneNumbersList}
            />
        </div>
      </div>
      <AddPhoneNumberForm isOpen={addPhoneNumberFormOpen} onClose={() => setAddPhoneNumberFormOpen(false)} refetchPhoneNumbers={refetch}/>
      <VerifyPhoneNumber
          open={verifyPhoneNumberOpen}
          onClose={() => setVerifyPhoneNumberOpen(false)}
          phoneNumber={verifyPhoneNumber}
          refetchData={refetch}
      />
      <DModalImage
          open={modalImageOpen}
          onClose={() => setModalImageOpen(false)}
          image={ModalImage}
          text="Your new AI Voice Agent is ready!"
          firstBtnText="Create another AI Voice Agent"
          secondBtnText="Manage AI Voice Agents"
      />
      <DConfirmationModal
          open={openDeleteModal}
          onClose={() => setOpenDeleteModal(false)}
          title="Delete phone number"
          description="Are you sure you want to delete this phone number? This action cannot be undone."
          onConfirm={handleDeletePhoneNumber}
          confirmText="Delete"
          cancelText="Cancel"
          variantConfirm="danger"
          loading={isDeleting}
      />

      {/* Phone Number Assigned Modal */}
      <PhoneNumberAssignedModal
          isOpen={showAssignedModal}
          onClose={() => setShowAssignedModal(false)}
          voiceId={assignedVoiceId}
          phoneNumber={assignedPhoneNumber}
      />
    </LayoutMain>
}

export default PhoneNumbers;
