const ClockIcon = (props) => {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.00009 1.08333C3.28469 1.08333 1.08342 3.2846 1.08342 6C1.08342 8.71538 3.28469 10.9167 6.00009 10.9167C8.71547 10.9167 10.9168 8.71537 10.9168 6C10.9168 3.2846 8.71547 1.08333 6.00009 1.08333ZM0.250092 6C0.250092 2.82436 2.82445 0.25 6.00009 0.25C9.1757 0.25 11.7501 2.82436 11.7501 6C11.7501 9.17561 9.1757 11.75 6.00009 11.75C2.82445 11.75 0.250092 9.17561 0.250092 6ZM6.00009 2.68333C4.16835 2.68333 2.68342 4.16826 2.68342 6C2.68342 6.23012 2.49688 6.41667 2.26676 6.41667C2.03664 6.41667 1.85009 6.23012 1.85009 6C1.85009 3.70802 3.70811 1.85 6.00009 1.85C6.23021 1.85 6.41676 2.03655 6.41676 2.26667C6.41676 2.49679 6.23021 2.68333 6.00009 2.68333ZM8.69467 3.30537C8.85739 3.46809 8.85739 3.73191 8.69467 3.89463L6.8986 5.69069C6.93197 5.78765 6.95009 5.89171 6.95009 6C6.95009 6.52468 6.52477 6.95 6.00009 6.95C5.47541 6.95 5.05009 6.52468 5.05009 6C5.05009 5.47532 5.47541 5.05 6.00009 5.05C6.10835 5.05 6.21238 5.06811 6.30932 5.10146L8.10541 3.30537C8.26813 3.14265 8.53195 3.14265 8.69467 3.30537Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default ClockIcon;
