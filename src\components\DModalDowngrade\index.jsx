import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { downgradeStepsEnum } from './stepDowngrade';
import StepLostFeatures from './StepLostFeatures';
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from '@headlessui/react';
import DTransition from '../Global/DTransition';
import StepReasons from './StepReasons';
import StepSaleOffer from './StepSaleOffer';
import StepSubscriptionPause from './StepSubscriptionPause';
import {
  finishCancelDowngrade,
  finishDowngrade,
  getDowngradeFeatures,
} from '@/services/plans.service';

import StepTeam from './StepTeam';
import useToast from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { trackSubscriptionCancellation } from '@/helpers/analytics';

const DModalDowngrade = ({ onClose, isOpen, operation, nextTier }) => {
  const { addSuccessToast } = useToast();
  const [title, setTitle] = useState('');
  const [step, setStep] = useState(downgradeStepsEnum.LOST_FEATURES);
  const [changesBetweenTiers, setChangesBetweenTiers] = useState({
    features: [],
    nr_of_team_members_affected: 0,
    total_members: 0,
  });
  const [isLoading, setIsLoading] = useState(false);

  const { user } = useUserStore();

  const new_tier =
    operation === 'cancel'
      ? { tier_type: 'free' }
      : {
          tier_type: nextTier?.tier_type || nextTier?.name.toLowerCase(),
        };

  const getDowngradeDifferences = async () => {
    try {
      setIsLoading(true);
      const response = await getDowngradeFeatures(new_tier?.tier_type);
      if (response.status === 200) {
        setChangesBetweenTiers(response.data);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to pad numbers with leading zeros
  const padZero = (num) => {
    return num.toString().padStart(2, '0');
  };

  // Helper function to format date with time in UTC
  const formatDateWithTimeUTC = (date) => {
    return `${padZero(date.getUTCMonth() + 1)}/${padZero(date.getUTCDate())}/${date.getUTCFullYear()} ${padZero(date.getUTCHours())}:${padZero(date.getUTCMinutes())}:${padZero(date.getUTCSeconds())}`;
  };

  const trackCancellation = () => {
    if (typeof window !== 'undefined') {
      // Format cancellation date in UTC
      const currentDate = new Date();
      const formattedCancellationDate = formatDateWithTimeUTC(currentDate);

      // Estimate next payment date based on current period
      const nextPaymentDate = new Date();
      if (user.tier_key?.period === 'yearly') {
        nextPaymentDate.setFullYear(nextPaymentDate.getFullYear() + 1);
      } else {
        nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1);
      }
      const formattedNextPaymentDate = formatDateWithTimeUTC(nextPaymentDate);

      // Estimate subscription date (1 period before next payment)
      const subscriptionDate = new Date(nextPaymentDate);
      if (user.tier_key?.period === 'yearly') {
        subscriptionDate.setFullYear(subscriptionDate.getFullYear() - 1);
      } else {
        subscriptionDate.setMonth(subscriptionDate.getMonth() - 1);
      }
      const formattedSubscriptionDate = formatDateWithTimeUTC(subscriptionDate);

      // Track the cancellation event
      trackSubscriptionCancellation({
        subscription_id: user.subscription_id || user.tier_id || '',
        user_id: user.id || '',
        email: user.email || '',
        tier_name: user.tier_type || '',
        billing_cycle: user.tier_key?.period || 'monthly',
        billing_price: user.tier_price_as_float || 0,
        next_payment_date: formattedNextPaymentDate,
        payment_method: 'credit card',
        subscription_date: formattedSubscriptionDate,
        subscription_method: 'direct',
        subscription_cancellation_date: formattedCancellationDate,
      });
    }
  };

  const handleFinishDowngrade = async () => {
    try {
      let response;
      if (operation === 'cancel') {
        response = await finishCancelDowngrade();
      } else {
        response = await finishDowngrade(nextTier?.id);
      }
      if (response.status === 200) {
        if (response.data.checkout_session) {
          window.open(response.data.checkout_session.url, '_blank');
        }
        if (operation === 'cancel') {
          // Track cancellation before showing the success toast
          trackCancellation();

          addSuccessToast({
            message: 'Your subscription has been canceled',
          });
        } else {
          addSuccessToast({
            message: `Your subscription has been downgraded to ${nextTier?.name}. Your subscription will be canceled at the end of the current billing period.`,
          });
        }
        onClose();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleNextStep = (nextStep) => {
    if (nextStep === downgradeStepsEnum.TEAM) {
      if (changesBetweenTiers.nr_of_team_members_affected > 0) {
        setStep(downgradeStepsEnum.TEAM);
      } else {
        handleNextStep('FINAL');
      }
    } else if (nextStep === 'FINAL') {
      handleFinishDowngrade();
    } else {
      setStep(nextStep);
    }
  };

  useEffect(() => {
    if (isOpen) {
      getDowngradeDifferences();
    }
    return () => {
      setStep(downgradeStepsEnum.LOST_FEATURES);
    };
  }, [isOpen]);

  return (
    <Dialog
      open={isOpen}
      onClose={() => onClose()}
      className="relative z-50 transition-all"
    >
      <DialogBackdrop
        transition
        className="fixed inset-0 duration-150 bg-white/90 data-[closed]:opacity-0 data-[leave]:delay-100"
      />

      <div className="fixed inset-0 flex w-screen items-center justify-center transition-all">
        <DialogPanel
          transition
          className="transition-all duration-200 w-11/12 md:w-full md:max-w-[500px] max-h-[80vh] bg-white p-size6 rounded-size1 flex flex-col gap-size5 border border-grey-5 transition data-[enter]:delay-100 duration-150 data-[closed]:-translate-y-20 data-[closed]:opacity-0 scrollbar-hide mt-size5"
        >
          <header
            className={`flex ${
              title ? ' justify-between' : 'justify-end'
            }  items-center`}
          >
            <div className="flex flex-col gap-size1">
              {title && (
                <DialogTitle className="text-2xl text-center">
                  {title}
                </DialogTitle>
              )}
            </div>
          </header>
          {title && <div className="w-full h-px bg-grey-5"></div>}
          <main className="text-lg grow transition-all relative">
            <Transition
              unmount={true}
              show={step === downgradeStepsEnum.REASONS}
              style={{
                paddingTop: '0px',
              }}
            >
              <div className="data-[enter]:delay-200  data-[leave]:absolute inset-0 transition-all duration-500 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-x-1 z-10">
                <StepReasons
                  handleNextStep={handleNextStep}
                  handleClose={() => onClose()}
                  currentStep={step}
                  setStep={setStep}
                  setTitle={setTitle}
                  new_tier={new_tier?.tier_type}
                />
              </div>
            </Transition>
            <Transition
              unmount={true}
              show={step === downgradeStepsEnum.LOST_FEATURES}
              style={{
                paddingTop: '0px',
              }}
              
            >
              <div className="data-[enter]:delay-200  data-[leave]:absolute data-[leave]:duration-200 inset-0 transition-all duration-500 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-x-1 z-11">
                <StepLostFeatures
                  changesBetweenTiers={changesBetweenTiers}
                  handleNextStep={handleNextStep}
                  handleClose={() => onClose()}
                  currentStep={step}
                  setStep={setStep}
                  setTitle={setTitle}
                  new_tier={new_tier?.tier_type}
                  isLoading={isLoading}
                />
              </div>
            </Transition>
            
            <Transition
              unmount={true}
              show={step === downgradeStepsEnum.SALE_OFFER}
            >
              <div className="data-[enter]:delay-200 data-[leave]:absolute inset-0 transition-all duration-500 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-x-1 z-11">
                <StepSaleOffer
                  handleNextStep={handleNextStep}
                  handleClose={() => onClose()}
                  setTitle={setTitle}
                />
              </div>
            </Transition>

            {/* <Transition
              unmount={true}
              show={step === downgradeStepsEnum.SUBSCRIPTION_PAUSE}
            >
              <div className="data-[enter]:delay-200 data-[leave]:absolute inset-0 transition-all duration-500 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-x-1 z-11">
                <StepSubscriptionPause
                  handleNextStep={handleNextStep}
                  handleClose={() => onClose()}
                  setTitle={setTitle}
                />
              </div>
            </Transition> */}

            <Transition
              unmount={true}
              show={step === downgradeStepsEnum.TEAM}
              type="collapse"
            >
              <div className="data-[enter]:delay-200  data-[leave]:absolute inset-0 transition-all duration-500 ease-in-out data-[closed]:opacity-0 data-[closed]:translate-x-1 z-12">
                <StepTeam
                  changesBetweenTiers={changesBetweenTiers}
                  handleNextStep={handleNextStep}
                  handleClose={() => onClose()}
                  currentStep={step}
                  setStep={setStep}
                  setTitle={setTitle}
                  newTier={
                    operation === 'cancel' ? 'cancel' : new_tier?.tier_type
                  }
                  setIsLoading={setIsLoading}
                />
              </div>
            </Transition>
          </main>
        </DialogPanel>
      </div>
    </Dialog>
  );
};

DModalDowngrade.propTypes = {
  onClose: PropTypes.func.isRequired,
  isOpen: PropTypes.bool.isRequired,
  operation: PropTypes.string,
  nextTier: PropTypes.object,
};

export default DModalDowngrade;
