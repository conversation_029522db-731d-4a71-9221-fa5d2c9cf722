import { useEffect, useState } from 'react';

import AvatarBlock from './components/Avatar/AvatarBlock';
import ChatbotBlock from './components/Chatbot/ChatbotBlock';
import CreateSideNav from './components/Chatbot/Create/SideNav/CreateSideNav';
import ChatbotDashboard from './components/Chatbot/Dashboard';
import DBadge from './components/Global/DBadge';
import DButton from './components/Global/DButton';
import DCheckbox from './components/Global/DCheckbox';
import DNavLink from './components/Global/DNavLink';
import DUpload from './components/Global/DUpload';
import AddIcon from './components/Global/Icons/AddIcon';
import CoinsIcon from './components/Global/Icons/CoinsIcon';
import SnippetIcon from './components/Global/Icons/SnippetIcon';
import ZapIcon from './components/Global/Icons/ZapIcon';
import MainNav from './components/MainNav';
import { STATUS } from './constants';

const Atom = () => {
  const [icons, setIcons] = useState([]);
  const [iconsName, setIconsName] = useState([]);

  useEffect(() => {
    const importIcons = async () => {
      const context = import.meta.glob('./components/global/Icons/*.jsx');

      const iconModules = await Promise.all(
        Object.entries(context).map(async ([filePath, moduleImporter]) => {
          const module = await moduleImporter();
          return {
            component: module.default,
            name: filePath.split('/').pop().replace('.jsx', ''),
          };
        })
      );
      setIcons(iconModules);
    };

    importIcons();
  }, []);

  return (
    <div className="p-2">
      <h1 className="text-4xl">Dante Design System</h1>
      <div>
        <h2 className="text-xl">Fonts</h2>
        <div className="text-xs font-regular">
          This is extra small regular text.
        </div>

        <div className="text-sm font-medium">This is small medium text.</div>

        <div className="text-xl font-bold">This is extra large bold text.</div>

        <h2 className="text-xl">Padding</h2>
        <div className="p-size2">This div has 12px padding on all sides.</div>

        <div className="px-size4 py-size2">
          This div has 16px padding left/right and 12px padding top/bottom.
        </div>

        <div className="pt-size5">This div has 24px padding on the top.</div>

        <h2 className="text-xl">Margin</h2>
        <div className="m-size1">This div has 8px margin on all sides.</div>

        <div className="mt-size3 mb-size1">
          This div has 16px margin on the top and 8px on the bottom.
        </div>

        <h2 className="text-xl">Border radius</h2>
        <div className="rounded-size1">
          This div has a border radius of 8px.
        </div>

        <div className="rounded-t-size3">
          This div has a border radius of 16px on the top corners only.
        </div>

        <h2 className="text-xl">Gap</h2>
        <div className="grid gap-size2">
          <div>Item 1</div>
          <div>Item 2</div>
          <div>Item 3</div>
        </div>

        <div className="flex gap-size4">
          <div>Flex Item 1</div>
          <div>Flex Item 2</div>
          <div>Flex Item 3</div>
        </div>

        <h2 className="text-xl">Colors</h2>
        {/* <!-- Purple Colors --> */}
        <div className="bg-purple-300 text-white p-4 mb-4">
          Purple 300 Background
        </div>
        <div className="bg-purple-200 text-white p-4 mb-4">
          Purple 200 Background
        </div>
        <div className="bg-purple-100 text-white p-4 mb-4">
          Purple 100 Background
        </div>

        {/* <!-- Green Colors --> */}
        <div className="bg-green-300 text-white p-4 mb-4">
          Green 300 Background
        </div>
        <div className="bg-green-200 text-green-300 p-4 mb-4">
          Green 200 Background with Green 300 Text
        </div>
        <div className="bg-green-5 text-green-300 p-4 mb-4">
          Green 100 Background
        </div>

        {/* <!-- Orange Colors --> */}
        <div className="bg-orange-300 text-white p-4 mb-4">
          Orange 300 Background
        </div>
        <div className="bg-orange-200 text-orange-300 p-4 mb-4">
          Orange 200 Background with Orange 300 Text
        </div>
        <div className="bg-orange-5 text-orange-300 p-4 mb-4">
          Orange 100 Background
        </div>

        {/* <!-- Grey (Black with Opacity) Colors --> */}
        <div className="bg-grey-50 text-white p-4 mb-4">Grey 50 Background</div>
        <div className="bg-grey-20 text-white p-4 mb-4">Grey 20 Background</div>
        <div className="bg-grey-10 text-black p-4 mb-4">Grey 10 Background</div>
        <div className="bg-grey-5 text-black p-4 mb-4">Grey 5 Background</div>
        <div className="bg-grey-2 text-black p-4 mb-4">Grey 2 Background</div>
      </div>
      <div>
        <h2 className="text-xl">Icons</h2>
        <div className="flex flex-wrap  mx-auto">
          {icons.map(({ component: IconComponent, name }, index) => {
            return (
              <div key={index} className="flex flex-col p-2 items-center w-36">
                <IconComponent />
                <div className="font-mono">{name}</div>
              </div>
            );
          })}
        </div>
      </div>
      <h2 className="text-xl">Buttons</h2>
      <div className="flex flex-col gap-2 p-4">
        <DButton variant="contained" size="md">
          <ZapIcon />
          Button variant contained size MD
        </DButton>
        <DButton variant="contained" size="sm">
          Button variant contained size SM
        </DButton>
        <DButton variant="outlined" size="sm">
          <CoinsIcon />
          Button variant outlined size SM
        </DButton>
        <DButton variant="outlined" size="md">
          Button variant outlined size MD
        </DButton>
        <DButton variant="dark" size="md">
          <SnippetIcon />
          Button variant dark size MD
        </DButton>
        <DButton variant="dark" size="sm">
          Button variant dark size SM
        </DButton>
      </div>
      <h2 className="text-xl">Nav Links</h2>
      <div className="flex flex-col gap-2 bg-grey-5 p-4">
        <DNavLink label="Nav Link" iconPlacement={'pre'} icon={<AddIcon />} />
        <DNavLink
          label="Nav Link"
          iconPlacement={'pre'}
          icon={<AddIcon />}
          disabled={true}
        />
        <DNavLink
          label="Nav Link"
          iconPlacement={'pre'}
          icon={<AddIcon />}
          variant="contained"
        />
        <DNavLink
          label="Nav Link"
          iconPlacement={'pre'}
          icon={<AddIcon />}
          variant="light"
        />
      </div>
      <h2 className="text-xl">Badges</h2>
      <div className="flex flex-col gap-2">
        <DBadge type="active" label="Active" />
        <DBadge type="paused" label="Paused" />
        <DBadge type="paused" hiddenLabel />
        <DBadge type="active" hiddenLabel />
      </div>

      <h2 className="text-xl">Upload</h2>
      <div className="flex flex-col gap-2">
        <DUpload
          accept=""
          id=""
          name=""
          note="Max. size 128mb"
          onChangeFile={(event) => console.log(event.target.files)}
          subtitle=""
          title=""
        />

        <DUpload
          accept=""
          id=""
          name=""
          note=""
          onChangeFile={(event) => console.log(event.target.files)}
          subtitle="Only one URL per line"
          title="Drop your text file here"
        />
      </div>
      <h2 className="text-xl">Checkbox</h2>
      <div className="flex flex-col gap-2">
        <DCheckbox label="Checkbox" />
        <DCheckbox checked label="Checkbox" />
        <DCheckbox disabled label="Checkbox" />
        <DCheckbox checked disabled label="Checkbox" />
      </div>

      <div className="bg-[#F8F8F8] w-full h-screen p-size3 flex gap-[100px]">
        <MainNav />
        <CreateSideNav />
      </div>

      <div className="bg-[#F8F8F8] w-full h-screen p-size3 flex flex-col gap-size3">
        <ChatbotBlock
          imageChatbot="https://chat.dante-ai.com/btn-embed.png"
          infos={{
            active_integrations: {
              status: 'active',
              value: 0,
            },
            avatars: {
              status: 'active',
              value: 0,
            },
            flagged_messages: {
              status: 'active',
              value: 0,
            },
          }}
          name="Long chatbot name example"
          stats={[
            {
              date: new Date('2024-08-08T03:00:00.000Z'),
              value: 3,
            },
            {
              date: new Date('2024-08-09T03:00:00.000Z'),
              value: 1,
            },
            {
              date: new Date('2024-08-10T03:00:00.000Z'),
              value: 7,
            },
            {
              date: new Date('2024-08-11T03:00:00.000Z'),
              value: 2,
            },
            {
              date: new Date('2024-08-12T03:00:00.000Z'),
              value: 3,
            },
            {
              date: new Date('2024-08-13T03:00:00.000Z'),
              value: 1,
            },
            {
              date: new Date('2024-08-14T03:00:00.000Z'),
              value: 2,
            },
            {
              date: new Date('2024-08-15T03:00:00.000Z'),
              value: 5,
            },
            {
              date: new Date('2024-08-16T03:00:00.000Z'),
              value: 1,
            },
            {
              date: new Date('2024-08-17T03:00:00.000Z'),
              value: 7,
            },
            {
              date: new Date('2024-08-18T03:00:00.000Z'),
              value: 9,
            },
            {
              date: new Date('2024-08-19T03:00:00.000Z'),
              value: 2,
            },
            {
              date: new Date('2024-08-20T03:00:00.000Z'),
              value: 1,
            },
            {
              date: new Date('2024-08-21T03:00:00.000Z'),
              value: 10,
            },
            {
              date: new Date('2024-08-22T03:00:00.000Z'),
              value: 3,
            },
            {
              date: new Date('2024-08-23T03:00:00.000Z'),
              value: 6,
            },
            {
              date: new Date('2024-08-24T03:00:00.000Z'),
              value: 7,
            },
            {
              date: new Date('2024-08-25T03:00:00.000Z'),
              value: 1,
            },
            {
              date: new Date('2024-08-26T03:00:00.000Z'),
              value: 10,
            },
            {
              date: new Date('2024-08-27T03:00:00.000Z'),
              value: 2,
            },
            {
              date: new Date('2024-08-28T03:00:00.000Z'),
              value: 1,
            },
            {
              date: new Date('2024-08-29T03:00:00.000Z'),
              value: 6,
            },
            {
              date: new Date('2024-08-30T03:00:00.000Z'),
              value: 6,
            },
            {
              date: new Date('2024-08-31T03:00:00.000Z'),
              value: 1,
            },
            {
              date: new Date('2024-09-01T03:00:00.000Z'),
              value: 7,
            },
            {
              date: new Date('2024-09-02T03:00:00.000Z'),
              value: 9,
            },
            {
              date: new Date('2024-09-03T03:00:00.000Z'),
              value: 0,
            },
            {
              date: new Date('2024-09-04T03:00:00.000Z'),
              value: 1,
            },
            {
              date: new Date('2024-09-05T03:00:00.000Z'),
              value: 7,
            },
            {
              date: new Date('2024-09-06T03:00:00.000Z'),
              value: 6,
            },
          ]}
          status={STATUS.ACTIVE}
        />

        <AvatarBlock
          avatarId="asd-123"
          image="https://dante-chatbot-pictures.s3.amazonaws.com/764a6813-da65-45d3-a0e4-cc8ec47c0307/avatar-764a6813-da65-45d3-a0e4-cc8ec47c0307-1722278074914.png"
          name="Copy of James"
          isDefault={false}
        />

        <AvatarBlock
          avatarId="asd-123-default"
          image="https://dante-chatbot-pictures.s3.amazonaws.com/764a6813-da65-45d3-a0e4-cc8ec47c0307/avatar-764a6813-da65-45d3-a0e4-cc8ec47c0307-1722278074914.png"
          isDefault={true}
          name="James"
        />
      </div>
      <div className="bg-grey-100 w-full h-screen p-size2">
        <h1>Chatbots</h1>
        <ChatbotDashboard />
      </div>
    </div>
  );
};

export default Atom;
