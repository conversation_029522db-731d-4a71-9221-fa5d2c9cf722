import * as React from 'react';

const ShareChatbotIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 64 64"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="m1 58.556c-.116 0-.233-.02-.346-.062-.418-.154-.684-.565-.652-1.01.025-.353 2.942-35.002 41.093-37.975l-.092-13.058c-.003-.41.246-.781.627-.934.38-.153.817-.059 1.1.241l20.997 22.218c.364.385.364.987.001 1.372l-21.128 22.417c-.282.301-.721.396-1.101.242-.382-.153-.631-.525-.627-.938l.121-12.726c-25.667 1.844-39.054 19.624-39.188 19.805-.193.262-.494.408-.805.408z"
    />
  </svg>
);

export default ShareChatbotIcon;
