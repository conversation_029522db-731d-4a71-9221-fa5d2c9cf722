import { describe, expect,it, vi } from 'vitest';

import * as authService from '@/services/auth.service';
import { useUserStore } from '@/stores/user/userStore';

import getUserInfo from '../user/getUserInfo';

import forgotPasswordUseCase from './forgotPassword';

// Mock functions in useUserStore, authService, and getUserInfo
vi.mock('@/stores/user/userStore', () => ({
  useUserStore: {
    getState: vi.fn()
  }
}));

vi.mock('@/services/auth.service', () => ({
  forgotPassword: vi.fn()
}));

vi.mock('../user/getUserInfo', () => ({
  default: vi.fn()
}));

describe('forgotPasswordUseCase', () => {
  it('should save auth details and user info on successful response', async () => {
    const mockSaveAuthDetail = vi.fn();
    const mockSetUser = vi.fn();

    // Mock user store functions
    useUserStore.getState.mockReturnValue({
      saveAuthDetail: mockSaveAuthDetail,
      setUser: mockSetUser
    });

    // Mock the response from authService and getUserInfo
    authService.forgotPassword.mockResolvedValue({
      status: 200,
      data: { access_token: 'test_access_token' }
    });

    const mockUserInfo = {
      id: 1,
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      date_created: '2023-01-01',
      login_count: 5
    };
    getUserInfo.mockResolvedValue(mockUserInfo);

    const result = await forgotPasswordUseCase({ email: '<EMAIL>' });

    expect(mockSaveAuthDetail).toHaveBeenCalledWith({
      access_token: 'test_access_token',
      user_id: mockUserInfo.id,
      first_name: mockUserInfo.first_name,
      last_name: mockUserInfo.last_name,
      email: mockUserInfo.email,
      date_created: mockUserInfo.date_created,
      login_count: mockUserInfo.login_count
    });
    expect(mockSetUser).toHaveBeenCalledWith(mockUserInfo);
    expect(result.data).toEqual({ access_token: 'test_access_token', ...mockUserInfo });
  });

  it('should return an error object if the API call fails', async () => {
    const error = new Error('Failed to reset password');
    authService.forgotPassword.mockRejectedValue(error);

    const result = await forgotPasswordUseCase({ email: '<EMAIL>' });

    expect(result).toEqual(error);
  });

  it('should return an empty object if the response status is not 200', async () => {
    authService.forgotPassword.mockResolvedValue({
      status: 400,
      data: {}
    });

    const result = await forgotPasswordUseCase({ email: '<EMAIL>' });

    expect(result).toEqual({});
  });
});
