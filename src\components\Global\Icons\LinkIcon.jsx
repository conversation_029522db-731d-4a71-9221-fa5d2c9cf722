import * as React from 'react';
const LinkIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.742 4a3.3 3.3 0 0 0-2.317.924l-.005.006-.944.938a.549.549 0 0 0 .774.778l.94-.935a2.195 2.195 0 0 1 3.105 3.103l-1.643 1.643a2.196 2.196 0 0 1-3.31-.237.549.549 0 0 0-.879.657 3.292 3.292 0 0 0 4.965.356l1.646-1.646.007-.007A3.292 3.292 0 0 0 12.741 4M9.136 7.81a3.3 3.3 0 0 0-2.564.957l-1.647 1.646-.006.007a3.293 3.293 0 0 0 4.656 4.656l.006-.007.939-.939a.549.549 0 1 0-.776-.776l-.935.935a2.195 2.195 0 0 1-3.104-3.103l1.643-1.643a2.196 2.196 0 0 1 3.31.237.549.549 0 0 0 .879-.658A3.3 3.3 0 0 0 9.136 7.81"
      fill="currentColor"
    />
  </svg>
);
export default LinkIcon;
