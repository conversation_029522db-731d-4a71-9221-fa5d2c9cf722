import Lottie from 'lottie-react';
import Spinner from './Spinner.json';
import { colorify, getColors } from 'lottie-colorify';

/**
 * Converts a hex color string (e.g. "#FFFFFF") to an array of normalized RGB values.
 * @param {string} hex - The hex color string.
 * @returns {number[]} Normalized RGB array (e.g. [1, 1, 1]).
 */
const hexToNormalizedRGB = (hex) => {
  if (hex[0] === '#') {
    hex = hex.substring(1);
  }
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;
  return [r, g, b];
};

/**
 * Traverses the Lottie JSON and, for each fill ("fl") shape with an animated color,
 * converts its color property to a static value.
 * @param {Object} json - The Lottie animation JSON.
 * @param {string} hexColor - The color to set (hex string).
 * @returns {Object} The updated JSON with static fill colors.
 */
const convertAnimatedColorToStatic = (json, hexColor) => {
  const newColor = hexToNormalizedRGB(hexColor);
  // Create a deep copy of the JSON so we don't mutate the original
  const updatedJson = JSON.parse(JSON.stringify(json));
  
  updatedJson.layers.forEach((layer) => {
    if (layer.shapes && Array.isArray(layer.shapes)) {
      layer.shapes.forEach((shape) => {
        if (
          shape.ty === 'fl' &&
          shape.c &&
          shape.c.a === 1 &&
          Array.isArray(shape.c.k)
        ) {
          // Overwrite the animated property with our static newColor
          shape.c.a = 0;
          shape.c.k = newColor;
        }
      });
    }
  });
  
  return updatedJson;
};

/**
 * DSpinner component renders a customizable loading spinner.
 * 
 * @param {Object} props - The properties passed to the DSpinner component.
 * @param {string} [props.color] - Custom color for the spinner (e.g., '#FF0000' for red).
 * @param {Object} [props.style] - Custom styles for the spinner container.
 * @returns {JSX.Element} The rendered spinner component.
 */
const DSpinner = ({ color, ...props }) => {
  const getSpinnerData = () => {
    // If a custom color is provided, override using our conversion function
    if (color) {
      return colorify([color], convertAnimatedColorToStatic(Spinner, color));
    }
    // If localStorage theme is dark, set spinner to white
    if (localStorage.getItem('theme') === 'dark') {
      return colorify(['#FFFFFF'], convertAnimatedColorToStatic(Spinner, '#FFFFFF'));
    }
    // Otherwise, return the original spinner
    return Spinner;
  };

  return (
    <Lottie 
      animationData={getSpinnerData()} 
      loop={true} 
      autoplay={true} 
      style={{ height: 40, width: 40 }} 
      {...props} 
    />
  );
};

export default DSpinner;