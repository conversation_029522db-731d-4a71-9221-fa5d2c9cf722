# AI Voice Demo Component

This component implements a voice-based interaction between the user and an AI assistant, utilizing WebSockets for real-time communication.

## Features

- Real-time voice capture and transmission
- Audio visualization for both user and AI voice
- Configurable settings for voice type and knowledge base
- Status indicators showing the current state of the system
- User authentication integration

## Authentication

The component now integrates with the application's user authentication system:

- When a user is logged in, their authentication token is automatically retrieved from the Zustand user store.
- If the token includes a "Bearer" prefix, it's automatically removed before being sent to the backend.
- For non-authenticated users, a default demo token is used.
- The authentication token is not exposed in the UI and cannot be modified by users.
- The token is automatically preserved when other settings are updated.

## Component Structure

- `index.jsx`: Main component that renders the UI
- `useAudioState.js`: Hook that manages the audio state and WebSocket connection
- `WebSocketHandler.js`: Handles WebSocket communication with the backend
- `AudioHandler.js`: Manages audio playback and processing
- `UIComponents.jsx`: React components for the UI elements
- `SettingsPanel.jsx`: Panel for configuring voice settings
- `AudioUtils.js`: Utility functions for audio processing
- `VisualizationUtils.js`: Utility functions for audio visualization
- `config.js`: Configuration options for voices and knowledge bases

## WebSocket Protocol

The WebSocket connection sends an initialization message with:
- `auth_token`: User authentication token
- `kb_id`: Knowledge base ID for context
- `voice_id`: Voice ID for the AI assistant
- `initial_message`: Optional greeting message

## Usage

The component is designed to be used as a page component in a React Router setup, or embedded within another component.

```jsx
import AIVoiceDemo from '@/pages/AIVoiceDemo';

const MyPage = () => {
  return (
    <div>
      <h1>My Voice Assistant</h1>
      <AIVoiceDemo />
    </div>
  );
};
