import * as React from 'react';
const AvatarIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.854 6a3 3 0 1 1 6 0 3 3 0 0 1-6 0m3-4A4 4 0 0 0 8.21 9.647l-.74.068a4.97 4.97 0 0 0-4.37 3.743l-.045.176a1.86 1.86 0 0 0 .489 1.763A5.47 5.47 0 0 0 7.414 17h4.88a5.47 5.47 0 0 0 3.87-1.603c.462-.462.647-1.13.49-1.763l-.045-.176a4.97 4.97 0 0 0-4.37-3.743l-.74-.068A4 4 0 0 0 9.854 2m-.579 8.555a6.4 6.4 0 0 1 1.158 0l1.715.156a3.97 3.97 0 0 1 3.491 2.99l.044.175a.86.86 0 0 1-.225.814A4.47 4.47 0 0 1 12.294 16h-4.88a4.47 4.47 0 0 1-3.163-********** 0 0 1-.225-.814l.043-.175a3.97 3.97 0 0 1 3.492-2.99z"
      fill="currentColor"
    />
  </svg>
);
export default AvatarIcon;
