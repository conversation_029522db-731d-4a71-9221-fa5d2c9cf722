import AIVoiceDemo from '@/pages/AIVoiceDemo';
import BrainIcon from '../../Global/Icons/BrainIcon';
import ColorIcon from '../../Global/Icons/ColorIcon';
import ConnectIcon from '../../Global/Icons/ConnectIcon';
import DashboardIcon from '../../Global/Icons/DashboardIcon';
import EditIcon from '../../Global/Icons/EditIcon';
import EyeIcon from '../../Global/Icons/EyeIcon';
import GraphIcon from '../../Global/Icons/GraphIcon';
import PersonIcon from '../../Global/Icons/PersonIcon';
import SecureIcon from '../../Global/Icons/SecureIcon';
import ZapIcon from '../../Global/Icons/ZapIcon';

import { SidebarStepEnum } from './SidebarStepEnum';
import AiVoiceIcon from '@/components/Global/Icons/AiVoiceIcon';

export const chatbotNavItems = [
  {
    id: SidebarStepEnum.OVERVIEW,
    type: 'link',
    icon: <EyeIcon />,
    label: 'Overview',
    link: ''
  },
  {
    id: SidebarStepEnum.TABS,
    type: 'link',
    icon: <DashboardIcon />,
    label: 'Tabs',
    link: '/tabs',
    permission: 'tabs'
  },
  {
    id: SidebarStepEnum.INSIGHTS,
    type: 'link',
    icon: <GraphIcon />,
    label: 'Insights',
    link: '/insights',
    permission: 'insights'
  },
  {
    id: SidebarStepEnum.INTEGRATIONS,
    type: 'link',
    icon: <ConnectIcon />,
    label: 'Integrations',
    link: '/integrations',
    permission: ['api_key', 'integrations']
  },
  {
    id: SidebarStepEnum.SAFETY,
    type: 'link',
    icon: <SecureIcon />,
    label: 'Safety',
    link: '/safety',
    permission: 'chatbot_safety'
  },

  {
    type: 'group',
    icon: '',
    label: 'Customize',
    link: '/',
    items: [
      {
        id: SidebarStepEnum.STYLING,
        type: 'link',
        icon: <ColorIcon />,
        label: 'Styling',
        link: '/styling',
        permission: 'chatbot_styling'
      },
      {
        id: SidebarStepEnum.KNOWLEDGE,
        type: 'link',
        icon: <BrainIcon />,
        label: 'Knowledge',
        link: '/knowledge',
        permission: 'chatbot_knowledge'
      },
      {
        id: SidebarStepEnum.PERSONALITY,
        type: 'link',
        icon: <PersonIcon />,
        label: 'Personality',
        link: '/personality',
        permission: 'chatbot_personality'
      },
      {
        id: SidebarStepEnum.SET_UP,
        type: 'link',
        icon: <EditIcon />,
        label: 'Core settings',
        link: '/setup',
        permission: 'chatbot_core_settings'
      },
      {
        id: SidebarStepEnum.POWER_UPS,
        type: 'link',
        icon: <ZapIcon />,
        label: 'Power-ups',
        link: '/powerups',
        permission: 'chatbot_powerups'
      },
      {
        id: SidebarStepEnum.REALTIME_VOICE,
        type: 'link',
        icon: <AiVoiceIcon />,
        label: 'Realtime Voice',
        link: '/realtime-voice',
        permission: 'chatbot_realtime_voice'
      }
    ]
  }
];

export default chatbotNavItems;
