
import http from './http';
import { DEFAULT_HEADERS, UPLOAD_FILE_HEADERS } from './constants.service';

export const postSharedCreateConversation = (data) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'conversations/shared',
    data.collection_fields,
    {
      params: {
        kb_id: data.kb_id,
        token: data.token
      },
      headers: DEFAULT_HEADERS
    }
  );
};

/**
 * Sends a POST request to create a new conversation.
 *
 * @param {Object} data - The data for the conversation creation request.
 * @param {string} data.kb_id - The knowledge base ID related to the conversation.
 * @returns {Promise} - A promise that resolves when the HTTP request completes.
 */
export const postCreateConversation = ({ kb_id }) => {
  return http.post(
    `${import.meta.env.VITE_APP_BASE_API}conversations`, // API endpoint for conversations
    [], // Empty array as the request payload
    {
      params: { kb_id }, // Pass the kb_id as a query parameter
      headers: DEFAULT_HEADERS
    }
  );
};

export const getConversationsFromCurrentUser = () => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'conversations', {
    params: {
      limit: 1000
    },
    headers: DEFAULT_HEADERS
  });
};

export const getConversationsByKbId = (kb_id) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'conversations', {
    params: {
      limit: 1000,
      kb_id
    },
    headers: DEFAULT_HEADERS
  });
};

export const getSharedConversations = (
  kb_id,
  date_created_from,
  date_created_to,
  timezone,
  limit,
  offset
) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + `conversations/shared/${kb_id}`, {
    params: {
      date_created_from,
      date_created_to,
      limit,
      offset,
      timezone
    },
    headers: DEFAULT_HEADERS
  });
};

/**
 * Renames an existing conversation.
 *
 * @param {string} conversation_id - The ID of the conversation to be renamed.
 * @param {string} name - The new name for the conversation.
 * @returns {Promise} The HTTP response from the conversation rename request.
 */
export const patchRenameConversation = (conversation_id, name) => {
  return http.patch(
    `${import.meta.env.VITE_APP_BASE_API}conversations/${conversation_id}`,
    { name }, // Send the new name in the request body
    {
      headers: DEFAULT_HEADERS
    }
  );
};

/**
 * Deletes an existing conversation by its ID.
 *
 * @param {string} id - The ID of the conversation to delete.
 * @returns {Promise} The HTTP response from the delete request.
 */
export const deleteConversationById = (id) => {
  return http.delete(`${import.meta.env.VITE_APP_BASE_API}conversations/${id}`, {
    headers: DEFAULT_HEADERS
  });
};

export const getConversationById = (id, timezone) => {
  return http.get(`${import.meta.env.VITE_APP_BASE_API}conversations/${id}`, {
    params: {
      timezone
    },
    headers: DEFAULT_HEADERS
  });
};
/**
 * Creates a shareable link for a conversation by its ID.
 *
 * @param {string} id - The ID of the conversation to generate a shareable link for.
 * @returns {Promise} The HTTP response containing the shareable link information.
 */
export const postConversationShareLink = (id) => {
  return http.post(
    `${import.meta.env.VITE_APP_BASE_API}conversations/share/${id}`, // Use template literals for readability
    {},
    {
      headers: DEFAULT_HEADERS
    }
  );
};

export const downloadChatRecordLog = (kb_id, dateFrom, dateTo, format = 'csv', timezone) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + `conversations/shared/${kb_id}/export`, {
    params: {
      date_created_from: dateFrom,
      date_created_to: dateTo,
      type: 'text/'+ format,
      timezone
    }
  });
};

export const downloadLeads = (kb_id, dateFrom, dateTo, format = 'csv', timezone) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + `conversations/shared/${kb_id}/export-data`, {
    params: {
      date_created_from: dateFrom,
      date_created_to: dateTo,
      type: 'text/'+ format,
      timezone
    }
  });
};

/**
 * Searches for conversations based on a query string and knowledge base ID.
 *
 * @param {Object} params - The search parameters.
 * @param {string} params.query - The search term used to filter conversations.
 * @param {string} params.kb_id - The knowledge base ID for filtering conversations.
 * @returns {Promise} The HTTP response containing the search results.
 */
export const getSearchConversations = ({ query, kb_id }) => {
  return http.get(`${import.meta.env.VITE_APP_BASE_API}conversations/search`, {
    params: {
      query,
      kb_id
    },
    headers: DEFAULT_HEADERS
  });
};


export const getDynamicLeadGen = ({ kb_id, conversation_id, question, answer, input_id, stop_after }) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'conversations/leadgen-dynamic', {
      question,
      answer,
      input_id,
      stop_after
    },
    {
      params: {
        kb_id,
        conversation_id

      },
      headers: DEFAULT_HEADERS
    }
  );
};

export const exportStatistics = (kb_id, date_created_from, date_created_to, fields) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + `conversations/shared/${kb_id}/export-statistics`, {
    params: {
      date_from: date_created_from,
      date_to: date_created_to,
      fields
    },
    headers: {...DEFAULT_HEADERS},
  });
};
