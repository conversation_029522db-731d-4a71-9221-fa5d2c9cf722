#!/bin/bash

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is not installed. Please install it to proceed."
    exit 1
fi

# Export AWS credentials
export AWS_ACCESS_KEY_ID=********************
export AWS_SECRET_ACCESS_KEY=VtdxdcSd+U0mup9fIjD9rtyhGQk2uIaktQhbWjCD

# Install dependencies and build the project
npm install
npm run build:staging

# Sync non-HTML files with caching headers
aws s3 sync build/ s3://dante-20-development-demo --delete --exclude "*.html" --cache-control "max-age=3600"

# Copy HTML files without caching for immediate updates
aws s3 cp build/ s3://dante-20-development-demo --recursive --exclude "*" --include "*.html" --cache-control "no-cache, no-store, must-revalidate"

# Invalidate CloudFront cache for immediate change visibility
aws cloudfront create-invalidation --distribution-id E61K8E3CB4M5W --paths "/*"
