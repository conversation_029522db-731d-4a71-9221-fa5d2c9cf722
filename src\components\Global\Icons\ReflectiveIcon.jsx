const ReflectiveIcon = (props) => {
  return (
    <svg
      width="18"
      height="22"
      viewBox="0 0 18 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.0002 1.39941V4.69941M9.0002 6.79941V9.79941M9.0002 12.1994V15.1994M9.0002 17.5994V20.5994M1.2002 4.78365V17.2152C1.2002 17.4104 1.38361 17.5536 1.57296 17.5062L5.77296 16.4562C5.90651 16.4228 6.0002 16.3028 6.0002 16.1652V5.83365C6.0002 5.69599 5.90651 5.57599 5.77296 5.5426L1.57296 4.4926C1.38361 4.44527 1.2002 4.58848 1.2002 4.78365ZM16.8002 4.78365V17.2152C16.8002 17.4104 16.6168 17.5536 16.4274 17.5062L12.2274 16.4562C12.0939 16.4228 12.0002 16.3028 12.0002 16.1652V5.83365C12.0002 5.69599 12.0939 5.57599 12.2274 5.5426L16.4274 4.4926C16.6168 4.44527 16.8002 4.58848 16.8002 4.78365Z"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default ReflectiveIcon;
