import React, { useEffect, useState } from 'react';
import DButton from '../Global/DButton';
import DCheckbox from '../Global/DCheckbox';
import DInput from '../Global/DInput/DInput';
import DInputBlock from '../Global/DInput/DInputBlock';
import GoogleIcon from '../Global/Icons/GoogleIcon';
import LayoutOnBoarding from './LayoutOnBoarding';
import { SignUpStepsEnum } from './signUpSteps';
import * as userService from '@/services/user.service';
import validateEmail from '@/helpers/validateEmail';
import './style.css';
import DRadioGroup from '../Global/DRadioGroup';
const UserRegister = ({
  handleGoogleLogIn,
  setCurrentStep,
  handleChange,
  email,
  contactConsent,
  setContactConsent,
}) => {
  const [isCheckboxChecked, setIsCheckboxChecked] = useState(false);
  const [pending, setPending] = useState(false);
  const [checkboxError, setCheckboxError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [data, setData] = useState({
    contact_consent: contactConsent,
    new_password: '',
  });

  const handleSignUp = async () => {
    let hasError = false;

    // Email validation
    if (!email || email.trim() === '') {
      setEmailError('Email is required.');
      hasError = true;
    } else if (!validateEmail(email)) {
      setEmailError('Invalid email address.');
      hasError = true;
    } else if (email.includes('+')) {
      setEmailError(
        'Please use a valid email and keep one email per user only. Emails with \'+\' are not allowed.'
      );
      hasError = true;
    } else {
      setEmailError('');
    }

    // Checkbox validation
    if (!isCheckboxChecked) {
      setCheckboxError(
        'You must agree to the Terms of Service and Privacy Policy to proceed.'
      );
      hasError = true;
    } else {
      setCheckboxError('');
    }

    if (hasError) return;

    try {
      setPending(true);
      const response = await userService.sendEmailVerification(email);
      if (response.status === 200) {
        setCurrentStep(SignUpStepsEnum.VALIDATE_EMAIL);
      }
    } catch (e) {
      setEmailError(e.response.data.detail);
      console.log(e);
    } finally {
      setPending(false);
    }
  };

  const handleCheckboxChange = (checked) => {
    setIsCheckboxChecked(checked);
    if (checked) {
      setCheckboxError('');
    }
  };

  const handleRadioButtonChange = (checked) => {
    setContactConsent(checked);
    setData((prevData) => ({
      ...prevData,
      contact_consent: checked,
    }));
  };

  const handleGoogleSignUp = () => {
    if (!isCheckboxChecked) {
      setCheckboxError(
        'You must agree to the Terms of Service and Privacy Policy to proceed.'
      );
      return;
    }
    handleGoogleLogIn();
  };

  useEffect(() => {
    const startClick = () => {
      const checkboxes = document.querySelectorAll('.check-link label a');

      checkboxes.forEach((checkbox) => {
        checkbox.addEventListener('click', (e) => {
          e.stopPropagation();
        });
      });
    };
    startClick();
  }, []);

  return (
    <LayoutOnBoarding
      title="Welcome to AI simplicity"
      subtitle="Get started with Dante AI"
    >
      <div className="flex flex-col gap-size3 items-center w-full">
        <div className="flex flex-col w-full gap-size2">
          <DInputBlock label="Email" name="email">
            <DInput
              name="email"
              type="email"
              autoComplete="email"
              placeholder="Enter your email"
              onChange={(e) => handleChange(e, 'email')}
              value={email}
              error={emailError}
            />
          </DInputBlock>

        <div className="flex gap-size0 w-full">
          <DCheckbox
            label={`<p>
                I agree to Dante AI's
                <a
                href="https://www.dante-ai.com/terms-of-service"
                target="_blank"
                rel="noopener noreferrer"
              >
                Terms of Service
              </a>
              and
              <a
                href="https://www.dante-ai.com/privacy-policy"
                target="_blank"
                rel="noopener noreferrer"
              >
                Privacy Policy
                </a>
              </p>`}
            name="agree"
            className="check-link"
            onChange={handleCheckboxChange}
            error={checkboxError}
          />
        </div>
          <DButton
            type="submit"
            variant="dark"
            size="lg"
            fullWidth
            onClick={handleSignUp}
            name="sign-up-button"
            className='!mt-size1'
            loading={pending}
          >
            Sign up with email
          </DButton>
        </div>
        <DCheckbox
          label="Send me additional free support and feature updates"
          onChange={handleRadioButtonChange}
          checked={data.contact_consent}
          name="contact_consent"
        />
        <p className="text-grey-50">or</p>
        <DButton
          variant="light"
          size="sm"
          className="w-max"
          onClick={handleGoogleSignUp}
        >
          <GoogleIcon />
          Sign up with Google
        </DButton>
      </div>
    </LayoutOnBoarding>
  );
};

export default UserRegister;
