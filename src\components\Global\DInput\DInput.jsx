import React, { useState } from 'react';

import { Input } from '@headlessui/react';

import DButtonIcon from '../DButtonIcon';
import EyeClosedIcon from '../Icons/EyeClosedIcon';
import EyeIcon from '../Icons/EyeIcon';
import DTransition from '../DTransition';
import { COMMON_CLASSNAMES } from '@/constants';
import ValidationError from '../ValidationError';
import clsx from 'clsx';

const DInput = React.forwardRef(
  (
    {
      children,
      iconPlacement,
      error,
      type,
      icon,
      size,
      notClickable = false,
      className,
      autoComplete,
      inputClassName,
      hiddenError = false,
      suffixText,
      required = false,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const inputRef = React.useRef(null);
    const combinedRef = (node) => {
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
      inputRef.current = node;
    };

    let inputSize = 'text-sm';

    if (autoComplete === 'one-time-code') {
      inputSize = 'text-3xl text-center';
    }

    // Toggle password visibility
    const togglePasswordVisibility = () => {
      setShowPassword((prevState) => !prevState);
    };

    // Determine input type (text or password)
    const getInputType = () => {
      if (type === 'password') {
        return showPassword ? 'text' : 'password';
      }

      return type;
    };

    // Render icon if needed
    const renderIcon = (position) => {
      if (iconPlacement === position) {
        return <span>{icon}</span>;
      }
      return null;
    };

    return (
      <div className="flex flex-col w-full">
        <div className="flex gap-1 items-center w-full">
          <div
            className={clsx(
              'w-full',
              'flex',
              'transition-all',
              COMMON_CLASSNAMES.transition.duration.short,
              'items-center',
              'rounded-size1',
              'border-[1px]',
              'focus-within:border-black/20',
              'border-black/5',
              'py-size1',
              'bg-white',
              'px-size2',
              'gap-size1',
              'group',
              className,
              error && 'border-red-500',
              autoComplete === 'one-time-code' ? 'h-[86px]' : 'h-11'
            )}
          >
            {renderIcon('pre')}
            <Input
              ref={combinedRef}
              data-testid={`d-input-${props.name ?? props.id ?? ''}`}
              {...props}
              required={required}
              type={getInputType()}
              autoComplete={autoComplete}
              className={clsx(
                'dbutton',
                'w-full',
                'placeholder-black/20',
                'data-[focus]:border-black/20',
                'text-black',
                'bg-white',
                inputSize,
                inputClassName,
                'text-min-safe-input',
                type === 'number' && 'no-spinner'
              )}
            />
            {renderIcon('post')}
            {type === 'number' && (
              <div className="flex flex-col opacity-0 group-hover:opacity-100">
                <DButtonIcon
                  size="xs"
                  variant="ghost"
                  onClick={() => {
                    inputRef.current?.stepUp();
                  }}
                  name="increase"
                >
                  +
                </DButtonIcon>
                <DButtonIcon
                  size="xs"
                  variant="ghost"
                  onClick={() => {
                    inputRef.current?.stepDown();
                  }}
                  name="decrease"
                >
                  -
                </DButtonIcon>
              </div>
            )}
            {suffixText && (
              <p className="text-sm tracking-tight">{suffixText}</p>
            )}
          </div>
          {type === 'password' && (
            <DButtonIcon
              size="lg"
              variant="grey"
              onClick={togglePasswordVisibility}
              name="toggle-password"
            >
              {showPassword ? <EyeClosedIcon /> : <EyeIcon />}
            </DButtonIcon>
          )}
        </div>
        {!hiddenError && <ValidationError error={error} />}
      </div>
    );
  }
);

DInput.displayName = 'DInput';

export default DInput;
