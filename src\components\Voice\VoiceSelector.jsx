import React, { useState, useRef, useEffect } from 'react';
import './VoiceSelector.css';
import clsx from 'clsx';
import { useUserStore } from '@/stores/user/userStore';
import useModalStore from '@/stores/modal/modalStore';

const VoiceSelector = ({
  voices,
  selectedVoice,
  onVoiceChange,
  disabled = false,
  error,
  autoCollapseOnStep = false
}) => {
  const [currentlyPlayingId, setCurrentlyPlayingId] = useState(null);
  const [showAllVoices, setShowAllVoices] = useState(false);
  const [activeProvider, setActiveProvider] = useState(null);
  const audioRef = useRef(null);
  const [audioLevels, setAudioLevels] = useState({});
  const user = useUserStore(state => state.user);
  const openPlansModal = useModalStore(state => state.openPlansModal);

  // Group voices by provider
  const groupedVoices = voices.reduce((acc, voice) => {
    // Use the provider from the voice object directly
    const provider = voice.provider || 'Other';

    if (!acc[provider]) {
      acc[provider] = [];
    }
    acc[provider].push(voice);
    return acc;
  }, {});

  const mainProviders = ['OpenAI', 'ElevenLabs', 'Cartesia'];
  mainProviders.forEach(provider => {
    if (!groupedVoices[provider]) {
      groupedVoices[provider] = [];
    }
  });

  // Sort providers to ensure a consistent order (OpenAI first, then ElevenLabs, then Cartesia)
  const sortedProviders = Object.keys(groupedVoices).sort((a, b) => {
    const order = { 'OpenAI': 1, 'ElevenLabs': 2, 'Cartesia': 3 };
    return (order[a] || 99) - (order[b] || 99);
  });

  useEffect(() => {

    if (sortedProviders.length > 0) {
      // If we have a selected voice, find its provider
      if (selectedVoice) {

        // Find the voice in all providers
        for (const provider of sortedProviders) {
          const voicesInProvider = groupedVoices[provider] || [];

          const selectedVoiceInProvider = voicesInProvider.find(voice => voice.value === selectedVoice);

          if (selectedVoiceInProvider) {
            // Found the provider that contains the selected voice
            setActiveProvider(provider);

            // If the selected voice is beyond the first 6 voices, show all voices
            const voiceIndex = voicesInProvider.findIndex(voice => voice.value === selectedVoice);
            if (voiceIndex >= 6) {
              setShowAllVoices(true);
            }

            return; // Exit the effect after finding the provider
          }
        }
      }

      // If no selected voice or provider not found, use default behavior
      // Find the first provider that has voices
      const providerWithVoices = sortedProviders.find(provider =>
        groupedVoices[provider] && groupedVoices[provider].length > 0
      );

      // If we found a provider with voices, set it as active
      // Otherwise, default to the first provider
      setActiveProvider(providerWithVoices || sortedProviders[0]);
    }
  }, [sortedProviders, selectedVoice, groupedVoices]);

  // Stop audio when component unmounts
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  // Simulate audio visualization with more dynamic waveform
  useEffect(() => {
    let animationFrameId;
    let startTime;

    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;

      // Only update if we're playing something
      if (currentlyPlayingId) {
        // Create a more dynamic waveform effect
        const levels = {};
        // Base level with sine wave modulation
        const baseLevel = Math.abs(Math.sin(elapsed / 300)) * 0.3 + 0.5;
        // Add some randomness for more natural look
        const randomFactor = Math.random() * 0.2;
        levels[currentlyPlayingId] = Math.min(baseLevel + randomFactor, 1.0);
        setAudioLevels(levels);
      }

      animationFrameId = requestAnimationFrame(animate);
    };

    animationFrameId = requestAnimationFrame(animate);

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [currentlyPlayingId]);

  // Auto-collapse when component will unmount or be hidden, but keep expanded if selected voice is in "show more"
  useEffect(() => {
    return () => {
      // This will run when the component is about to unmount
      // Check if selected voice is in the "show more" section
      if (selectedVoice && activeProvider) {
        const voicesInProvider = groupedVoices[activeProvider] || [];
        const voiceIndex = voicesInProvider.findIndex(voice => voice.value === selectedVoice);
        
        // If voice is in first 6 (visible without show more), we can safely collapse
        if (voiceIndex >= 0 && voiceIndex < 6) {
          setShowAllVoices(false);
        }
        // Otherwise keep expanded so the selected voice stays visible
      }
    };
  }, []); // Only run on unmount, not on every render

  // When selectedVoice changes, make sure it's visible (auto-expand if in "show more")
  useEffect(() => {
    if (selectedVoice && activeProvider) {
      const voicesInProvider = groupedVoices[activeProvider] || [];
      const voiceIndex = voicesInProvider.findIndex(voice => voice.value === selectedVoice);
      
      // If selected voice is in "show more" section, auto-expand
      if (voiceIndex >= 6) {
        setShowAllVoices(true);
      }
    }
  }, [selectedVoice, activeProvider, groupedVoices]);

  // Handle auto-collapse when moving to next step in the parent
  useEffect(() => {
    if (autoCollapseOnStep && selectedVoice && activeProvider) {
      const voicesInProvider = groupedVoices[activeProvider] || [];
      const voiceIndex = voicesInProvider.findIndex(voice => voice.value === selectedVoice);
      
      // Only collapse if the selected voice is in the first 6 items
      if (voiceIndex >= 0 && voiceIndex < 6) {
        setShowAllVoices(false);
      }
    }
  }, [autoCollapseOnStep, selectedVoice, activeProvider, groupedVoices]);

  const selectVoice = (voiceId) => {
    if (disabled) return;
    onVoiceChange(voiceId);
  };

  const playPreview = async (voiceId, e) => {
    if (e) {
      e.stopPropagation();
    }

    // If we have an audio playing, stop it
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;

      // If we're clicking the same voice that's playing, just stop it
      if (currentlyPlayingId === voiceId) {
        setCurrentlyPlayingId(null);
        setAudioLevels({});
        return;
      }
    }

    // Find the voice data
    const voice = voices.find(v => v.value === voiceId);

    if (voice?.soundUrl) {
      try {
        // Create new audio instance if needed
        if (!audioRef.current) {
          audioRef.current = new Audio();
        }

        audioRef.current.src = voice.soundUrl;
        audioRef.current.onended = () => {
          setCurrentlyPlayingId(null);
          setAudioLevels({});
        };
        await audioRef.current.play();
        setCurrentlyPlayingId(voiceId);
      } catch (error) {
        console.error('Error playing audio preview:', error);
        setCurrentlyPlayingId(null);
        setAudioLevels({});
      }
    }
  };

  // Get voices for the active provider
  const activeVoices = activeProvider ? groupedVoices[activeProvider] || [] : [];

  // Determine how many voices to show (6 in the grid - 3x2)
  const visibleVoices = showAllVoices ? activeVoices : activeVoices.slice(0, 6);
  const hasMoreVoices = activeVoices.length > 6;

  // Render a voice card with waveform
  const renderVoiceCard = (voice) => {
    const isPlaying = currentlyPlayingId === voice.value;
    const isSelected = selectedVoice === voice.value;
    const audioLevel = audioLevels[voice.value] || 0;

    return (
      <div
        key={voice.value}
        onClick={() => selectVoice(voice.value)}
        className={clsx(
          'border rounded-size1 p-size1 cursor-pointer flex flex-col',
          'transition-all duration-200 relative group',
          'h-[70px]',
          {
            'border-purple-200 bg-purple-5 shadow-sm': isSelected,
            'border-grey-5 hover:border-purple-100 hover:shadow-sm': !isSelected && !disabled,
            'border-grey-5': !isSelected && disabled,
            'opacity-60 cursor-not-allowed': disabled
          }
        )}
      >
        {/* Selected indicator */}
        {/* {isSelected && (
          <div className="absolute -top-1 -right-1 bg-purple-200 text-white rounded-full p-0.5 shadow-sm z-10 animate-fadeIn">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        )} */}

        {/* Voice name and play button */}
        <div className="flex items-center justify-between mb-1">
          <span className={clsx('text-xs transition-all truncate max-w-[75%]', {
            'font-medium': isSelected,
            'font-regular group-hover:text-purple-200': !isSelected && !disabled,
            'font-regular': !isSelected && disabled
          })}>
            {voice.label}
          </span>
          {voice.soundUrl && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                playPreview(voice.value, e);
              }}
              className={clsx(
                'p-0.5 rounded-full transition-colors',
                {
                  'text-purple-200 hover:text-purple-300 hover:bg-purple-5': isPlaying,
                  'text-grey-50 hover:text-purple-200 hover:bg-purple-5': !isPlaying && !disabled,
                  'text-grey-50': !isPlaying && disabled
                }
              )}
              disabled={disabled}
            >
              {isPlaying ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          )}
        </div>

        {/* Combined audio visualization and provider badge */}
        <div className="flex items-center gap-1 mb-1">
          {/* Provider badge */}
          <span className={clsx(
            'text-2xs px-1.5 py-0.5 rounded-full transition-all flex-shrink-0',
            isSelected
              ? 'bg-purple-5 text-purple-200'
              : !disabled
                ? 'bg-grey-2 text-grey-50 group-hover:bg-purple-5 group-hover:text-purple-200'
                : 'bg-grey-2 text-grey-50'
          )}>
            {voice.provider}
          </span>

          {/* Compact audio visualization - shown only while playing */}
          {isPlaying && (
            <div className={clsx(
              'h-4 flex-grow rounded-full overflow-hidden flex items-center justify-around px-1',
              isSelected ? 'bg-purple-10' : 'bg-grey-2',
              'transition-all duration-300'
            )}>
              {Array.from({ length: 9 }).map((_, i) => {
                // Create a dynamic height based on position and audio level with phase shift
                const phaseShift = i / 9 * Math.PI * 2;
                const baseHeight = 30;
                const amplitude = 40 * audioLevel;
                const speed = 150;

                // Different frequencies for odd/even bars
                const frequency = i % 3 === 0 ? 1.5 : (i % 3 === 1 ? 1.2 : 1);
                const barHeight = baseHeight + Math.sin(phaseShift + Date.now() / speed * frequency) * amplitude;

                return (
                  <div
                    key={i}
                    className={clsx(
                      'rounded-full transition-all',
                      'bg-purple-200',
                      'w-[1.5px]'
                    )}
                    style={{
                      height: `${barHeight}%`,
                      transition: 'height 150ms ease-in-out',
                      opacity: 1
                    }}
                  />
                );
              })}
            </div>
          )}
          </div>
        {voice.description && (
          <div className="text-2xs text-grey-50 overflow-hidden flex-grow whitespace-nowrap overflow-ellipsis">
            {voice.description}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full voice-selector-container">
      {/* Error message */}
      {error && (
        <p className="text-red-500 text-xs mb-2">{error}</p>
      )}

      {/* Provider tabs */}
      <div className="flex border-b border-grey-5 mb-3 overflow-x-auto hide-scrollbar h-[41px]">
        {sortedProviders.map(provider => {
          // Only show main providers or providers with voices
          const isMainProvider = ['OpenAI', 'ElevenLabs', 'Cartesia'].includes(provider);
          const hasVoices = groupedVoices[provider]?.length > 0;

          if (!isMainProvider && !hasVoices) return null;

          return (
            <button
              key={provider}
              onClick={() => {
                setActiveProvider(provider);
                // Reset selected voice when switching providers
                if (activeProvider !== provider && hasVoices) {
                  const firstVoiceInProvider = groupedVoices[provider][0];
                  if (firstVoiceInProvider) {
                    onVoiceChange(firstVoiceInProvider.value);
                  }
                }
              }}
              className={clsx(
                'px-4 py-2 text-sm whitespace-nowrap transition-all duration-200',
                activeProvider === provider
                  ? 'text-purple-200 border-b-2 border-purple-200 font-medium'
                  : 'text-grey-50 hover:text-grey-75 font-regular',
                !hasVoices && 'opacity-50 cursor-not-allowed'
              )}
              disabled={!hasVoices}
            >
              {provider}
              <span className={clsx(
                'ml-1 text-xs',
                activeProvider === provider ? 'text-grey-50' : 'text-grey-30'
              )}>({groupedVoices[provider]?.length || 0})</span>
            </button>
          );
        })}
      </div>

      {/* Voice grid - 3 columns for smaller cards */}
      <div className="grid grid-cols-3 gap-2 mb-3 min-h-[140px]">
        {visibleVoices.length > 0 ? (
          visibleVoices.map(renderVoiceCard)
        ) : (
          <div className="col-span-3 p-4 text-center text-grey-50 border border-grey-5 rounded-size1 bg-grey-1 animate-fadeIn h-[70px] flex items-center justify-center">
            {sortedProviders.length > 0
              ? (
                <div className="flex flex-col items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-grey-30 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  <p className="text-sm">No voices available for <span className="font-medium">{activeProvider}</span></p>
                </div>
              )
              : (
                <div className="flex flex-col items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-grey-30 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p className="text-sm">Please select a voice provider first</p>
                </div>
              )
            }
          </div>
        )}
      </div>

      {/* Show more button */}
      {hasMoreVoices && (
        <button
          type="button"
          onClick={() => { 
            if(user.tier_type === 'free'){
              openPlansModal('nr_voices');
              return;
            } else {
              setShowAllVoices(!showAllVoices);
            }
          }}
          className={clsx(
            'w-full py-2 text-sm font-medium rounded-size0 transition-all duration-200',
            'border border-grey-5 hover:border-grey-10',
            'focus:outline-none focus:ring-2 focus:ring-purple-100',
            showAllVoices
              ? 'text-grey-50 hover:text-grey-75'
              : 'text-purple-200 hover:text-purple-300 bg-purple-5 hover:bg-purple-10'
          )}
          disabled={disabled}
        >
          {showAllVoices ? 'Show Less' : `Show More (${activeVoices.length - 6} more)`}
        </button>
      )}

      {/* Custom styles for hiding scrollbars are added via CSS classes */}
    </div>
  );
};

export default VoiceSelector;