import { describe, expect, it, vi } from 'vitest';

import * as authService from '@/services/auth.service';

import resetPasswordUseCase from './resetPassword';

// Mock authService
vi.mock('@/services/auth.service', () => ({
  resetPassword: vi.fn()
}));

describe('resetPasswordUseCase', () => {
  it('should return an empty object on successful password reset', async () => {
    // Mock successful response from authService
    authService.resetPassword.mockResolvedValue({});

    const result = await resetPasswordUseCase({ password: 'newpassword123', token: 'reset_token' });

    expect(result).toEqual({});
    expect(authService.resetPassword).toHaveBeenCalledWith({
      password: 'newpassword123',
      token: 'reset_token'
    });
  });

  it('should return an error object if the API call fails', async () => {
    const error = new Error('Password reset failed');
    authService.resetPassword.mockRejectedValue(error);

    const result = await resetPasswordUseCase({ password: 'newpassword123', token: 'reset_token' });

    expect(result).toEqual(error);
  });
});
