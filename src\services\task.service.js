import http from './http';

/**
 * Fetches a task by its ID.
 *
 * @param {string} task_id - The ID of the task to retrieve.
 * @returns {Promise} - A promise that resolves with the task data.
 */
export const getTaskById = (task_id) => {
  return http.get(
    `${import.meta.env.VITE_APP_BASE_API}tasks/${task_id}`, // API endpoint with task ID
    {}, // No request payload needed for GET requests
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};
