import { useEffect, useState } from 'react';

import { breakpoints } from '../constants';

// Custom hook to check if screen width is above a certain breakpoint
function useIsAboveBreakpoint(breakpoint) {
  const mediaQuery = `(min-width: ${breakpoints[breakpoint]})`;
  const [isAbove, setIsAbove] = useState(window.matchMedia(mediaQuery).matches);

  useEffect(() => {
    const handleResize = () => {
      setIsAbove(window.matchMedia(mediaQuery).matches);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [mediaQuery]);

  return isAbove;
}

export default useIsAboveBreakpoint;
