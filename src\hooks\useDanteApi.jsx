import { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Custom hook to manage API calls and handle loading, response, and error states.
 *
 * @param {Function} serviceFunction - The API service function that will be called.
 * @param {...any} args - The arguments to pass to the service function.
 * @returns {Object} - An object containing `isLoading`, `error`, `response`, and `data`.
 */
const useDanteApi = (serviceFunction, dependencies = [], options = {}, ...args) => {
  const { skip = false } = options; // if skip is true, we don't fetch data. Is used on team management feature to avoid fetching data when the user doesn't have permission to see the data
  const [response, setResponse] = useState(null);
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Memorize the arguments to avoid unnecessary re-renders
  const memoriedData = useMemo(() => [...args], [...args]);

  const fetchData = useCallback(async () => {
    if (skip) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      // TODO: add tier check to call the function
      const result = await serviceFunction(...memoriedData);
      setResponse(result);
      setData(result?.data);
    } catch (err) {
      setError(err.message || 'Something went wrong');
    } finally {
      setIsLoading(false);
    }
  }, [skip, serviceFunction, memoriedData]);

  useEffect(() => {
    if (!skip) {
      fetchData();
    }
  }, [...dependencies, skip]);

  return {
    isLoading,
    loading: isLoading,
    pending: isLoading,
    error,
    response,
    data,
    refetch: fetchData,
  };
};

export default useDanteApi;
