import React from 'react';

import DBadge from '../../Global/DBadge';
import DButton from '../../Global/DButton';
import CopyIcon from '../../Global/Icons/CopyIcon';
import ShareIcon from '../../Global/Icons/ShareIcon';

const AvatarBlock = ({ image, name, avatarId, isDefault }) => {
  return (
    <div className=" flex flex-col rounded-size1 gap-size2  max-w-60">
      <div className="group rounded-size1 bg-gradient-to-b from-black to-purple-300 overflow-hidden relative">
        <img src={image} alt={`Avatar profile for ${name}`} />
        <button className="transition absolute text-white top-0 w-full h-full flex flex-col items-center justify-center invisible  group-hover:visible opacity-0 hover:opacity-100 bg-black/50">
          {isDefault && (
            <span className="bg-white/10 rounded-full px-size2 text-base">Preview Avatar</span>
          )}
          {!isDefault && (
            <span className="bg-white/10 rounded-full px-size2 text-base">Open Avatar</span>
          )}
        </button>
      </div>
      <div className="px-size3 flex justify-between items-start">
        <h1 className="text-xl">{name}</h1>
        {!isDefault && (
          <div className="flex gap-size0">
            <DButton variant="grey">
              <ShareIcon width="16" />
            </DButton>
            <DButton variant="grey">
              <CopyIcon width="16" />
            </DButton>
          </div>
        )}
        {isDefault && (
          <div className="flex gap-size0">
            <DBadge label="Default" />
          </div>
        )}
      </div>
    </div>
  );
};

export default AvatarBlock;
