const ExpandIcon = (props) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.64645 3.16272C9.84171 2.94576 10.1583 2.94576 10.3536 3.16272L13.8536 7.05161C14.0488 7.26856 14.0488 7.62032 13.8536 7.83728C13.6583 8.05424 13.3417 8.05424 13.1464 7.83728L10.5 4.89679L10.2923 4.67446C10.1342 4.50525 9.86584 4.50525 9.70773 4.67446L9.5 4.89679L6.85355 7.83728C6.65829 8.05424 6.34171 8.05424 6.14645 7.83728C5.95118 7.62032 5.95118 7.26856 6.14645 7.05161L9.64645 3.16272Z"
        fill="currentColor"
      />
      <path
        d="M9.64645 16.8373C9.84171 17.0542 10.1583 17.0542 10.3536 16.8373L13.8536 12.9484C14.0488 12.7314 14.0488 12.3797 13.8536 12.1627C13.6583 11.9458 13.3417 11.9458 13.1464 12.1627L10.5 15.1032L10.2923 15.3255C10.1342 15.4948 9.86584 15.4948 9.70773 15.3255L9.5 15.1032L6.85355 12.1627C6.65829 11.9458 6.34171 11.9458 6.14645 12.1627C5.95118 12.3797 5.95118 12.7314 6.14645 12.9484L9.64645 16.8373Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default ExpandIcon;
