import ChatbotBlock from '@/components/Chatbot/ChatbotBlock';
import ChatbotDashboard from '@/components/Chatbot/Dashboard';
import { listFakeChatbots } from '@/helpers/stories/generateListChatbots';
import { fn } from '@storybook/test';

import LayoutMobile from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Layout/LayoutMobile',
  subtitle: 'lalal',
  description: 'lalal',
  component: LayoutMobile,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'fullscreen'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {}
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  parameters: {
    viewport: {
      defaultViewport: 'iphone14promax'
    }
  },
  args: {
    title: 'Lorem ipsum',
    children: <ChatbotDashboard chatbots={listFakeChatbots} />
  }
};
