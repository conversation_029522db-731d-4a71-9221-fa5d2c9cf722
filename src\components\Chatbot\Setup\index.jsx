import { useEffect, useState } from 'react';

import DCheckbox from '../../Global/DCheckbox';
import DInput from '../../Global/DInput/DInput';
import DInputEditable from '../../Global/DInputEditable';
import DSwitch from '../../Global/DSwitch';
import AddIcon from '../../Global/Icons/AddIcon';
import featureCheck from '@/helpers/tier/featureCheck';
import useToast from '@/hooks/useToast';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { activateNewDesign } from '@/services/chatbot.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import DTooltip from '@/components/Global/DTooltip';
import InfoIcon from '@/components/Global/Icons/InfoIcon';

const Setup = ({ customizationData, updateCustomizationData }) => {
  const [openActivateNewDesign, setOpenActivateNewDesign] = useState(false);
  const [activateNewDesignLoading, setActivateNewDesignLoading] = useState(false);
  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);

  const [initialMessages, setInitialMessages] = useState(
    customizationData.initial_messages
  );
  const { addWarningToast } = useToast();
  const teamSelected = useTeamManagementStore((state) => state.selectedTeam?.id);

  const handleActivateNewDesign = async () => {
    try {
      setActivateNewDesignLoading(true);
      const response = await activateNewDesign(customizationData?.kb_id);
      if (response.status === 200) {
        setSelectedChatbot({
          ...selectedChatbot,
          knowledge_base: {
            ...selectedChatbot.knowledge_base,
            new_design_activated: true,
          },
        });
        setOpenActivateNewDesign(false);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setActivateNewDesignLoading(false);
    }
  };
  
  useEffect(() => {
    setInitialMessages(customizationData.initial_messages);
  }, [customizationData.initial_messages]);

  return (
    <div className="flex flex-col gap-size5 w-full h-full">
    {!selectedChatbot?.knowledge_base?.new_design_activated && (
      <div className="flex flex-col gap-size1 mb-size2">
        <div className="flex items-center gap-size0">
          <DSwitch
            label="Activate new design"
            onChange={() => setOpenActivateNewDesign(true)}
            id="activate-new-design"
          />
          <DTooltip content="By enabling the new design, your AI Chatbot will adopt a refreshed appearance and gain access to exciting new features. If you’d prefer to stick with the old design, you can continue using it until <b>31 December</b>. To keep the old design, make sure not to toggle this option on.">
            <InfoIcon className="text-grey-50 size-3 ml-1" />
          </DTooltip>
        </div>
        <p className="text-xs tracking-tight text-grey-50">
          Activate the new deisgn to upgrade your AI Chatbot’s look. The
          changes shown below will only take effect once your activate
          this setting.
        </p>
      </div>
    )}
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Welcome message</p>
        {initialMessages?.map((message, index) => (
          <DInputEditable
            key={index}
            value={message.content || ''}
            onChange={(e) => {
              setInitialMessages((prevMessages) => {
                const newMessages = [...prevMessages];
                newMessages[index] = {
                  ...newMessages[index],
                  content: e.target.value,
                };
                return newMessages;
              });
            }}
            onEdit={(value) => {
              updateCustomizationData('initial_messages', initialMessages);
            }}
            is_editable
            is_deletable
            onDelete={() => {
              const newMessages = [...customizationData?.initial_messages];
              newMessages.splice(index, 1);
              updateCustomizationData('initial_messages', newMessages);
            }}
          />
        ))}
        <button
          className="dbutton bg-grey-2 rounded-size2 px-size2 py-size1 flex gap-size0 items-center"
          onClick={() => {
            if (!teamSelected && !featureCheck('add_initial_message')) {
              return;
            }
            updateCustomizationData('initial_messages', [
              ...customizationData?.initial_messages,
              {
                  content: '',
                  role: 'assistant',
                  type: 'welcome_message',
                  status: 'completed'
                },
            ]);
          }}
        >
          <AddIcon className="size-5"/>
          <p className="text-xs font-regular tracking-tight mt-[1px]">
            Add another message
          </p>
        </button>
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">
          Input placeholder
        </p>
        <DInput
          value={customizationData?.input_placeholder_text || ''}
          onChange={(e) => {
            if (!teamSelected && !featureCheck('input_placeholder')) {
              return;
            }
            updateCustomizationData('input_placeholder_text', e.target.value);
          }}
        />
      </div>
      <div className="flex flex-col gap-size1">
        <div className=" flex items-center justify-between">
          <p className="text-base font-medium tracking-tight">
            Suggested prompts
          </p>
          <DSwitch
            checked={customizationData?.suggested_prompts_enabled}
            onChange={(value) => {
              if (!teamSelected && !featureCheck('suggested_prompts')) {
                return;
              }
              updateCustomizationData('suggested_prompts_enabled', value);
            }}
          />
        </div>
        <div
          className={`transition-all duration-300 flex flex-col gap-size1 ${
            customizationData?.suggested_prompts_enabled
              ? 'max-h-screen opacity-100'
              : 'max-h-0 opacity-0 overflow-hidden'
          }`}
        >
          {customizationData?.suggested_prompts_enabled && (
            <>
              {customizationData?.prompt_suggestions.length > 0 &&
                customizationData?.prompt_suggestions.map((prompt, index) => {
                  return (
                    <div className="flex gap-size0">
                      <DInputEditable
                        value={prompt.content || ''}
                        onChange={(e) => {
                          const newPrompts = [
                            ...customizationData?.prompt_suggestions,
                          ];
                          newPrompts[index] = {
                            ...newPrompts[index],
                            content: e.target.value,
                          };
                          updateCustomizationData(
                            'prompt_suggestions',
                            newPrompts
                          );
                        }}
                        onDelete={() => {
                          const newPrompts = [
                            ...customizationData?.prompt_suggestions,
                          ];
                          newPrompts.splice(index, 1);
                          updateCustomizationData(
                            'prompt_suggestions',
                            newPrompts
                          );
                        }}
                        is_deletable
                        outlined
                        is_editable
                        onEdit={(value) => {
                          const newPrompts = [
                            ...customizationData?.prompt_suggestions,
                          ];
                          newPrompts[index] = {
                            ...newPrompts[index],
                            content: value,
                          };
                          updateCustomizationData(
                            'prompt_suggestions',
                            newPrompts
                          );
                        }}
                        className="!pl-0 !min-h-8"
                      />
                    </div>
                  );
                })}
              <button
                className="dbutton bg-transparent rounded-size2 px-size2 py-size1 flex items-center gap-size0"
                onClick={() => {
                  updateCustomizationData('prompt_suggestions', [
                    ...customizationData?.prompt_suggestions,
                    {
                      type: 'suggestion',
                    },
                  ]);
                }}
              >
                <AddIcon className="size-5"/>
                <p className="text-xs font-regular tracking-tight mt-[2px]">
                  Add another prompt
                </p>
              </button>
              <DCheckbox
                label="Only show suggested prompts once"
                checked={customizationData?.show_prompt_messages}
                onChange={(value) => {
                  updateCustomizationData('show_prompt_messages', value);
                }}
              />
              <DCheckbox
                label="Show always the same prompts"
                checked={customizationData?.show_always_same_prompts}
                onChange={(value) => {
                  updateCustomizationData('show_always_same_prompts', value);
                }}
              />
            </>
          )}
        </div>
      </div>
      <div className="flex flex-col gap-size1">
        <div className=" flex items-center justify-between">
          <p className="text-base font-medium tracking-tight">Disclaimer</p>
          <DSwitch
            checked={customizationData?.disclaimer_enabled}
            onChange={(value) => {
              if (!teamSelected && !featureCheck('disclaimer')) {
                return;
              }
              updateCustomizationData('disclaimer_enabled', value);
            }}
          />
        </div>
        <div
          className={`transition-all duration-300 flex flex-col gap-size1 ${
            customizationData?.disclaimer_enabled
              ? 'max-h-screen opacity-100'
              : 'max-h-0 opacity-0 overflow-hidden'
          }`}
        >
          {customizationData?.disclaimer_enabled && (
            <DInput
              value={customizationData?.disclaimer_text}
              onChange={(e) => {
                if (!teamSelected && !featureCheck('disclaimer')) {
                  return;
                }
                updateCustomizationData('disclaimer_text', e.target.value);
              }}
            />
          )}
        </div>
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">
          Terms of use link
        </p>
        <DInput
          value={customizationData?.terms_of_use_link || 'https://www.dante-ai.com/terms-of-service'}
          onChange={(e) => {
            updateCustomizationData('terms_of_use_link', e.target.value);
          }}
        />
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">
          Privacy policy link
        </p>
        <DInput
          value={customizationData?.privacy_policy_link || 'https://www.dante-ai.com/privacy-policy'}
          onChange={(e) => {
            updateCustomizationData('privacy_policy_link', e.target.value);
          }}
        />
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">
          Additional options
        </p>
        {customizationData?.additional_options?.map((option, index) => (
          <DCheckbox
            key={index}
            label={option.label}
            checked={option.checked}
            onChange={(value) => {
              if (option.name === 'remove_watermark') {
                if (!teamSelected && !featureCheck('remove_powered_by_dante')) {
                  return;
                }
                const newOptions = [...customizationData?.additional_options];
                newOptions[index].checked = value;
                updateCustomizationData(option.name, value);
              } else if (option.name === 'show_microphone') {
                if (!teamSelected && !featureCheck('voice_to_text')) {
                  return;
                }
                const newOptions = [...customizationData?.additional_options];
                newOptions[index].checked = value;
                updateCustomizationData(option.name, value);
              } else if (option.name === 'show_email_details') {
                if (!teamSelected && !featureCheck('show_email_details')) {
                  return;
                }
                const newOptions = [...customizationData?.additional_options];
                newOptions[index].checked = value;
                updateCustomizationData(option.name, value);
              } else if (option.name === 'show_play_button') {
                if (!teamSelected && !featureCheck('show_play_button')) {
                  return;
                }
                const newOptions = [...customizationData?.additional_options];
                newOptions[index].checked = value;
                updateCustomizationData(option.name, value);
              } else if (option.name === 'show_welcome_message_as_tooltip') {
                if (!teamSelected && !featureCheck('show_welcome_message_as_tooltip')) {
                  return;
                }
                const newOptions = [...customizationData?.additional_options];
                newOptions[index].checked = value;
                updateCustomizationData(option.name, value);
              } else {
                const newOptions = [...customizationData?.additional_options];
                newOptions[index].checked = value;
                updateCustomizationData(option.name, value);
              }
            }}
          />
        ))}
      </div>
      <DConfirmationModal
        open={openActivateNewDesign}
        onClose={() => setOpenActivateNewDesign(false)}
        onConfirm={handleActivateNewDesign}
        title="Activate New Design"
        description="Are you sure you want to activate the new design? This action cannot be undone."
        confirmText="Activate"
        cancelText="Cancel"
        loading={activateNewDesignLoading}
      />
    </div>
  );
};

export default Setup;
