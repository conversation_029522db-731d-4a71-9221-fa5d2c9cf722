import React, { useState } from 'react';
import { Disclosure } from '@headlessui/react';
import ArrowDownIcon  from '@/components/Global/Icons/ArrowDownIcon';
import DBadge from '@/components/Global/DBadge';
import DSelect from '@/components/Global/DSelect';
import DTransition from '../Global/DTransition';
import AddOn from '../AddOn';
import DButton from '../Global/DButton';
import * as plansService from '@/services/plans.service';
import { STATUS } from '@/constants';
import { trackAddonSubscription } from '@/helpers/analytics';
import { useUserStore } from '@/stores/user/userStore';

const AddOnRow = ({ category, selectedMessages, setSelectedMessages, addons }) => {
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const user = useUserStore((state) => state.user);
    const handlePurchase = async (id) => {
        try{
            setLoading(true);
            let response;

            if(category.key === 'extra_messages'){
                response = await plansService.getCheckoutSessionExtraMessages(id);
            }else{
                response = await plansService.getCheckoutSessionAddon(id);
            }

            if(response.status === 200){
                trackAddonSubscription({
                    subscription_id: response.data.checkout_session.id,
                    user_id: user.id,
                    email: user.email,
                    addon_name: category.name,
                    addon_price: category.price,
                });
                setLoading(false);
                window.open(response.data.checkout_session.url, '_blank');
            }
        }catch(error){
            console.log(error);
            setLoading(false);
        }
    }

    return  (
        <React.Fragment>
            <div className="flex flex-col gap-size3">
                <div className="flex justify-between gap-size1">
                    <div className="flex gap-size1 items-center">
                    <p className="text-2xl font-medium tracking-tight">{category.name}</p>
                    <DBadge type={STATUS.ACTIVE} label="Add on" showIcon={false}/>
                    </div>
                    <div className="hidden md:flex gap-size1 items-center cursor-pointer" onClick={() => setOpen(!open)}>
                        <p>{open ? 'Close add-ons' : 'Explore add-ons'}</p>
                        <ArrowDownIcon className={`size-3 ${open ? 'rotate-180' : ''}`}/>
                    </div>
                </div>
                <div className="flex gap-size1">
                    {category.has_select ? (
                        <div className="w-full flex gap-size1 items-center">
                        <p className="text-lg text-white text-opacity-75">Add</p>
                        <DSelect 
                            listButtonClass="!bg-transparent !border-white !border-opacity-75 !text-white !text-opacity-75 !text-sm !py-size0 !h-auto !w-64"
                            options={category.options} 
                            value={selectedMessages}
                            onChange={(value) => setSelectedMessages(value)}
                        />
                        <p className="text-lg text-white text-opacity-75">
                            <span dangerouslySetInnerHTML={{ __html: category.description }}></span>
                            {selectedMessages ? <b> ${category.options.find(option => option.value === selectedMessages)?.price}</b> : ''}
                        </p>
                        </div>
                    ) : (
                        <p className="text-lg text-white text-opacity-75">
                            <span dangerouslySetInnerHTML={{ __html: category.description }}></span>
                            {!category?.no_checkout && <b> {category?.price}$</b>}
                        </p>
                    )}
                </div>
                <div className="w-[200px] bg-white/10 rounded-size0">
                    {category?.no_checkout ? (
                        <DButton variant="grey" fullWidth size="sm" className="bg-white/10 text-white/85 mt-auto" onClick={() => window.open('mailto:<EMAIL>', '_blank')}>
                            Contact us
                        </DButton>
                    ):(
                        <DButton variant="grey" fullWidth size="sm" className="bg-white opacity-85 !text-black"
                        onClick={() => {
                            handlePurchase(category.key === 'extra_messages' ? category.options.find(option => option.value === selectedMessages)?.id : category.id);
                        }}>
                            Purchase Add-on
                        </DButton>
                    )}
                </div>
                <div className="flex gap-size1 items-center cursor-pointer md:hidden mt-size2" onClick={() => setOpen(!open)}>
                        <p>{open ? 'Close add-ons' : 'Explore add-ons'}</p>
                        <ArrowDownIcon className={`size-3 ${open ? 'rotate-180' : ''}`}/>
                    </div>
            </div>
            <DTransition show={open}>
                 <div className="grid grid-cols-3 gap-size5 border-t border-[#272632] mt-size3 py-size3">

                    {addons?.map((addon, addonIndex) => (
                        <AddOn 
                            key={addonIndex} 
                            category={addon} 
                            selectedMessages={selectedMessages} 
                            setSelectedMessages={setSelectedMessages} 
                            handlePurchase={() => handlePurchase(addon.key === 'extra_messages' ? addon.options.find(option => option.value === selectedMessages)?.id : addon.id)}
                            loading={loading}
                        />
                    ))}
                </div>
            </DTransition>
        </React.Fragment>
    )
};

export default AddOnRow;
