import * as React from 'react';
const EditIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.3 3.826c-.289 0-.566.115-.77.32L4.656 13.02l-.514 2.055 2.056-.514 8.874-8.874a1.091 1.091 0 0 0-.771-1.86m-1.379-.288a1.95 1.95 0 0 1 2.759 2.758L6.72 15.254a.43.43 0 0 1-.2.113l-2.866.716a.43.43 0 0 1-.521-.521l.716-2.867a.43.43 0 0 1 .113-.2zm-3.35 12.128a.43.43 0 0 1 .43-.43h6.45a.43.43 0 1 1 0 .86H10a.43.43 0 0 1-.43-.43"
      fill="currentColor"
    />
  </svg>
);
export default EditIcon;
