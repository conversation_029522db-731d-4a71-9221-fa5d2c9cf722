import React, { useState, useRef, useEffect } from 'react';
import { voiceOptions, kbOptions } from './config';

// We need isStopButtonDisabled to accurately determine the audio state
const SettingsPanel = ({
  settings,
  setSettings,
  isStartButtonDisabled,
  isStopButtonDisabled,
}) => {
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false);
  const [currentlyPlayingId, setCurrentlyPlayingId] = useState(null);
  const [showVoiceDropdown, setShowVoiceDropdown] = useState(false);
  const [voiceInstructions, setVoiceInstructions] = useState(
    settings.voice_instructions || ''
  );
  const audioRef = useRef(null);
  const dropdownRef = useRef(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    // Skip if trying to change authToken
    if (name === 'authToken') return;

    setSettings((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Audio is active when Start is disabled AND Stop is enabled
  // This ensures we only consider audio active during playback, not during loading
  const isAudioActive = isStartButtonDisabled && !isStopButtonDisabled;

  // Get the current selected voice option
  const selectedVoice = voiceOptions.find(
    (voice) => voice.id === settings.voiceId
  );

  // Check if the selected voice is an OpenAI voice
  const isOpenAIVoice = selectedVoice?.provider === 'OpenAI';

  // Handle voice instructions change
  const handleVoiceInstructionsChange = (e) => {
    const value = e.target.value;
    setVoiceInstructions(value);
    setSettings((prev) => ({
      ...prev,
      voice_instructions: value,
    }));
  };

  // Reset voice instructions when switching away from OpenAI voices
  useEffect(() => {
    if (!isOpenAIVoice && settings.voice_instructions) {
      setSettings((prev) => {
        const newSettings = { ...prev };
        delete newSettings.voice_instructions;
        return newSettings;
      });
      setVoiceInstructions('');
    }
  }, [isOpenAIVoice, setSettings]);

  // Handle playing the preview for a specific voice
  const playPreview = (voiceId) => {
    const voice = voiceOptions.find((v) => v.id === voiceId);
    if (!voice || !voice.preview_url) return;

    if (audioRef.current) {
      // If we're already playing this voice, stop it
      if (currentlyPlayingId === voiceId) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        setIsPreviewPlaying(false);
        setCurrentlyPlayingId(null);
        return;
      }

      // Otherwise, stop any current audio and play the new one
      audioRef.current.pause();
      audioRef.current.currentTime = 0;

      // Set new source
      audioRef.current.src = voice.preview_url;

      // Play the preview
      audioRef.current
        .play()
        .then(() => {
          setIsPreviewPlaying(true);
          setCurrentlyPlayingId(voiceId);
        })
        .catch((error) => {
          console.error('Error playing preview:', error);
          setIsPreviewPlaying(false);
          setCurrentlyPlayingId(null);
        });
    }
  };

  // Handle when audio ends
  const handleAudioEnded = () => {
    setIsPreviewPlaying(false);
    setCurrentlyPlayingId(null);
  };

  // Select a voice
  const selectVoice = (voiceId) => {
    setSettings((prev) => ({
      ...prev,
      voiceId,
    }));
    setShowVoiceDropdown(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowVoiceDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="w-full">
      {/* Hidden audio element for previews */}
      <audio
        ref={audioRef}
        onEnded={handleAudioEnded}
        onError={() => {
          setIsPreviewPlaying(false);
          setCurrentlyPlayingId(null);
        }}
      />

      {isAudioActive && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md text-sm text-yellow-700">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            Settings cannot be changed while audio is active. Stop the audio to
            modify settings.
          </div>
        </div>
      )}

      <div className="mb-4">
        <label
          className="block text-gray-700 text-sm font-medium mb-2"
          htmlFor="voiceId"
        >
          Voice
        </label>
        <div className="relative" ref={dropdownRef}>
          {/* Custom dropdown trigger */}
          <button
            type="button"
            onClick={() =>
              !isAudioActive && setShowVoiceDropdown(!showVoiceDropdown)
            }
            disabled={isAudioActive}
            className="relative w-full bg-gray-50 border border-gray-300 text-gray-700 py-2 px-3 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 text-left flex justify-between items-center"
          >
            <span>
              {selectedVoice
                ? `${selectedVoice.provider} - ${selectedVoice.name}`
                : 'Select a voice'}
            </span>
            <svg
              className="fill-current h-4 w-4 ml-2"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
            >
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
            </svg>
          </button>

          {/* Custom dropdown */}
          {showVoiceDropdown && (
            <div className="absolute z-10 mt-1 w-full rounded-md bg-white shadow-lg max-h-80 overflow-y-auto border border-gray-200">
              {/* OpenAI voices */}
              <div className="py-2 px-3 text-sm font-bold text-gray-700 bg-gray-100 border-b border-gray-300">
                OpenAI
              </div>
              {voiceOptions
                .filter((voice) => voice.provider === 'OpenAI')
                .map((voice) => (
                  <div
                    key={voice.id}
                    className="flex items-center justify-between py-2 px-3 pl-6 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                  >
                    <div
                      className="flex-grow"
                      onClick={() => selectVoice(voice.id)}
                    >
                      <span
                        className={`text-xs ${
                          settings.voiceId === voice.id ? 'font-semibold' : ''
                        }`}
                      >
                        OpenAI - {voice.name}
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        playPreview(voice.id);
                      }}
                      disabled={
                        isAudioActive ||
                        (!isPreviewPlaying && !voice.preview_url)
                      }
                      className="ml-2 inline-flex items-center p-1 text-sm rounded-full text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title={
                        currentlyPlayingId === voice.id
                          ? 'Stop preview'
                          : 'Preview voice'
                      }
                    >
                      {currentlyPlayingId === voice.id ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </button>
                  </div>
                ))}

              {/* ElevenLabs voices */}
              <div className="py-2 px-3 text-sm font-bold text-gray-700 bg-gray-100 border-b border-gray-300">
                ElevenLabs
              </div>
              {voiceOptions
                .filter((voice) => voice.provider === 'ElevenLabs')
                .map((voice) => (
                  <div
                    key={voice.id}
                    className="flex items-center justify-between py-2 px-3 pl-6 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                  >
                    <div
                      className="flex-grow"
                      onClick={() => selectVoice(voice.id)}
                    >
                      <span
                        className={`text-xs ${
                          settings.voiceId === voice.id ? 'font-semibold' : ''
                        }`}
                      >
                        ElevenLabs - {voice.name}
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        playPreview(voice.id);
                      }}
                      disabled={
                        isAudioActive ||
                        (!isPreviewPlaying && !voice.preview_url)
                      }
                      className="ml-2 inline-flex items-center p-1 text-sm rounded-full text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title={
                        currentlyPlayingId === voice.id
                          ? 'Stop preview'
                          : 'Preview voice'
                      }
                    >
                      {currentlyPlayingId === voice.id ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </button>
                  </div>
                ))}

              {/* Cartesia voices */}
              <div className="py-2 px-3 text-sm font-bold text-gray-700 bg-gray-100 border-b border-gray-300">
                Cartesia
              </div>
              {voiceOptions
                .filter((voice) => voice.provider === 'Cartesia')
                .map((voice) => (
                  <div
                    key={voice.id}
                    className="flex items-center justify-between py-2 px-3 pl-6 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                  >
                    <div
                      className="flex-grow"
                      onClick={() => selectVoice(voice.id)}
                    >
                      <span
                        className={`text-xs ${
                          settings.voiceId === voice.id ? 'font-semibold' : ''
                        }`}
                      >
                        Cartesia - {voice.name}
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        playPreview(voice.id);
                      }}
                      disabled={
                        isAudioActive ||
                        (!isPreviewPlaying && !voice.preview_url)
                      }
                      className="ml-2 inline-flex items-center p-1 text-sm rounded-full text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title={
                        currentlyPlayingId === voice.id
                          ? 'Stop preview'
                          : 'Preview voice'
                      }
                    >
                      {currentlyPlayingId === voice.id ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </button>
                  </div>
                ))}
            </div>
          )}

          {/* Hidden select for form handling */}
          <select
            id="voiceId"
            name="voiceId"
            value={settings.voiceId}
            onChange={handleChange}
            disabled={isAudioActive}
            className="sr-only"
            aria-hidden="true"
          >
            <optgroup label="OpenAI">
              {voiceOptions
                .filter((voice) => voice.provider === 'OpenAI')
                .map((voice) => (
                  <option key={voice.id} value={voice.id}>
                    {voice.name}
                  </option>
                ))}
            </optgroup>
            <optgroup label="ElevenLabs">
              {voiceOptions
                .filter((voice) => voice.provider === 'ElevenLabs')
                .map((voice) => (
                  <option key={voice.id} value={voice.id}>
                    {voice.name}
                  </option>
                ))}
            </optgroup>
            <optgroup label="Cartesia">
              {voiceOptions
                .filter((voice) => voice.provider === 'Cartesia')
                .map((voice) => (
                  <option key={voice.id} value={voice.id}>
                    {voice.name}
                  </option>
                ))}
            </optgroup>
          </select>
        </div>
      </div>

      {/* Voice Instructions field - only shown for OpenAI voices */}
      {isOpenAIVoice && (
        <div className="mb-4">
          <label
            className="block text-gray-700 text-sm font-medium mb-2"
            htmlFor="voiceInstructions"
          >
            Voice Instructions{' '}
            <span className="text-xs text-gray-500">(OpenAI only)</span>
          </label>
          <textarea
            id="voiceInstructions"
            name="voiceInstructions"
            value={voiceInstructions}
            onChange={handleVoiceInstructionsChange}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            disabled={isAudioActive}
            className="block w-full bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 text-min-safe-input"
            rows="2"
            placeholder="Optional instructions for voice style, emotion, etc."
          ></textarea>
          <p className="mt-1 text-xs text-gray-500">
            Customize how the voice speaks by providing style instructions,
            emotions, or other context.
          </p>
        </div>
      )}

      <div className="mb-4">
        <label
          className="block text-gray-700 text-sm font-medium mb-2"
          htmlFor="kbId"
        >
          Knowledge Base
        </label>
        {/* Custom select wrapper */}
        <div className="relative">
          {/* Hide browser default arrow with webkit-appearance */}
          <select
            id="kbId"
            name="kbId"
            value={settings.kbId}
            onChange={handleChange}
            className="block w-full bg-gray-50 border border-gray-300 text-gray-700 py-2 px-3 pr-8 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60"
            style={{
              WebkitAppearance: 'none',
              MozAppearance: 'none',
              appearance: 'none',
            }}
            disabled={isAudioActive}
          >
            {kbOptions.map((kb) => (
              <option key={kb.id} value={kb.id}>
                {kb.name}
              </option>
            ))}
          </select>
          {/* Custom arrow icon */}
          <div
            className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700"
            style={{ pointerEvents: 'none' }}
          >
            <svg
              className="fill-current h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
            >
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
            </svg>
          </div>
        </div>
      </div>

      <div>
        <label
          className="block text-gray-700 text-sm font-medium mb-2"
          htmlFor="initialMessage"
        >
          Initial Message
        </label>
        <textarea
          id="initialMessage"
          name="initialMessage"
          value={settings.initialMessage}
          onChange={handleChange}
          disabled={isAudioActive}
          className="block w-full bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 text-min-safe-input"
          rows="3"
          placeholder="Enter initial message (optional)"
        ></textarea>
      </div>

      {/* Authentication Token field has been removed */}
    </div>
  );
};

export default SettingsPanel;
