import { useState } from 'react';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import DCheckbox from '../DCheckbox';

const DTable = ({
  columns,
  data,
  className,
  hiddenHeader = false,
  withCheckboxes = false, // Optional checkboxes
  onRowSelect = () => {},
  selectedRows = [],
  ...props
}) => {
  const isAboveSm = useIsAboveBreakpoint('sm');
  const [localSelectedRows, setLocalSelectedRows] = useState(new Set(selectedRows));

  const handleCheckboxChange = (rowIndex) => {
    const updatedSelectedRows = new Set(localSelectedRows);
    if (updatedSelectedRows.has(rowIndex)) {
      updatedSelectedRows.delete(rowIndex);
    } else {
      updatedSelectedRows.add(rowIndex);
    }
    setLocalSelectedRows(updatedSelectedRows);
    onRowSelect([...updatedSelectedRows]);
  };

  if (isAboveSm) {
    return (
      <table
        className={`w-full table-auto text-left ${className}`}
        {...props}
      >
        {!hiddenHeader && (
          <thead className="bg-grey-5">
            <tr>
              {withCheckboxes && (
                <th className="px-size1 py-size2 text-lg font-regular tracking-tight w-10">
                  {/* Checkbox for selecting all */}
                  <DCheckbox
                    onChange={(checked) => {
                      const allSelected = checked;
                      const newSelectedRows = allSelected
                        ? new Set(data.map((_, index) => index))
                        : new Set();
                      setLocalSelectedRows(newSelectedRows);
                      onRowSelect([...newSelectedRows]);
                    }}
                    checked={localSelectedRows.size === data.length && data.length > 0}
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  className={`px-size1 py-size2 text-lg font-regular tracking-tighte ${
                    column.minWidth ? column.minWidth : 'min-w-10'
                  }`}
                  key={column.key}
                >
                  {column.showName ? column.label : ''}
                </th>
              ))}
            </tr>
          </thead>
        )}
        <tbody>
          {data.map((row, index) => (
            <tr key={index} className="h-14">
              {withCheckboxes && (
                <td className="px-size1 py-size2 text-center w-10">
                  {/* Checkbox for each row */}
                  <DCheckbox
                    onChange={() => handleCheckboxChange(index)}
                    checked={localSelectedRows.has(index)}
                  />
                </td>
              )}
              {columns.map((column) => (
                <td
                  className={`px-size1 py-size2 ${
                    column.minWidth ? column.minWidth : 'min-w-10'
                  }`}
                  key={column.key}
                >
                  <div className="flex items-center gap-size1">
                    {column.key === 'member' && (
                      <img src={row.icon} alt="member" className="w-8 h-8 rounded-full" />
                    )}
                    {row[column.key]}
                  </div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    );
  } else {
    return (
      <div>
        {data.map((row, index) => (
          <div key={index} className="flex flex-col items-center gap-size1 w-full">
            <header className="bg-grey-5 border-b border-grey-5 p-size1 flex items-center justify-between w-full">
              <div className="flex items-center gap-size1">
                {withCheckboxes && (
                  <input
                    type="checkbox"
                    onChange={() => handleCheckboxChange(index)}
                    checked={localSelectedRows.has(index)}
                    className="mr-size1"
                  />
                )}
                <img src={row.icon} alt="member" className="w-8 h-8 rounded-full" />
                <div className="flex flex-col gap-size0">
                  <span className="text-sm font-medium tracking-tight">{row.member}</span>
                  <span className="text-sm font-regular tracking-tight">{row.email}</span>
                </div>
              </div>
              {row.actions}
            </header>
            <div className="flex gap-size1 w-full py-size1">
              {columns.map(
                (column, colIndex) =>
                  column.mobile && (
                    <div key={colIndex} className="flex flex-col gap-size1 p-size1 w-full">
                      <span className="text-sm font-medium tracking-tight text-grey-50">
                        {column.label}
                      </span>
                      <span className="text-sm font-regular tracking-tight">{row[column.key]}</span>
                    </div>
                  )
              )}
            </div>
          </div>
        ))}
      </div>
    );
  }
};

export default DTable;