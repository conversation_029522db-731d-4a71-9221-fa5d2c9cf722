import { STATUS } from '@/constants';
import useToastStore from '@/stores/toast/toastStore';
import toast from 'react-hot-toast';

const useToast = () => {
  const addToast = useToastStore((state) => state.addToast);
  const removeToast = useToastStore((state) => state.removeToast);
  
  // Common toast style options
  const toastOptions = {
    duration: 4000,
    position: 'top-center',
    className: 'toast-with-indicator',
    style: {
      background: 'rgba(var(--color-white), 1)',
      color: 'rgba(var(--color-dark-900), 0.9)', // Dark text for contrast with white background
      padding: '10px 16px',
      borderRadius: '6px',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
      maxWidth: '320px',
      textAlign: 'center',
      position: 'relative', // Required for the indicator positioning
      overflow: 'hidden', // Keep the indicator inside the toast
      border: '1px solid rgba(var(--color-purple-300), 0.2)', // Subtle border to match the indicator
    },
    // Remove default icons
    icon: null
  };
  
  // Add a global style for the progress bar animation if it doesn't exist
  const ensureAnimationStyle = () => {
    if (!document.getElementById('toast-animation-style')) {
      const style = document.createElement('style');
      style.id = 'toast-animation-style';
      style.textContent = `
        @keyframes shrinkToastBar {
          from { transform: scaleX(1); }
          to { transform: scaleX(0); }
        }
        
        .toast-progress-indicator {
          position: absolute;
          bottom: 0;
          left: 0;
          height: 3px;
          background: rgba(var(--color-purple-300), 1);
          width: 100%;
          transform-origin: left;
        }
      `;
      document.head.appendChild(style);
    }
  };
  
  const addSuccessToast = (toastData) => {
    addToast({ ...toastData, type: STATUS.SUCCESS });
    ensureAnimationStyle();
    
    // Create toast with custom render function to include progress bar
    toast.custom((t) => (
      <div
        className={`${t.visible ? 'animate-enter' : 'animate-leave'} toast-with-indicator`}
        style={{
          ...toastOptions.style,
          opacity: t.visible ? 1 : 0,
        }}
      >
        {toastData.message}
        <div 
          className="toast-progress-indicator"
          style={{
            animation: `shrinkToastBar ${toastOptions.duration}ms linear forwards`
          }}
        />
      </div>
    ), { duration: toastOptions.duration, position: toastOptions.position });
  };

  const addErrorToast = (toastData) => {
    addToast({ ...toastData, type: STATUS.ERROR });
    ensureAnimationStyle();
    
    toast.custom((t) => (
      <div
        className={`${t.visible ? 'animate-enter' : 'animate-leave'} toast-with-indicator`}
        style={{
          ...toastOptions.style,
          opacity: t.visible ? 1 : 0,
        }}
      >
        {toastData.message}
        <div 
          className="toast-progress-indicator"
          style={{
            animation: `shrinkToastBar ${toastOptions.duration}ms linear forwards`
          }}
        />
      </div>
    ), { duration: toastOptions.duration, position: toastOptions.position });
  };

  const addWarningToast = (toastData) => {
    addToast({ ...toastData, type: STATUS.WARNING });
    ensureAnimationStyle();
    
    toast.custom((t) => (
      <div
        className={`${t.visible ? 'animate-enter' : 'animate-leave'} toast-with-indicator`}
        style={{
          ...toastOptions.style,
          opacity: t.visible ? 1 : 0,
        }}
      >
        {toastData.message}
        <div 
          className="toast-progress-indicator"
          style={{
            animation: `shrinkToastBar ${toastOptions.duration}ms linear forwards`
          }}
        />
      </div>
    ), { duration: toastOptions.duration, position: toastOptions.position });
  };

  return { addSuccessToast, addErrorToast, addWarningToast, removeToast };
};

export default useToast;
