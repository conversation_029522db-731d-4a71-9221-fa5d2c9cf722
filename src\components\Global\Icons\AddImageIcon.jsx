import * as React from 'react';
const AddImageIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.433 3c.25 0 .452.21.452.47v1.605h1.545a.46.46 0 0 1 .451.469.46.46 0 0 1-.451.47h-1.545v1.605a.46.46 0 0 1-.452.469.46.46 0 0 1-.452-.47V6.014h-1.545a.46.46 0 0 1-.451-.469c0-.26.202-.47.451-.47h1.545V3.47c0-.259.203-.469.452-.469M9.774 8.088a2.17 2.17 0 0 0-1.563.673 2.34 2.34 0 0 0-.647 1.624c0 .61.233 1.193.647 1.624.415.43.977.673 1.563.673a2.17 2.17 0 0 0 1.563-.673c.415-.43.648-1.015.648-1.624s-.233-1.194-.648-1.624a2.17 2.17 0 0 0-1.563-.673m-2.202.009a3.06 3.06 0 0 1 2.202-.948c.826 0 1.618.341 2.202.948s.912 1.43.912 2.288-.328 1.681-.912 2.288a3.06 3.06 0 0 1-2.202.947c-.826 0-1.618-.34-2.202-.947a3.3 3.3 0 0 1-.912-2.288 3.3 3.3 0 0 1 .912-2.288M6.162 3.86C4.414 3.86 3 5.331 3 7.145v6.57C3 15.53 4.415 17 6.161 17h7.678C15.585 17 17 15.53 17 13.715V9.961a.46.46 0 0 0-.452-.47.46.46 0 0 0-.451.47v3.754c0 1.296-1.011 2.346-2.258 2.346H6.16c-1.247 0-2.258-1.05-2.258-2.346v-6.57C3.903 5.85 4.914 4.8 6.161 4.8h4.065a.46.46 0 0 0 .451-.47.46.46 0 0 0-.451-.469z"
      fill="currentColor"
    />
  </svg>
);
export default AddImageIcon;
