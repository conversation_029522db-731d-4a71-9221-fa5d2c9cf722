import { describe, expect, it, vi } from 'vitest';

import * as authService from '@/services/auth.service';
import { useUserStore } from '@/stores/user/userStore';

import refreshAccessToken from './refreshAccessToken';

// Mock the functions in useUserStore and authService
vi.mock('@/stores/user/userStore', () => ({
  useUserStore: {
    getState: vi.fn()
  }
}));

vi.mock('@/services/auth.service', () => ({
  refreshToken: vi.fn()
}));

describe('refreshAccessToken', () => {
  it('should update access token when refresh is successful', async () => {
    const mockUpdateAccessToken = vi.fn();
    const mockRemoveAuthDetail = vi.fn();

    // Mock the user store's functions
    useUserStore.getState.mockReturnValue({
      updateAccessToken: mockUpdateAccessToken,
      removeAuthDetail: mockRemoveAuthDetail
    });

    // Mock the authService response
    authService.refreshToken.mockResolvedValue({
      data: { access_token: 'new_access_token' }
    });

    await refreshAccessToken();

    expect(mockUpdateAccessToken).toHaveBeenCalledWith('new_access_token');
    expect(mockRemoveAuthDetail).not.toHaveBeenCalled();
  });

  it('should call removeAuthDetail on refresh failure', async () => {
    const mockUpdateAccessToken = vi.fn();
    const mockRemoveAuthDetail = vi.fn();

    // Mock the user store's functions
    useUserStore.getState.mockReturnValue({
      updateAccessToken: mockUpdateAccessToken,
      removeAuthDetail: mockRemoveAuthDetail
    });

    // Mock the authService to throw an error
    authService.refreshToken.mockRejectedValue(new Error('Refresh token failed'));

    await refreshAccessToken();

    expect(mockUpdateAccessToken).not.toHaveBeenCalled();
    expect(mockRemoveAuthDetail).toHaveBeenCalled();
  });

  it('should call removeAuthDetail if access token is invalid', async () => {
    const mockUpdateAccessToken = vi.fn();
    const mockRemoveAuthDetail = vi.fn();

    // Mock the user store's functions
    useUserStore.getState.mockReturnValue({
      updateAccessToken: mockUpdateAccessToken,
      removeAuthDetail: mockRemoveAuthDetail
    });

    // Mock the authService response with missing access token
    authService.refreshToken.mockResolvedValue({
      data: { access_token: null }
    });

    await refreshAccessToken();

    expect(mockUpdateAccessToken).not.toHaveBeenCalled();
    expect(mockRemoveAuthDetail).toHaveBeenCalled();
  });
});
