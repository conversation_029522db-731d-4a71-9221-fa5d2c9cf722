import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const initialState = {
  currentStep: null,
};

const useLiveAgentStore = create(
  persist(
    (set) => ({
      ...initialState,
      setCurrentStep: (currentStep) => set({ currentStep }),
      reset: () => set({ ...initialState })
    }),
    {
      name: 'live-agent',
      storage: createJSONStorage(() => localStorage)
    }
  )
);

export default useLiveAgentStore;