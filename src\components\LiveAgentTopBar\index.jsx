import { useNavigate, useParams } from 'react-router-dom';
import EyeIcon from '../Global/Icons/EyeIcon';
import GraphIcon from '../Global/Icons/GraphIcon';
import SettingsIcon from '../Global/Icons/SettingsIcon';
import ZapIcon from '../Global/Icons/ZapIcon';
import { LiveAgentStepEnum } from './LiveagentStepEnum';
import DNavLink from '../Global/DNavLink';
import useLiveAgentStore from '@/stores/liveAgent/liveAgentStore';

const LiveAgentTopBar = () => {
  const navigate = useNavigate();
  const setCurrentStep = useLiveAgentStore((state) => state.setCurrentStep);

  const { organization_id } = useParams();

  const liveagentNavItems = [
    {
      id: LiveAgentStepEnum.OVERVIEW,
      label: 'Overview',
      icon: <EyeIcon />,
      link: '',
    },
    {
      id: LiveAgentStepEnum.INSIGHTS,
      label: 'Insights',
      icon: <GraphIcon />,
      link: '/insights',
    },
    {
      id: LiveAgentStepEnum.QUICK_RESPONSES,
      label: 'Quick Responses',
      icon: <ZapIcon />,
      link: '/quick-responses',
    },
    {
      id: LiveAgentStepEnum.SETTINGS,
      label: 'Settings',
      icon: <SettingsIcon />,
      link: '/settings',
    },
  ];
  return (
    <div className="flex gap-size1 overflow-x-auto no-scrollbar w-full md:w-auto">
      {liveagentNavItems.map((navItem) => {
        const isActive =
          (navItem.id === LiveAgentStepEnum.OVERVIEW &&
            location.pathname === `/human-handover/${organization_id}`) ||
          (navItem.id === LiveAgentStepEnum.INSIGHTS &&
            location.pathname ===
              `/human-handover/${organization_id}/insights`) ||
          (navItem.id === LiveAgentStepEnum.QUICK_RESPONSES &&
            location.pathname ===
              `/human-handover/${organization_id}/quick-responses`) ||
          (navItem.id === LiveAgentStepEnum.SETTINGS &&
            location.pathname ===
              `/human-handover/${organization_id}/settings`);
        return (
          <DNavLink
            key={navItem.label}
            label={navItem.label}
            icon={navItem.icon}
            iconPlacement={'pre'}
            active={isActive}
            page="LiveAgentTopBar"
            onClick={() => {
              liveagentNavItems.forEach((item) => {
                item.active = false;
              });
              navItem.active = true;
              setCurrentStep(navItem.id);
              navigate(`/human-handover/${organization_id}${navItem.link}`);
            }}
          />
        );
      })}
    </div>
  );
};

export default LiveAgentTopBar;
