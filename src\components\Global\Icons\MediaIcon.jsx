import * as React from 'react';
const MedialIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.5 3A1.5 1.5 0 0 0 3 4.5v11A1.5 1.5 0 0 0 4.5 17h11a1.5 1.5 0 0 0 1.5-1.5v-11A1.5 1.5 0 0 0 15.5 3zM4 4.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 .5.5v11a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5zm7.512 2.993a.5.5 0 0 0-.835-.034L9.168 9.571l-1.035-.777a.5.5 0 0 0-.739.161l-2.166 3.972a.5.5 0 0 0 .439.74h8.666a.5.5 0 0 0 .43-.758zm-1.827 3.075 1.362-1.907 2.403 4.006H6.51L7.996 9.94l.982.737a.5.5 0 0 0 .707-.11"
      fill="currentColor"
    />
  </svg>
);
export default MedialIcon;
