import React from 'react';
import AddOnRow from '@/components/AddOnRow';
import DSwitch from '@/components/Global/DSwitch';
import CheckmarkIcon from '@/components/Global/Icons/CheckmarkIcon';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import DButton from '@/components/Global/DButton';
import { useUserStore } from '@/stores/user/userStore';
import { EMAILS } from '@/constants';
import DBadge from '../Global/DBadge';
import clsx from 'clsx';

const DesktopPlans = ({
  plans,
  tiers,
  period,
  setPeriod,
  selectedMessages,
  setSelectedMessages,
  onUpgrade,
  onDowngrade,
}) => {
  const user = useUserStore((state) => state.user);
  // Calculate widths
  const firstColumnWidth = '30%';
  const otherColumnWidth = `${70 / (tiers?.length || 1)}%`;

  return (
    <div className="overflow-x-auto w-full">
      {/* Header Table */}
      <table className="hidden md:table table-auto w-full border-collapse">
        <thead className="sticky top-0 bg-white z-10">
          <tr className="bg-purple-5 border border-gray-2">
            <th
              className="border-r border-gray-2 px-4 py-2 text-left"
              style={{ width: firstColumnWidth }}
            >
              <div className="flex gap-size1">
                <DSwitch
                  checked={period === 'yearly'}
                  onChange={(value) => setPeriod(value ? 'yearly' : 'monthly')}
                  label="Annual"
                />
                <div className="flex p-size1 items-center bg-purple-10 text-purple-300 rounded-size1">
                  <p className="text-sm tracking-tight">2 Months Free</p>
                </div>
              </div>
            </th>
            {tiers?.map((tier, tierIndex) => (
              <th
                key={tierIndex}
                className={clsx(
                  'border-r border-gray-2 px-1 py-2 text-center',
                  'align-center'
                )}
                style={{ width: otherColumnWidth }}
              >
                <div className="flex flex-col gap-size2">
                  <p className="text-base font-medium tracking-tight">
                    <span className="text-grey-50">{tier.name}</span>{' '}
                    {tier.name !== 'Enterprise' && `$${tier.price}`}{' '}
                    <span className="text-grey-20">
                      /per {period === 'yearly' ? 'year' : 'month'}
                    </span>
                  </p>
                  {user?.tier_rank === tier.rank && !user?.tier_on_trial && (
                    <DButton variant="light" size="sm" fullWidth className='!mt-auto'>
                      Current Plan
                    </DButton>
                  )}
                  {tier?.name === 'Enterprise' && !user?.tier_on_trial && (
                    <DButton
                      variant="dark"
                      size="sm"
                      fullWidth
                      onClick={() => {
                        window.open(`mailto:${EMAILS.SALES}`, '_blank');
                      }}
                    >
                      Contact us
                    </DButton>
                  )}
                  {user?.tier_rank > tier.rank &&
                    tier.name !== 'Enterprise' && !user?.tier_on_trial && (
                      <DButton
                        variant="grey"
                        size="sm"
                        fullWidth
                        onClick={() => onDowngrade(tier)}
                      >
                        Downgrade
                      </DButton>
                    )}
                  {(user?.tier_rank || 0) < tier.rank &&
                    tier.name !== 'Enterprise' && !user?.tier_on_trial && (
                      <DButton
                        variant="green"
                        size="sm"
                        fullWidth
                        onClick={() => onUpgrade(tier.id, tier.name)}
                      >
                        Upgrade
                      </DButton>
                    )}
                  {tier?.name !== 'Enterprise' && user?.tier_on_trial && (
                    <DButton
                      variant="green"
                      size="sm"
                      fullWidth
                      onClick={() => onUpgrade(tier.id, tier.name)}
                    >
                      {user?.tier_rank === tier.rank && user?.tier_on_trial ? 'Activate Now' : 'Select Plan'} {user?.tier_rank === tier.rank && user?.tier_on_trial && <span className='text-[10px]'>(Current Trial)</span>}
                    </DButton>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
      </table>

      {/* Scrollable Body */}
      <div
        className="overflow-y-auto no-scrollbar"
        style={{ maxHeight: 'calc(100vh - 320px)' }}
      >
        <table className="hidden md:table table-auto w-full border-collapse">
          <tbody>
            {plans?.map((category, categoryIndex) =>
              category?.type && category.type === 'addon' ? (
                category?.show_on_table && (
                  <React.Fragment key={categoryIndex}>
                    <tr>
                      <td colSpan="5" className="py-size5">
                        <div className="bg-black rounded-size2 p-size3 text-white">
                          <AddOnRow
                            category={category}
                            selectedMessages={selectedMessages}
                            setSelectedMessages={setSelectedMessages}
                            addons={plans.filter(
                              (plan) => plan.type === 'addon'
                            )}
                          />
                        </div>
                      </td>
                    </tr>
                  </React.Fragment>
                )
              ) : (
                <React.Fragment key={categoryIndex}>
                  {/* Category Header */}
                  <tr>
                    <td
                      colSpan="5"
                      className="text-lg font-medium text-left px-1 py-3"
                    >
                      {category.name}
                    </td>
                  </tr>

                  {/* Features */}
                  {category?.features?.map((feature, featureIndex) => (
                    <tr key={featureIndex} className="hover:bg-grey-2">
                      <td
                        className="border-b border-grey-5 px-1 py-2 text-left text-grey-50 text-sm tracking-tight"
                        style={{ width: firstColumnWidth }}
                      >
                        {feature.title}
                      </td>
                      {feature.value.map((tierVal, tIndex) => (
                        <td
                          key={tIndex}
                          className={`relative px-1 py-3 text-center ${user}`}
                          style={{ width: otherColumnWidth }}
                        >
                          <div className="flex justify-center items-center gap-size1">
                            {typeof tierVal.value === 'boolean' ? (
                              tierVal.value ? (
                                <CheckmarkIcon />
                              ) : (
                                <CloseIcon className="text-grey-20" />
                              )
                            ) : (
                              <p className="text-grey-50 text-sm tracking-tight">
                                {tierVal.value}
                              </p>
                            )}
                          </div>
                          <div className="absolute bottom-0 left-1/2 w-[92%] border-b border-grey-5 transform -translate-x-1/2"></div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </React.Fragment>
              )
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DesktopPlans;
