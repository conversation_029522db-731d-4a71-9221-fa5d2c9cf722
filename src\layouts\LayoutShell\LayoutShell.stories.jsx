import AvatarDashboard from '@/components/Avatar/Dashboard';
import Chat from '@/components/Chatbot/Chat';
import ChatbotBlock from '@/components/Chatbot/ChatbotBlock';
import ChatbotDashboard from '@/components/Chatbot/Dashboard';
import { DANTE_THEME_COLOR_CHAT } from '@/constants';
import {
  fakerInitialMessages,
  fakerLoadingConversation,
  fakerLongConversation,
  fakerShortConversation,
  fakerSuggestionsPrompts
} from '@/helpers/stories/generateChatMessages';
import { listFakeChatbots } from '@/helpers/stories/generateListChatbots';
import ChatbotCreate from '@/pages/CreateChatbot/index';
import { fn } from '@storybook/test';

import LayoutShell from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Layout/LayoutShell',
  component: LayoutShell,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'fullscreen'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {}
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  parameters: {
    viewport: {
      defaultViewport: 'reset'
    }
  },
  args: {
    title: 'Lorem ipsum',
    children: <ChatbotDashboard chatbots={listFakeChatbots} />
  }
};

export const Avatar = {
  parameters: {
    viewport: {
      defaultViewport: 'reset'
    }
  },
  args: {
    title: 'Lorem ipsum',
    children: <AvatarDashboard />
  }
};

export const Mobile = {
  parameters: {
    viewport: {
      defaultViewport: 'iphone14promax'
    }
  },
  args: Default.args
};
export const Tablets = {
  parameters: {
    viewport: {
      defaultViewport: 'ipad'
    }
  },
  args: Default.args
};
export const LaptopHD = {
  parameters: {
    viewport: {
      defaultViewport: 'laptopHd'
    }
  },
  args: Default.args
};
export const LaptopFullHD = {
  parameters: {
    viewport: {
      defaultViewport: 'laptopFullHd'
    }
  },
  args: Default.args
};

export const CreateChatbot = {
  parameters: {
    viewport: {
      defaultViewport: 'laptopFullHd'
    }
  },
  args: {
    children: <ChatbotCreate />
  }
};

export const LayoutWithChat = {
  parameters: {
    viewport: {
      defaultViewport: 'laptopFullHd'
    }
  },
  args: {
    children: (
      <Chat
        customization={DANTE_THEME_COLOR_CHAT}
        messages={fakerShortConversation}
        hiddenConversation={false}
      />
    )
  }
};
