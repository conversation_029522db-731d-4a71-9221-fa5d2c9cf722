import React, { useCallback, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import * as customizationService from '@/services/customization.service';
import CloseIcon from '@/components/Global/Icons/CloseIcon';

const TooltipsPage = () => {
  const [urlParams] = useSearchParams();
  const [messages, setMessages] = useState([]);
  const [prompts, setPrompts] = useState([]);
  const [tooltipsVisible, setTooltipsVisible] = useState(false);
  const [closeVisible, setCloseVisible] = useState(false);
  const [visibleIndex, setVisibleIndex] = useState(0); // Tracks the number of visible items
  const [customizationData, setCustomizationData] = useState(null); // Stores fetched data

  const TOOLTIP_SHOWN_KEY = 'tooltipShown';

  // Fetch chatbot customization data
  const getChatBotCustomization = useCallback(async () => {
    const tooltipAlreadyShown = sessionStorage.getItem(TOOLTIP_SHOWN_KEY);

    if (urlParams.get('kb_id')) {
      try {
        const kb_id = urlParams.get('kb_id');
        const token = urlParams.get('token');
        const { data } = await customizationService.getSharedChatbotCustomizationById({ kb_id, token });

        if (data) {
          const initialMessages = data.initial_messages || [];
          const initialPrompts = data.prompt_suggestions.slice(0, 2) || [];

          setMessages(initialMessages);
          setPrompts(initialPrompts);
          setCustomizationData(data); // Store fetched data

          if (data.show_welcome_message_as_tooltip && !tooltipAlreadyShown) {
            setTooltipsVisible(true);
            if (kb_id !== 'be7fb3e0-690a-42aa-8845-8663af2e08c9') {
              sessionStorage.setItem(TOOLTIP_SHOWN_KEY, 'true');
            }
          }
        }
      } catch (error) {
        console.error('Error fetching chatbot customization:', error);
      }
    }

    if (tooltipAlreadyShown && !tooltipsVisible) {
      window.parent.postMessage({ eventType: 'tooltipCloseClick', eventData: true }, '*');
    }
  }, [urlParams, tooltipsVisible]);

  // Handle closing of tooltips
  const handleCloseTooltip = () => {
    setVisibleIndex(0); // Reset visibility
    setCloseVisible(false);
    parent.postMessage({ eventType: 'tooltipCloseClick', eventData: true }, '*');
  };

  // Handle clicking on a prompt
  const handleClickPrompt = (ele) => {
    // if (window.parent !== window.top) {
    //   window.top.postMessage({ eventType: 'promptClick', eventData: ele }, '*');
    // } else {
      window.parent.postMessage({ eventType: 'promptClick', eventData: ele }, '*');
    // }
  };
  

  // Handle clicking on a tooltip message
  const handleClickTooltip = (ele) => {
    window.parent.postMessage({ eventType: 'tooltipClick', eventData: ele }, '*');
  };

  // Fetch customization data on component mount or when dependencies change
  useEffect(() => {
    getChatBotCustomization();
  }, [getChatBotCustomization]);

  // Sequentially display tooltips
  useEffect(() => {
    if (tooltipsVisible) {
      const combinedItems = [
        ...messages.map((msg) => ({ ...msg, type: 'message' })),
        ...prompts.map((prompt) => ({ ...prompt, type: 'prompt' })),
      ];

      const totalItems = combinedItems.length;
      let currentIndex = 0;
      const delayBetween = 1000;

      const showNext = () => {
        if (currentIndex < totalItems) {
          setVisibleIndex(currentIndex + 1);
          currentIndex += 1;
          setTimeout(showNext, delayBetween);
        } else {
          setCloseVisible(true);
        }
      };

      showNext();

      return () => {
        currentIndex = totalItems;
      };
    }
  }, [tooltipsVisible, messages, prompts]);

  // Combine messages and prompts for rendering
  const combinedItems = [
    ...messages.map((msg) => ({ ...msg, type: 'message' })),
    ...prompts.map((prompt) => ({ ...prompt, type: 'prompt' })),
  ];

  useEffect(() => {
    if (tooltipsVisible && customizationData) {
      const tooltipContainer = document.querySelector('.tooltip-container');
      if (!tooltipContainer) return;

      const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach((entry) => {
          const newHeight = entry.contentRect.height;
          if (!customizationData.custom_url_enabled) {
            parent.postMessage(
              { eventType: 'tooltipContainerHeight', eventData: newHeight },
              '*'
            );
          } else {
            window.parent.postMessage(
              { eventType: 'tooltipContainerHeight', eventData: newHeight },
              '*'
            );
            if (window.parent.parent) {
              window.parent.parent.postMessage(
                { eventType: 'tooltipContainerHeight', eventData: newHeight },
                '*'
              );
            }
          }
        });
      });

      resizeObserver.observe(tooltipContainer);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [tooltipsVisible, customizationData]);

  return (
    <>
      {tooltipsVisible && (
        <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
          <div className="tooltip-container pointer-events-auto">
            {/* Close Button */}
            <div className="flex justify-end w-full mb-2">
              <button
                onClick={handleCloseTooltip}
                className={`dbutton text-xs bg-white w-4 h-4 rounded-full border border-gray-200 flex items-center justify-center transition-opacity duration-300 ${
                  closeVisible ? 'opacity-100' : 'opacity-0'
                }`}
                aria-label="Close tooltips"
              >
                <CloseIcon className="size-3" />
              </button>
            </div>

            {/* Tooltips Wrapper */}
            <div className="tooltip-wrapper flex flex-col space-y-2 items-end">
              {combinedItems.map((ele, i) => (
                <div
                  key={i}
                  onClick={() =>
                    ele.type === 'message' ? null : handleClickPrompt(ele.content)
                  }
                  className={`w-max max-w-[300px] p-2 ${ele.type === 'message' ? 'cursor-default' : 'cursor-pointer'} ${
                    ele.type === 'message' ? 'bg-white' : 'bg-zinc-300'
                  } rounded-md shadow-xs border border-gray-20 
                  transition-opacity transition-transform duration-500 ease-out
                  ${
                    visibleIndex > i
                      ? 'opacity-100 translate-y-0'
                      : 'opacity-0 translate-y-4'
                  }`}
                  style={{
                    transitionDelay: visibleIndex > i ? `${i * 100}ms` : '0ms',
                  }}
                >
                  <p className="text-base text-wrap">{ele.content}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TooltipsPage;