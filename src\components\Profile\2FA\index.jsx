import { useEffect, useState } from 'react';

import useDante<PERSON>pi from '@/hooks/useDanteApi';
import * as userService from '@/services/user.service';
import DInput from '@/components/Global/DInput/DInput';
import DButtonIcon from '@/components/Global/DButtonIcon';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import Box2FA from '@/components/Global/Box2FA';
import DButton from '@/components/Global/DButton';
import useToast from '@/hooks/useToast';
import CheckmarkIcon from '@/components/Global/Icons/CheckmarkIcon';


const Form2FA = ({ onClose, user, setUser }) => {
    const { data: qrCode, response: qrCodeResponse } = useDanteApi(userService.generate2FAQRCode);
    const { data: setupKey } = useDanteApi(userService.generate2FASetupKey);

    const [isCopied, setIsCopied] = useState(false);
    const [qrCodeUrl, setQrCodeUrl] = useState(null);
    const [otp, setOtp] = useState(Array(6).fill(''));
    const [error, setError] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    const { addSuccessToast } = useToast();

    const handleVerify = async() => {
        if(otp.length !== 6){
            setError('Please enter a valid 6-digit code');
            return;
        }
        try{
            setIsLoading(true);
            const response = await userService.check2FAOTP(otp);
            if(response.status === 200){
                addSuccessToast({ message: '2FA enabled successfully' });
                setUser({...user, requires_2FA: true});
                onClose();
            }
        } catch (error) {
            console.log(error);
        } finally {
            setIsLoading(false);
        }
    }

    useEffect(() => {
        // Check if qrCode exists and create object URL
        if (qrCode) {
            // Create a blob from the qrCode data if it's not already a blob
            const qrCodeBlob = qrCode instanceof Blob ? qrCode : new Blob([qrCode]);
            const url = URL.createObjectURL(qrCodeBlob);
            setQrCodeUrl(url);

            // Cleanup URL when component unmounts
            return () => {
                if (url) URL.revokeObjectURL(url);
            };
        }
    }, [qrCode]);


    return (
        <div className="flex flex-col gap-size3 p-size2 bg-grey-2 rounded-size0 w-full">
            <div className="flex justify-between items-start gap-size2">
                <div className="flex flex-col gap-size2">
                    <p className="text-sm font-regular tracking-tight">1. You will need an authenticator mobile app to complete this process, such as one of the following:</p>
                    <ul className="list-disc list-inside text-xs font-medium tracking-tight">
                        <li>Google Authenticator</li>
                        <li>Microsoft Authenticator</li>
                    </ul>
                </div>
                <div className="w-size12 h-size12 flex items-center justify-center">
                    {qrCodeUrl && <img src={qrCodeUrl} alt="QR Code" width={150} height={150} style={{ maxWidth: '100%', maxHeight: '100%' }} />}
                </div>
            </div>
            <div className="flex flex-col gap-size1">
                <div className="flex flex-col gap-size0">
                    <p className="text-sm font-regular tracking-tight">2. Scan the QR code above with your authenticator mobile app.</p>
                    <span className="text-xs font-medium tracking-tight text-grey-50">If you can't scan the code, you can eneter this secret key into your authentication app</span>
                </div>
                <div className="flex gap-size1 items-center w-full">
                    <div className="w-full">
                        <DInput
                            value={setupKey?.setup_key}
                            disabled
                        />
                    </div>
                    <DButtonIcon
                        variant="lightgrey"
                        size="lg"
                        onClick={() => {
                            setIsCopied(true);
                            navigator.clipboard.writeText(setupKey?.setup_key);
                            setTimeout(() => {
                                setIsCopied(false);
                            }, 2000);
                        }}
                    >
                        {isCopied ? <CheckmarkIcon /> : <CopyIcon />}
                    </DButtonIcon>
                </div>
            </div>
            <div className="flex flex-col gap-size1">
                <p className="text-sm font-regular tracking-tight">3. After scanning the QR code above, enter the six-digit code generated by your authenticator app.</p>
                <div>
                    <Box2FA
                        value={otp}
                        onChange={(value) => setOtp(value)}
                        error={error}
                    />
                </div>
            </div>
            <DButton
                variant="dark"
                size="sm"
                onClick={handleVerify}
                fullWidth
                loading={isLoading}
            >
                Verify now
            </DButton>
        </div>
    )
}

export default Form2FA;