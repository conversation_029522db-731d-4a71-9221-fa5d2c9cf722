import { DEFAULT_HEADERS } from './constants.service';
import http from './http';

export const getAvailableNumbers = async (filters) => {
  // Remove empty parameters and transform parameter names for API compatibility
  const cleanedFilters = {};
  Object.keys(filters).forEach(key => {
    if (filters[key] !== '' && filters[key] !== null && filters[key] !== undefined) {
      // Handle special parameter mappings
      if (key === 'area_code') {
        // If using the underscore version, map to the camelCase version expected by the API
        cleanedFilters['areaCode'] = filters[key];
      } else {
        cleanedFilters[key] = filters[key];
      }
    }
  });

  return await http.get(import.meta.env.VITE_APP_BASE_API + 'twilio/numbers', {
    params: cleanedFilters,
    headers: DEFAULT_HEADERS
  });
};

export const createNumberPurchaseCheckoutSession = async (phoneNumber) => {
  return await http.post(
    import.meta.env.VITE_APP_BASE_API + 'twilio/get-number-purchase-checkout-session',
    {},
    {
      params: { phone_number: phoneNumber },
      headers: DEFAULT_HEADERS
    }
  );
};

export const confirmNumberPurchase = async (token, sessionId, userId) => {
  return await http.get(
    import.meta.env.VITE_APP_BASE_API + 'twilio/purchase-number-success',
    {
      params: { token, session_id: sessionId, user_id: userId },
      headers: DEFAULT_HEADERS
    }
  );
};

export const cancelNumberPurchase = async (token, userId) => {
  return await http.get(
    import.meta.env.VITE_APP_BASE_API + 'twilio/purchase-number-cancel',
    {
      params: { token, user_id: userId },
      headers: DEFAULT_HEADERS
    }
  );
};

// Simplified success function for the new success page
export const purchaseNumberSuccess = async () => {
  // Get URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get('token');
  const sessionId = urlParams.get('session_id');
  const userId = urlParams.get('user_id');

  if (!token || !sessionId || !userId) {
    return { success: false, error: 'Missing required parameters' };
  }

  try {
    const response = await confirmNumberPurchase(token, sessionId, userId);
    const urlParams = new URLSearchParams(window.location.search);
    const phoneNumberFromUrl = urlParams.get('phone_number');

    return {
      success: true,
      data: {
        ...response.data,
        // Use the phone number from the response if available, otherwise from URL
        phone_number: response.data?.phone_number || phoneNumberFromUrl
      }
    };
  } catch (error) {
    console.error('Error confirming purchase:', error);
    return { success: false, error: error.message };
  }
};

// Simplified cancel function for the new cancel page
export const purchaseNumberCancel = async () => {
  // Get URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get('token');
  const userId = urlParams.get('user_id');

  if (!token || !userId) {
    return { success: false, error: 'Missing required parameters' };
  }

  try {
    const response = await cancelNumberPurchase(token, userId);
    return { success: true, data: response.data };
  } catch (error) {
    console.error('Error cancelling purchase:', error);
    return { success: false, error: error.message };
  }
};
