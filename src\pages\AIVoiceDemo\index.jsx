import React, { useState, useEffect } from 'react';
import { useAudioState } from './useAudioState';
import {
  AudioVisualizer,
  ControlButtons,
  Header
} from './UIComponents';
import VoiceSettingsAccordion from './VoiceSettingsAccordion';
import { useUserStore } from '@/stores/user/userStore';

const AIVoiceDemo = () => {
  const { auth } = useUserStore(); // Get auth from user store only for display purposes
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [config, setConfig] = useState(null);
  // Hardcoded token for AI Voice Demo
  const hardcodedToken = 'DanteAIVoiceDemo';

  const {
    isStartButtonDisabled,
    isStopButtonDisabled,
    isPlaying,
    userAudioLevel,
    speaking,
    smoothedBotLevel,
    startAudio,
    stopAudio,
    settings,
    setSettings,
    isMuted,
    toggleMute,
    hasReceivedFirstMessage
  } = useAudioState();

  // Check only for saved demo email, regardless of user login status
  useEffect(() => {
    const savedEmail = localStorage.getItem('aiVoiceDemoEmail');

    if (savedEmail) {
      setEmail(savedEmail);
      validateAccess(savedEmail);
    }
  }, []);

  // Apply config settings when they are loaded
  useEffect(() => {
    if (config && isAuthenticated) {
      // Make sure we're setting all the needed properties correctly
      setSettings(prevSettings => {
        const newSettings = {
          ...prevSettings,
          // Apply default settings for voice and KB
          selectedVoice: config.defaultSettings?.selectedVoice || prevSettings.selectedVoice,
          kbId: config.defaultSettings?.kbId || prevSettings.kbId,
          initialMessage: config.defaultSettings?.initialMessage || prevSettings.initialMessage,
          voice_instructions: config.defaultSettings?.voice_instructions || prevSettings.voice_instructions,
          // IMPORTANT: Preserve the auth token that was used for validation
          // DON'T overwrite with value from config

          // IMPORTANT: Preserve the email that was used for login
          userEmail: prevSettings.userEmail,

          // Make sure voiceOptions and kbOptions are properly set
          voiceOptions: config.voiceOptions || prevSettings.voiceOptions,
          kbOptions: config.kbOptions || prevSettings.kbOptions
        };
        return newSettings;
      });
    }
  }, [config, isAuthenticated, setSettings]);

  const validateAccess = async (emailToUse) => {
    setIsLoading(true);
    setError(null);

    // Use the provided email or fall back to the state email
    const emailToSave = emailToUse || email;

    try {
      const response = await fetch(`${import.meta.env.VITE_APP_BASE_API || ''}ai-voices/demo-access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: hardcodedToken }),
      });

      const data = await response.json();

      if (response.ok && data.access_granted) {
        setIsAuthenticated(true);
        localStorage.setItem('aiVoiceDemoEmail', emailToSave);

        // Set the validated token directly in settings
        setSettings(prevSettings => ({
          ...prevSettings,
          authToken: hardcodedToken,
          userEmail: emailToSave
        }));

        // Then fetch the config
        fetchConfig(hardcodedToken);
      } else {
        setError('Access validation failed. Please try again later.');
        localStorage.removeItem('aiVoiceDemoEmail');
      }
    } catch (err) {
      setError('Error validating access. Please try again later.');
      console.error('Access validation error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchConfig = async (tokenToUse) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_APP_BASE_API || ''}ai-voices/demo-config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: tokenToUse }),
      });

      if (response.ok) {
        const configData = await response.json();
        setConfig(configData);
      } else {
        const errorText = await response.text();
        console.error('Failed to fetch voice config:', response.status, errorText);
        setError('Failed to load configuration. Please try again.');
      }
    } catch (err) {
      console.error('Error fetching voice config:', err);
      setError('Error loading configuration. Please check your connection.');
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    validateAccess(email);
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto py-12 flex flex-col items-center min-h-screen bg-gray-50">
        <div className="w-full max-w-md">
          <Header />
          <div className="bg-white p-6 rounded-lg shadow-md mt-8">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">AI Voice Demo Access</h2>
            <p className="mb-4 text-gray-600">
              Before continuing, please provide your email.
            </p>
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className={'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 border-gray-300 focus:ring-gray-400 text-min-safe-input'}
                  disabled={isLoading}
                  required
                  autoComplete="email"
                />
              </div>
              {error && (
                <div className="mb-4 p-2 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  {error}
                </div>
              )}
              <button
                type="submit"
                className="w-full py-2 px-4 bg-gray-800 hover:bg-gray-900 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-colors"
                disabled={isLoading || !email.trim()}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : (
                  'Access Demo'
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 flex flex-col items-center min-h-screen bg-gray-50">
      <Header />

      <div className="absolute top-4 right-4">
        <button
          onClick={() => {
            // Stop any active audio session first
            if (!isStopButtonDisabled) {
              stopAudio(true);
            }

            // Clear authentication
            localStorage.removeItem('aiVoiceDemoEmail');
            setIsAuthenticated(false);
            setEmail('');
          }}
          className="py-1 px-3 bg-gray-200 hover:bg-gray-300 text-gray-800 text-sm rounded-md focus:outline-none transition-colors"
        >
          Logout
        </button>
      </div>

      {!config ? (
        <div className="flex flex-col items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800 mb-4"></div>
          <p className="text-gray-600">Loading voice configuration...</p>
        </div>
      ) : (
        <>
          <AudioVisualizer
            speaking={speaking}
            userAudioLevel={userAudioLevel}
            smoothedBotLevel={smoothedBotLevel}
            isCalling={isPlaying && !hasReceivedFirstMessage && speaking !== 'bot'}
          />

          <ControlButtons
            isStartButtonDisabled={isStartButtonDisabled}
            isStopButtonDisabled={isStopButtonDisabled}
            startAudio={startAudio}
            stopAudio={stopAudio}
            isMuted={isMuted}
            toggleMute={toggleMute}
          />

          <div className="fixed bottom-0 left-0 right-0 p-4 pt-8 pb-6 bg-gradient-to-t from-gray-50 via-gray-50">
            <VoiceSettingsAccordion
              settings={settings}
              setSettings={setSettings}
              isStartButtonDisabled={isStartButtonDisabled}
              isStopButtonDisabled={isStopButtonDisabled}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default AIVoiceDemo;
