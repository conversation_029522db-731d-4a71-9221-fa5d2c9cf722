import { useState, useEffect } from 'react';
import DButton from '../Global/DButton';
import CloseIcon from '../Global/Icons/CloseIcon';
import { useNavigate } from 'react-router-dom';

const CreateChatbotPopup = ({ user }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => setIsMounted(false), 500);
  };

  return (
    isMounted &&
    user.tier_on_trial &&
    !user.has_created_chatbot && (
      <div>
        <div
          className={`flex flex-col gap-size3 justify-center p-size5 rounded-size1 bg-black text-white shadow-sm w-[300px] h-[220px] fixed bottom-size3 left-size3 transform transition-transform duration-500 ease-in-out ${
            isVisible ? 'translate-x-0' : '-translate-x-full'
          }`}
        >
          <div className="flex items-center justify-between">
            <p className="text-lg font-medium">Extend Trial</p>
            <button onClick={handleClose}>
              <CloseIcon className="w-size3 h-size3 text-white" />
            </button>
          </div>
          <p className="text-sm text-white">
            Unlock 7 additional free trial days by creating an AI Chatbot
          </p>
          <DButton
            variant="contained"
            fullWidth
            onClick={() => navigate('/chatbot/create')}
          >
            Create AI Chatbot
          </DButton>
        </div>
      </div>
    )
  );
};

export default CreateChatbotPopup;
