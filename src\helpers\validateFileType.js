const mimeTypeAllowed = [
    'application/epub+zip',
    'application/json',
    'application/json',
    'application/mbox',
    'application/pdf',
    'application/rtf',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/xml',
    'application/msword',
    'image/jpeg',
    'image/png',
    'text/csv',
    'text/html',
    'text/markdown',
    'text/plain',
    'text/xml',
    'text/rtf',
    'application/x-epub+zip',
    'application/json',
    'application/x-mbox',
    'application/pdf',
    'application/rtf',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/xml',
    'application/msword',
    'image/jpeg',
    'image/png',
    'text/csv',
    'text/html',
    'text/markdown',
    'text/plain',
    'text/xml',
    'text/rtf'
  ];
  
  const extensionsAllowed = [
    'epub',
    'json',
    'json',
    'mbox',
    'pdf',
    'rtf',
    'ppt',
    'pptx',
    'xlsx',
    'docx',
    'doc',
    'xml',
    'jpg',
    'png',
    'csv',
    'html',
    'md',
    'txt',
    'xml',
    'rtf'
  ];
  
  const isValidFileType = (file) => {
    return (
      mimeTypeAllowed.includes(file.type) || extensionsAllowed.includes(file.name.split('.').pop())
    );
  };
  
  export default isValidFileType;
  