import * as React from 'react';

const AddChatbotIcon = (props) => (
  <svg
    id="Layer_2"
    height={512}
    viewBox="0 0 512 512" 
    width={512}
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    data-name="Layer 2"
    {...props}
  >
    <linearGradient
      id="linear-gradient"
      gradientUnits="userSpaceOnUse"
      x1={43.93}
      x2={468.07}
      y1={43.93}
      y2={468.07}
    >
      <stop offset={0} stopColor="rgb(112, 30, 245)" />
      <stop offset={1} stopColor="rgb(168, 117, 250)" />
    </linearGradient>
    <g id="Icon">
      <g id="_39" data-name="39">
        <rect
          id="Background"
          fill="url(#linear-gradient)"
          height={512}
          rx={150}
          width={512}
        />
        <path
          d="M376 231h-95v-95c0-13.81-11.19-25-25-25-13.81 0-25 11.19-25 25v95h-95c-13.81 0-25 11.19-25 25 0 13.81 11.19 25 25 25h95v95c0 13.81 11.19 25 25 25 13.81 0 25-11.19 25-25v-95h95c13.81 0 25-11.19 25-25 0-13.81-11.19-25-25-25z"
          fill="#fff"
        />
      </g>
    </g>
  </svg>
);

export default AddChatbotIcon;
