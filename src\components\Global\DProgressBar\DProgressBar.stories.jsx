import DProgressBar from './index.jsx';

export default {
  title: 'Global/DProgressBar',
  component: DProgressBar,
  argTypes: {
    steps: {
      control: {
        type: 'array',
        default: [
          { id: 1, status: 'done' },
          { id: 2, status: 'done' },
          { id: 3, status: 'active' },
          { id: 4, status: 'pending' },
          { id: 5, status: 'pending' }
        ]
      }
    }
  },
  args: {
    steps: [
      { id: 1, status: 'done' },
      { id: 2, status: 'done' },
      { id: 3, status: 'active' },
      { id: 4, status: 'pending' },
      { id: 5, status: 'pending' }
    ]
  }
};

export const Default = (args) => <DProgressBar steps={args.steps} />;
