import ChatListMessages from '@/components/Chatbot/ChatListMessages';
import DLoading from '@/components/DLoading';
import DToastContainer from '@/components/DToast/DToastContainer';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';
import DTransition from '@/components/Global/DTransition';
import StyleTag from '@/components/StyleTag';
import { COMMON_CLASSNAMES, DANTE_THEME_CHAT } from '@/constants';
import convertThemeToCSSVariablesStyle from '@/helpers/convertThemeToCSSVariables';
import generateGoogleFonts from '@/helpers/generateGoogleFonts';
import transformLinkUri from '@/helpers/transformLinkUri';
import transformMessageToBubbleMessage from '@/helpers/transformMessageToBubbleMessage';
import { getConversationMessagesBySharedId } from '@/services/message.service';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import { Transition } from '@headlessui/react';
import clsx from 'clsx';
import { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const ShareConversation = () => {
  const [searchParams] = useSearchParams();
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);

  const conversation_id = searchParams.get('id');
  const { chatbotCustomization } = useCustomizationStore();

  useEffect(() => {
    const fetchConversationMessages = async () => {
      try {
        const response = await getConversationMessagesBySharedId(
          conversation_id
        );
        if (response.status === 200) {
          const processedMessages = response.data.results.flatMap((message) =>
            transformMessageToBubbleMessage(message)
          );
          setMessages(processedMessages);
        }
      } catch (error) {
        console.error('Error fetching conversation messages:', error);
      } finally {
        setLoading(false);
      }
    };

    if (conversation_id) {
      fetchConversationMessages();
    }
  }, [conversation_id]);

  const customizationFontFamily = useMemo(() => {
    return generateGoogleFonts(
      chatbotCustomization.font_name || DANTE_THEME_CHAT.font_name
    );
  }, [chatbotCustomization]);

  return (
    <>
      <div
        className={clsx(
          'embed',
          'flex flex-col w-full h-full items-center',
          'transition-all',
          COMMON_CLASSNAMES.transition.duration.short
        )}
        style={{
          backgroundColor: 'var(--dt-color-surface-100)',
        }}
      >
        <StyleTag tag="body" tempCustomizationData={chatbotCustomization} customImport={customizationFontFamily} />
        <div className="w-full flex flex-col items-end px-size2">
          <DToastContainer />
        </div>
        <header className="flex items-center justify-between py-size3 px-size5 sm:px-size2 w-full max-w-screen-xl">
          <div className="flex items-center justify-between w-[95px] h-[25px]">
            <DFullLogo />
          </div>
        </header>
        <Transition show={loading}>
          <div className="transition-all duration-200 data-[enter]:delay-300 ease-in-out data-[closed]:opacity-0 flex flex-col w-full max-w-screen-lg h-screen">
            <DLoading show={true} />
          </div>
        </Transition>
        <Transition show={!loading}>
          <div className="transition-all duration-200 data-[enter]:delay-300 ease-in-out data-[closed]:opacity-0 flex flex-col w-full max-w-screen-lg h-full">
            <ChatListMessages
              transformLinkUri={transformLinkUri}
              messages={messages}
              readonly
              hideFooter
            />
          </div>
        </Transition>
      </div>
    </>
  );
};

export default ShareConversation;
