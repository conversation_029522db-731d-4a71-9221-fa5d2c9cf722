import * as React from 'react';
const CardMasterCardIcon = (props) => (
  <svg
    width={24}
    height={16}
    viewBox="0 0 24 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width={24} height={16} rx={2} fill="#212B36" />
    <path fill="#fff" opacity={0.01} d="M2.32 1.6h19.35v12.8H2.32z" />
    <path fill="#F26122" d="M9.45 3.83h5.11v8.34H9.45z" />
    <path
      d="M10 8a5.31 5.31 0 0 1 2-4.17 5.3 5.3 0 1 0 0 8.34A5.29 5.29 0 0 1 10 8"
      fill="#EA1D25"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.41 11.08v.21h-.11v-.06l.06-.15zm-.131.096.021.054v-.1zm-.039-.096.039.096-.039.084-.06-.13v-.05zm-.17.04v.17H20v-.17z"
      fill="#F69E1E"
    />
    <path
      d="M20.58 8A5.31 5.31 0 0 1 12 12.17a5.31 5.31 0 0 0 0-8.34A5.3 5.3 0 0 1 20.58 8"
      fill="#F69E1E"
    />
  </svg>
);
export default CardMasterCardIcon;
