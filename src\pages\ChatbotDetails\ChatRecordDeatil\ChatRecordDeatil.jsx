import { useEffect, useState } from 'react';
import { DateTime } from 'luxon';
import { useNavigate, useParams } from 'react-router-dom';

import DChatbotSidebar from '@/components/Chatbot/Details/ChatbotSidebar';
import DAlert from '@/components/Global/DAlert';
import DBubbleMessage from '@/components/Global/DBubbleMessage';
import DButton from '@/components/Global/DButton';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import useDanteApi from '@/hooks/useDanteApi';
import * as conversationService from '@/services/conversations.service';
import * as messageService from '@/services/message.service';
import useLayoutStore from '@/stores/layout/layoutStore';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import ResolveForm from './ResolveForm';
import generateFilename from '@/helpers/generateFileName';
import transformMessageToBubbleMessage from '@/helpers/transformMessageToBubbleMessage';
import transformLinkUri from '@/helpers/transformLinkUri';
import ChatListMessages from '@/components/Chatbot/ChatListMessages';
import { getSources } from '@/services/model.service';
import { useTimezoneStore } from '@/stores/timezone/timezoneStore';
import DModalExportFormat from '@/components/DModalExportFormat';
import FlagIcon from '@/components/Global/Icons/FlagIcon';
import { checkFeatureAvailability } from '@/helpers/tier/featureCheck';
import DLoading from '@/components/DLoading';

const ChatRecordDeatil = () => {
  const params = useParams();
  const navigate = useNavigate();
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const timezone = useTimezoneStore((state) => state.timezone);
  const [isResolveFormOpen, setIsResolveFormOpen] = useState(false);
  const [sourcesLoading, setSourcesLoading] = useState([]);
  const [showSources, setShowSources] = useState([]);
  const [openSources, setOpenSources] = useState(false);
  const [exportFormatModal, setExportFormatModal] = useState({
    isOpen: false,
    type: ''
  });
  const [isLoadingLeads, setIsLoadingLeads] = useState(false);
  const [isLoadingRecords, setIsLoadingRecords] = useState(false);
  const [timeRange, setTimeRange] = useState({
    date_from: DateTime.now()
      .minus({ days: 30 })
      .startOf('day')
      .toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
    date_to: DateTime.now().endOf('day').toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"),
  });

  const { data: messagesRaw, isLoading } = useDanteApi(
    messageService.getMessagesByConversationId,
    [params.recordId],
    {},
    params.recordId,
    timezone
  );

  const { data: allRecords } = useDanteApi(
    conversationService.getSharedConversations,
    [],
    {},
    params.id,
    timeRange.date_from,
    timeRange.date_to,
    timezone
  );

  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);

  const [messages, setMessages] = useState([]);

  const { response } = useDanteApi(
    conversationService.getConversationById,
    [params.recordId],
    {},
    params.recordId,
    timezone
  );

  const downloadLeads = async () => {
    try {
      setIsLoadingLeads(true);
      const res = await conversationService.downloadLeads(
        response?.data?.kb_id,
        response?.data?.date_created,
        response?.data?.date_created,
        'csv',
        timezone
      );
      if (res.status === 200) {
        const download_url = res.data.download_url;
        
        window.open(download_url, '_blank');
      }
    } catch (err) {
      console.log(err);
    } finally {
      setIsLoadingLeads(false);
    }
  };

  const downloadRecordLog = async (format) => {
    setIsLoadingRecords(true);
    try {
      const res = await conversationService.downloadChatRecordLog(
        response?.data?.kb_id,
        response?.data?.date_created,
        response?.data?.date_created,
        format,
        timezone
      );
      if (res.status === 200) {
        const download_url = res.data.download_url;
        
        window.open(download_url, '_blank');
      }
    } catch (err) {
      console.log(err);
    } finally {
      setIsLoadingRecords(false);
    }
  };

  const handleExportFormat = (format) => {
    if (exportFormatModal.type === 'leads') {
      downloadLeads(format);
    } else if (exportFormatModal.type === 'record') {
      downloadRecordLog(format);
    }
  };

  const handleFooterButton = ({ message_id, action }) => {
    if (!message_id || !action) {
      console.error('Invalid parameters for footer button action');
      return;
    }

    const actionHandlers = {
      sources: () => setOpenSources(true),
    };

    const handler = actionHandlers[action];
    if (handler) {
      handler();
    } else {
      console.warn(`Unhandled action: ${action}`);
    }
  };

  useEffect(() => {
    if (messagesRaw?.results) {
      const processedMessages = [];

      messagesRaw.results.forEach((message) => {
        const transformedMessages = transformMessageToBubbleMessage(message);
        processedMessages.push(...transformedMessages);
        setShowSources((prev) => [...prev, message.id]);
      });

      setMessages(processedMessages);
    }
  }, [messagesRaw]);

  useEffect(() => {
    setSidebarOpen(false);
  }, []);

  return (
    <div className="flex flex-col md:flex-row gap-size3 h-full w-full">
      <div className="w-full h-full bg-white rounded-size1 p-size5 flex flex-col gap-size2">
        <div className="flex justify-between items-center">
          <p className="text-xl font-medium tracking-tight ">Record Preview</p>
          <DButton
            variant="grey"
            className="felx gap-size1 !h-12"
            onClick={() => navigate(`/chatbot/${params.id}/insights/records`)}
          >
            <CloseIcon />
            <p className="text-base font-regular tracking-tight hidden md:block">
              Close preview
            </p>
          </DButton>
        </div>
        <div className="flex gap-size3 flex-col-reverse md:flex-row h-[1px] grow">
          {/* Records Sidebar */}
          <div className="hidden md:flex flex-col gap-size2 w-[300px] py-size5 px-size1 rounded-size5 border border-grey-5 overflow-y-auto">
            <p className="text-base font-medium tracking-tight">All Records</p>
            <div className="flex flex-col gap-size1">
              {allRecords?.results.length > 0 ? allRecords?.results.map((item, index) => (
                <button
                  key={index}
                  onClick={() => navigate(`/chatbot/${params.id}/insights/records/${item.id}`)}
                  className={`flex justify-between items-center p-size2 ${
                    item.id === params.recordId ? 'bg-grey-5' : ''
                  } ${
                    item.has_not_answered_questions
                      ? 'text-negative-100'
                      : 'text-black'
                  } hover:bg-grey-5 rounded-size0 transition-colors`}
                >
                  <p className="text-sm tracking-tight truncate">
                    {item?.medium === 'whatsapp' ? item?.name : DateTime.fromISO(item.date_created).toFormat(
                      'd MMM yyyy - HH:mm:ss'
                    )}
                  </p>
                  {item.has_not_answered_questions && checkFeatureAvailability('unanswered_question_recognition') && (
                    <FlagIcon className="text-negative-100 shrink-0 ml-size1" />
                  )}
                </button>
              )) : <DLoading show={true} />}
            </div>
          </div>

          <div className="py-size5 px-size1 rounded-size5 border border-grey-5 h-full w-full md:w-4/5 overflow-y-auto relative">
            <div className="h-full w-full">
              <ChatListMessages
                transformLinkUri={transformLinkUri}
                messages={messages}
                readonly
                showDate
                place='chat_record'
                sources={messagesRaw?.results.sources ?? []}
                handleFooterButton={handleFooterButton}
                openSources={openSources}
                setOpenSources={setOpenSources}
                sourcesLoading={sourcesLoading}
                showSources={showSources}
              />
            </div>
          </div>
          <div className="py-size3 flex flex-col gap-size5 w-full md:w-[40%]">
            <p className="text-xl font-medium tracking-tight">
              {response?.data?.medium === 'whatsapp' ? response?.data?.name : DateTime.fromISO(response?.data?.date_created).toFormat(
                'd MMM yyyy - HH:mm:ss'
              )}
            </p>
            {/* {data && data.results.some((item) => !item.was_answered) && (
              <div className="flex flex-col gap-size2">
                <DAlert state="negative">
                  <p className="text-sm tracking-tight">
                    One flagged message has been found. Click action button below to resolve this
                    question and update knowledge base with information provided.
                  </p>
                </DAlert>
                <DButton
                  variant="dark"
                  className="h-[40px]"
                  onClick={() => setIsResolveFormOpen(true)}
                >
                  Resolve
                </DButton>
              </div>
            )} */}
            <div className="border border-grey-5 w-full"></div>
            <div className="flex flex-col gap-size2">
              <DButton
                variant="grey"
                className="h-[40px]"
                fullWidth
                onClick={() => downloadLeads()}
              >
                Download leads
              </DButton>
              <DButton
                variant="grey"
                className="h-[40px]"
                fullWidth
                onClick={() => setExportFormatModal({ isOpen: true, type: 'record' })}
              >
                Download record log
              </DButton>
            </div>
          </div>
        </div>
      </div>
      <ResolveForm
        open={isResolveFormOpen}
        onClose={() => setIsResolveFormOpen(false)}
      />
      <DModalExportFormat
        open={exportFormatModal.isOpen}
        onClose={() => setExportFormatModal({ isOpen: false, type: '' })}
        onExport={handleExportFormat}
        loading={isLoadingLeads || isLoadingRecords}
      />
    </div>
  );
};

export default ChatRecordDeatil;
