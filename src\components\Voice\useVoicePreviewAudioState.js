import { useState, useEffect } from 'react';
import { useAudioState } from '../../pages/AIVoiceDemo/useAudioState';
import { useUserStore } from '@/stores/user/userStore';
import { WS_ENDPOINT_TYPES } from '@/services/websocket/voiceWebSocketUtils';

// This is a wrapper around the useAudioState hook that ensures
// we're using the correct WebSocket URL from the .env file
export const useVoicePreviewAudioState = (endpointType = WS_ENDPOINT_TYPES.PREVIEW) => {
  // Get all the state and functions from the original hook
  const audioState = useAudioState();

  // Add connection status state
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [connectionError, setConnectionError] = useState(null);

  // Get user auth data from the user store
  const { auth } = useUserStore();

  // Set endpoint type and JWT token in settings when component mounts or auth changes
  useEffect(() => {
    audioState.setSettings(prev => ({
      ...prev,
      endpointType: endpointType,
      // jwtToken: auth.access_token
    }));
  }, [auth.access_token, audioState.setSettings]);

  // Add connection status handlers
  useEffect(() => {
    // Function to handle WebSocket connection status changes
    const handleConnectionStatus = (status, error = null) => {
      setConnectionStatus(status);
      if (error) {
        setConnectionError(error);
      } else {
        setConnectionError(null);
      }
    };

    // Add the connection status handler to the window object so it can be accessed by WebSocketHandler
    window.__voicePreviewConnectionHandler = handleConnectionStatus;

    // Clean up when component unmounts
    return () => {
      delete window.__voicePreviewConnectionHandler;
    };
  }, []);

  // Return enhanced audio state with connection status
  return {
    ...audioState,
    connectionStatus,
    connectionError
  };
};
