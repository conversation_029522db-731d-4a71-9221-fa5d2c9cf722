import { Duration } from 'luxon';

// Function to format the duration
function formatDuration({ minutes = 0, seconds = 0, hours = 0 }) {
  const duration = Duration.fromObject({ minutes, seconds, hours });

  const hoursConvert = Math.floor(duration.as('hours'));
  // Fix for handling fractional minutes
  const minutesConvert = Math.floor(duration.as('minutes') % 60);
  const secondsConvert = Math.floor(duration.as('seconds') % 60);

  let formatted = '';

  if (hoursConvert > 0) {
    formatted += `${hoursConvert}h`;
  }
  if (minutesConvert > 0) {
    formatted += `${minutesConvert}min`;
  }
  if (secondsConvert > 0) {
    formatted += `${secondsConvert}s`;
  }

  // If we have a non-zero duration but all components rounded to zero, show seconds
  if (formatted === '' && (minutes > 0 || seconds > 0 || hours > 0)) {
    formatted = '1s'; 
  }

  return formatted || '0s'; 
}

export default formatDuration;
