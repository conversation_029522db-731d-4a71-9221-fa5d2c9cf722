import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DModal from '@/components/Global/DModal';
import DMultiselect from '@/components/Global/DMultiselect';
import DSelect from '@/components/Global/DSelect';
import { AGENT_MEMBER_PERMISSIONS } from '@/constants';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import { ListboxOption } from '@headlessui/react';
import { useState } from 'react';

import validateEmail from '@/helpers/validateEmail';
import DToastContainer from '@/components/DToast/DToastContainer';
const AddAgent = ({
  open,
  onClose,
  onSubmit,
  newMember,
  setNewMember,
  selectedChatbots,
  setSelectedChatbots,
  isLoadingMember
}) => {
  const chatbots = useChatbotStore((state) => state.chatbots);
  const [errors, setErrors] = useState([]);

  const handleBeforeSubmit = () => {
    const newErrors = [];

    if (newMember.name === '') {
      newErrors.push({ field: 'name', message: 'Name is required' });
    }
    if (newMember.email === '') {
      newErrors.push({ field: 'email', message: 'Email is required' });
    } else if (!validateEmail(newMember.email)) {
      newErrors.push({ field: 'email', message: 'Invalid email' });
    }

    setErrors(newErrors);
    if (newErrors.length === 0) {
      onSubmit();
    }
  };

  return (
    <DModal
      title="Add Agent"
      isOpen={open}
      onClose={onClose}
      footer={
        <div className="flex items-center gap-size1 w-full">
          <DButton onClick={onClose} variant="grey" fullWidth size="md">
            Cancel
          </DButton>
          <DButton
            onClick={handleBeforeSubmit}
            variant="dark"
            fullWidth
            size="md"
            loading={isLoadingMember}
          >
            Confirm
          </DButton>
        </div>
      }
    >
      <DToastContainer showFixed />

      <div className="flex flex-col gap-size5">
        <div className="flex flex-col gap-size0">
          <p className="text-base font-medium tracking-tight">Name</p>
          <DInput
            placeholder="Enter name"
            value={newMember.name}
            error={errors.find((error) => error.field === 'name')?.message}
            onChange={(e) =>
              setNewMember({ ...newMember, name: e.target.value })
            }
          />
        </div>
        <div className="flex flex-col gap-size0">
          <p className="text-base font-medium tracking-tight">Email</p>
          <DInput
            placeholder="Enter email"
            type="email"
            value={newMember.email}
            error={errors.find((error) => error.field === 'email')?.message}
            onChange={(e) =>
              setNewMember({ ...newMember, email: e.target.value })
            }
          />
        </div>
        {/* <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Permissions</p>
          <DSelect
            value={newMember?.permissions}
            onChange={(value) => {
              setNewMember({ ...newMember, permissions: value });
            }}
            selectedChild={
              <p>
                {
                  AGENT_MEMBER_PERMISSIONS.find(
                    (permission) => permission.value === newMember?.permissions
                  )?.name
                }
              </p>
            }
          >
            {AGENT_MEMBER_PERMISSIONS.map((permission) => (
              <ListboxOption
                key={permission.value}
                value={permission.value}
                className="cursor-pointer border-t border-grey-5 flex flex-col gap-size0 px-size0 py-size1 hover:bg-grey-5"
              >
                <p className="text-sm font-medium">{permission.name}</p>
                <p className="text-xs font-light">{permission.description}</p>
              </ListboxOption>
            ))}
          </DSelect>
        </div> */}
        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Chatbot</p>
          <DMultiselect
            options={chatbots?.map((kb) => ({
              label: kb.name,
              value: kb.kb_id,
            }))}
            selected={selectedChatbots}
            skipSelectAllCheck={true}
            setSelected={(value) => {
              setSelectedChatbots(value);
              setNewMember({
                ...newMember,
                chatbots: value.map((kb) => kb.value),
                all_knowledge_bases: value.length === chatbots?.length,
              });
            }}
          />
        </div>
      </div>
    </DModal>
  );
};

export default AddAgent;
