import DNotificationHeader from '@/components/DNotificationHeader';
import DToastContainer from '@/components/DToast/DToastContainer';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';
import HeaderAccountMenu from '@/components/HeaderAccountMenu';
import LiveAgentTopBar from '@/components/LiveAgentTopBar';
import { useNavigate } from 'react-router-dom';

const LayoutLiveAgent = ({ children }) => {
  const navigate = useNavigate();

  return (
    <main className="layout-medium-screen d-h-screen flex flex-row gap-size2 xl:gap-size5 w-full p-size3">
      <div className="w-full flex flex-col h-full gap-size2">
        <header className="flex justify-between">
          <div className="gap-size0 flex md:w-full">
            <div className="flex items-center gap-size5 w-full">
              <button
                className="overflow-hidden transition-all"
                onClick={() => navigate('/')}
              >
                <DFullLogo size="lg" />
              </button>
              <div className="w-px h-full bg-grey-5 hidden md:flex"></div>
              <div className="hidden md:flex">
                <LiveAgentTopBar />
              </div>
            </div>
          </div>

          <DToastContainer hideInMobile />

          <div className="flex gap-size2 items-center justify-end">
            <DNotificationHeader />
            <HeaderAccountMenu />
          </div>
        </header>
        <div className="gap-size0 flex md:hidden md:w-full">
          <LiveAgentTopBar />
        </div>
        <div className="h-[1px] flex flex-col grow">{children}</div>
      </div>
    </main>
  );
};

export default LayoutLiveAgent;
