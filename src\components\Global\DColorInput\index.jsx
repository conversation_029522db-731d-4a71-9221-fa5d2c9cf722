import { useState, useEffect, useRef } from 'react';
import { ChromePicker } from 'react-color';

const DColorInput = ({ value, onChange, id = '' }) => {
  const [showPicker, setShowPicker] = useState(false);
  const pickerRef = useRef(null);

  const togglePicker = (e) => {
    e.stopPropagation();
    setShowPicker(!showPicker);
  };

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (pickerRef.current && !pickerRef.current.contains(e.target)) {
        setShowPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" data-testid={`d-color-input-${id}`}>
      {/* Button to toggle color picker */}
      <button
        onClick={togglePicker}
        className="dbutton bg-grey-2 rounded-size1 p-size1 flex items-center gap-size1"
        data-testid={`d-color-input-button-${id}`}
      >
        <div
          className={'rounded-size0 border-grey-5 w-5 h-5'}
          style={{ backgroundColor: value }}
          data-testid={`d-color-input-preview-${id}`}
        ></div>
        <div
          className="ml-2 text-grey-700"
          data-testid={`d-color-input-value-${id}`}
        >
          {value}
        </div>
      </button>

      {/* Color Picker */}
      {showPicker && (
        <div
          ref={pickerRef}
          className="absolute top-[46px] left-0 z-50"
          data-testid={`d-color-picker-${id}`}
        >
          <div data-testid={`d-color-picker-hex-input-${id}`}>
            <ChromePicker
              color={value}
              onChange={onChange}
              onClick={(e) => e.stopPropagation()} // Prevent closing when interacting with the picker
              data-testid={`d-color-picker-component-${id}`}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default DColorInput;
