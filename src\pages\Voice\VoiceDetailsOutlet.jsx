import VoiceSidebar from '@/components/Voice/VoiceSidebar';
import DLoading from '@/components/DLoading';
import { Suspense, useEffect } from 'react';
import { Outlet, useParams, useNavigate } from 'react-router-dom';

const VoiceDetailsOutlet = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Redirect to conversations when accessing /voice/:id
  useEffect(() => {
    const path = window.location.pathname;
    if (path === `/voice/${id}`) {
      navigate(`/voice/${id}/conversations`);
    }
  }, [id, navigate]);

  return (
    <div className="flex flex-col md:flex-row gap-size3 h-full w-full">
      <VoiceSidebar />
      <div className="w-full h-[1px] grow overflow-y-auto md:h-full rounded-size1 flex flex-col gap-size5">
        <Suspense fallback={<DLoading show={true} />}>
          <Outlet />
        </Suspense>
      </div>
    </div>
  );
};

export default VoiceDetailsOutlet; 