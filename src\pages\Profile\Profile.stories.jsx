import LayoutShell from '@/layouts/LayoutShell';

import Profile from './index';

export default {
  title: 'Pages/Profile',
  component: Profile,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen'
  },
  argTypes: {
    onSubmit: {
      action: 'submitted'
    }
  },
  decorators: [
    (Story) => (
      <div className="h-screen flex">
        <Story />
      </div>
    )
  ]
};

export const Default = {
  args: {
    title: 'Profile'
  }
};
