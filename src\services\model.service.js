import http from './http';

export const getLLMModels = ({ tier_type }) => {
  return http.get(`${import.meta.env.VITE_APP_BASE_API}model/llms`, {
    params: {
      tier_type,
    },
  });
};


export const getSources = (message_id) => {
  return http.post(`${import.meta.env.VITE_APP_BASE_API}model/sources`, {},{
    params: {
      message_id,
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};
