import { useNavigate } from 'react-router-dom';
import { describe, expect, it, vi } from 'vitest';

import { useUserStore } from '@/stores/user/userStore';
import { act, renderHook } from '@testing-library/react-hooks';

import useLogout from './logout';

// Mock useNavigate and useUserStore
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn()
}));

vi.mock('@/stores/user/userStore', () => ({
  useUserStore: vi.fn()
}));

describe('useLogout', () => {
  it('should call removeAuthDetail and navigate to /log-in', async () => {
    const mockRemoveAuthDetail = vi.fn();
    const mockNavigate = vi.fn();

    useNavigate.mockReturnValue(mockNavigate);
    useUserStore.mockImplementation(() => mockRemoveAuthDetail);

    const { result } = renderHook(() => useLogout());

    await act(async () => {
      await result.current();
    });

    expect(mockRemoveAuthDetail).toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith('/log-in');
  });
});
