import DButton from "@/components/Global/DButton";
import DCheckbox from "@/components/Global/DCheckbox";
import DInput from "@/components/Global/DInput/DInput";
import DSwitchAccordion from "@/components/Global/DSwitchAccordion";
import DTooltip from "@/components/Global/DTooltip";
import DUpload from "@/components/Global/DUpload";
import AddIcon from "@/components/Global/Icons/AddIcon";
import ChevronRightIcon from "@/components/Global/Icons/ChevronRightIcon";
import DeleteIcon from "@/components/Global/Icons/DeleteIcon";
import FilesIcon from "@/components/Global/Icons/FilesIcon";
import InfoIcon from "@/components/Global/Icons/InfoIcon";
import LinkIcon from "@/components/Global/Icons/LinkIcon";
import { getFriendlyFileType } from "@/helpers/getFriendlyMimeType";
import isValidFileType from "@/helpers/validateFileType";
import useToast from "@/hooks/useToast";
import { useEffect, useState } from "react";
import useIsAboveBreakpoint from "@/helpers/useIsAboveBreakpoint";
// Function to validate URL
const isValidUrl = (urlString) => {
  try {
    // Check if it's empty or default
    if (!urlString || urlString === "https://" || urlString === "http://") {
      return false;
    }

    // Check if it's a valid URL format
    const url = new URL(urlString);

    // Make sure it's http or https protocol
    if (url.protocol !== "http:" && url.protocol !== "https:") {
      return false;
    }

    // Check hostname - require at least one dot and minimum length
    const hostname = url.hostname;
    if (!hostname || hostname.length < 3 || !hostname.includes(".")) {
      return false;
    }

    // Check for valid TLD (at least 2 characters after last dot)
    const parts = hostname.split(".");
    const tld = parts[parts.length - 1];
    if (tld.length < 2) {
      return false;
    }

    return true;
  } catch (e) {
    return false;
  }
};

const ChatbotKnowledge = ({
  activeTab,
  setActiveTab,
  chatbotData,
  setChatbotData,
  errors,
  onBackClick,
  onNextClick,
}) => {
  const { addErrorToast } = useToast();
  const [isBulkUploadMode, setIsBulkUploadMode] = useState(false);
  const [isExcludeMode, setIsExcludeMode] = useState(false);
  const isAboveSm = useIsAboveBreakpoint("sm");
  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      onNextClick();
    }
  };

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [onNextClick]);

  const handleChangeAddUrl = (url, index) => {
    const newUrls = [...chatbotData.chatbotUrls];
    newUrls[index].url = url;
    newUrls[index].max_urls = 1;
    setChatbotData("chatbotUrls", newUrls);
  };

  const handleDeleteUrl = (index) => {
    const newUrls = [...chatbotData.chatbotUrls];
    newUrls.splice(index, 1);
    setChatbotData("chatbotUrls", newUrls);
  };

  const handleAddUrl = () => {
    setChatbotData("chatbotUrls", [
      ...chatbotData.chatbotUrls,
      { url: "https://", sweepEntireDomain: false },
    ]);
  };

  const handleChangeExcludeUrl = (url, index) => {
    const newUrls = [...chatbotData.chatbotExcludedUrls];
    newUrls[index].url = url;
    setChatbotData("chatbotExcludedUrls", newUrls);
  };

  const handleDeleteExcludeUrl = (index) => {
    const newUrls = [...chatbotData.chatbotExcludedUrls];
    newUrls.splice(index, 1);
    setChatbotData("chatbotExcludedUrls", newUrls);
  };

  const handleAddExcludeUrl = () => {
    setChatbotData("chatbotExcludedUrls", [
      ...chatbotData.chatbotExcludedUrls,
      { url: "", sweepEntireDomain: false },
    ]);
  };

  const handleUploadFiles = (e) => {
    if (e.target.files.length > 0) {
      // check if file is valid and if size is max 128mb
      if (isValidFileType(e.target.files[0])) {
        if (e.target.files[0].size <= 128 * 1024 * 1024) {
          setChatbotData("chatbotFiles", [
            ...chatbotData.chatbotFiles,
            ...e.target.files,
          ]);
        } else {
          addErrorToast({ message: "File size must be less than 128MB" });
        }
      } else {
        addErrorToast({ message: "Invalid file type" });
      }
    }
  };

  const handleDeleteFile = (index) => {
    const newFiles = [...chatbotData.chatbotFiles];
    newFiles.splice(index, 1);
    setChatbotData("chatbotFiles", newFiles);
  };

  const handleUploadBulkUrls = (e) => {
    if (e.target.files.length > 0) {
      setChatbotData("chatbotBulkUploadedUrls", [
        ...chatbotData.chatbotBulkUploadedUrls,
        ...e.target.files,
      ]);
    }
  };

  const handleDeleteBulkUrl = (index) => {
    const newUrls = [...chatbotData.chatbotBulkUploadedUrls];
    newUrls.splice(index, 1);
    setChatbotData("chatbotBulkUploadedUrls", newUrls);
  };

  const handleChangeSweepEntireDomain = (checked, index) => {
    const newUrls = [...chatbotData.chatbotUrls];
    newUrls[index].sweepEntireDomain = checked;
    setChatbotData("chatbotUrls", newUrls);
  };

  const handleChangeExcludeSweepEntireDomain = (checked, index) => {
    const newUrls = [...chatbotData.chatbotExcludedUrls];
    newUrls[index].sweepEntireDomain = checked;
    setChatbotData("chatbotExcludedUrls", newUrls);
  };

  return (
    <div className="h-full w-full overflow-y-hidden">
      <div
        className={`flex flex-col md:flex-row gap-size1 w-full ${
          !errors.knowledgeError ? "justify-end" : "justify-between"
        }`}
      >
        {errors.knowledgeError && (
          <p className="text-error mt-size2 max-w-[calc(650px-316px)] mx-auto w-full">
            {errors.knowledgeError}
          </p>
        )}
        <div className="flex gap-size1 justify-end self-end">
          <DButton
            variant="outlined"
            size="md"
            onClick={onBackClick}
            // fullWidth
            className="!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3"
          >
            Back
          </DButton>
          <DButton
            variant="contained"
            size="md"
            // fullWidth
            onClick={onNextClick}
            disabled={
              !chatbotData.chatbotUrls.length &&
              !chatbotData.chatbotFiles.length &&
              !chatbotData.chatbotBulkUploadedUrls.length
            }
            className="!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3 flex items-center justify-center knowledge-next-btn"
          >
            <div className="flex items-center justify-center gap-1">
              <span className="text-lg ml-size2">Next</span>
              <ChevronRightIcon className="w-5 h-5" />
            </div>
          </DButton>
        </div>
      </div>
      <div className="flex flex-col h-full w-full max-w-[650px] mx-auto overflow-y-auto pb-size5 no-scrollbar pt-size5">
        <div className="flex flex-col gap-size8 pb-size5">
          <div className="flex flex-col gap-size1">
            <p className="text-xl font-medium tracking-tight">
              Create your AI Chatbot's memory
            </p>
            <div className="flex flex-col">
              <p className="text-xs font-regular tracking-tight text-grey-50">
                Upload the links and/or files your AI Chatbot will use to learn.
              </p>
              <p className="text-xs font-regular tracking-tight text-grey-50">
                These will become its memory—powering accurate, helpful answers.
              </p>
              <p className="text-xs font-regular tracking-tight text-grey-50">
                🔗 Add files or paste URLs using the tabs below, then click Next
                to continue.
              </p>
            </div>
          </div>
          <div className="flex gap-size1 w-full">
            <button
              className={`flex w-full items-center gap-size0 font-regular tracking-tight !text-[16px] !border !border-grey-20 rounded-size1 px-size2 py-size1 justify-center ${
                activeTab === "urls"
                  ? "bg-grey-5 dark:bg-grey-20"
                  : "bg-white dark:bg-[#212121]"
              }`}
              onClick={() => setActiveTab("urls")}
            >
              <LinkIcon className="size-5" />
              <span className="text-[16px]">Add URLs</span>
            </button>
            <button
              className={`flex w-full items-center gap-size0 !text-base font-regular tracking-tight !border !border-grey-20 rounded-size1 px-size2 py-size1 justify-center ${
                activeTab === "files"
                  ? "bg-grey-5 dark:bg-grey-20"
                  : "bg-white dark:bg-[#212121]"
              }`}
              onClick={() => setActiveTab("files")}
            >
              <FilesIcon className="size-5" />
              <span className="text-base">Upload Files</span>
            </button>
          </div>
          {activeTab === "files" && (
            <div className="flex flex-col gap-size1">
              {/* <div className="flex flex-col gap-size0"> */}
              {/* <span className="text-base font-medium tracking-tight">
              Upload knowledge files
            </span> */}
              {/* <span className="text-xs font-regular tracking-tight text-grey-50">
              Build your AI Chatbot's knowledge base by uploading files. <br />{' '}
              These files train your AI Chatbot to answer questions accurately. <br />{' '}
              Add files and/or URLs then click Next.
            </span> */}
              {/* </div> */}
              <div className="flex flex-col items-start gap-size1">
                <DUpload
                  note="Upload or drag and drop your files here"
                  onChangeFile={(e) => handleUploadFiles(e)}
                  accept=".pdf,.json,.xml,.docx,.doc,.txt,.csv,.xlsx,.ppt,.pptx,.pptx_zip,.jpeg,.png,.markdown,.epub,.mbox,.rtf,.html"
                />
                <a
                  className="text-xs font-regular tracking-tight text-grey-50 underline cursor-pointer"
                  target="_blank"
                  href="https://www.dante-ai.com/guides/uploading-files"
                >
                  Click here for supported file types and max upload size
                </a>
                {/* <DTooltip
              title="Max. size 128mb"
              content={<div className='flex flex-col gap-size0'>
                <span className='text-sm font-regular tracking-tight'>Supported file types:</span>
                <span className='text-sm font-regular tracking-tight'>PDF, JSON, XML, DOCX, DOC, TXT, CSV, XLSX, PPT, PPTX, PPTX_ZIP, JPEG, PNG, MARKDOWN, EPUB, MBOX, RTF, HTML</span>
                <span className='text-sm font-regular tracking-tight'>Max. size: 128mb</span>
              </div>}
            >

              <InfoIcon className="w-4 h-4" />
            </DTooltip> */}
              </div>
              {chatbotData.chatbotFiles.length > 0 &&
                chatbotData.chatbotFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex flex-col gap-size0 max-h-[300px] overflow-y-auto"
                  >
                    <div className="flex flex-col">
                      <div className="px-size1 py-size0 flex items-center justify-between bg-grey-2 rounded-size1">
                        <div className="flex gap-size0 items-center">
                          <FilesIcon className="size-4" />
                          <div className="flex gap-size0 items-center">
                            <span className="text-sm font-regular tracking-tight">
                              {file.name}
                            </span>
                            <div className="flex gap-size0 items-center text-xs font-regular tracking-tight text-grey-20 h-[11px]">
                              <span className="truncate max-w-[300px]">
                                {getFriendlyFileType(file.type)}
                              </span>
                              <div className="w-px h-[11px] bg-grey-20"></div>
                              <span
                                className={
                                  file.size >= 128 * 1024 * 1024
                                    ? "text-error"
                                    : ""
                                }
                              >
                                {file.size >= 1024 * 1024
                                  ? `${(file.size / (1024 * 1024)).toFixed(
                                      2
                                    )} MB`
                                  : `${(file.size / 1024).toFixed(2)} KB`}
                              </span>
                            </div>
                          </div>
                        </div>
                        <button
                          className="dbutton"
                          onClick={() => handleDeleteFile(index)}
                        >
                          <DeleteIcon className="size-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
          {activeTab === "urls" && (
            <div className="flex flex-col gap-size5">
              <div className="flex flex-col gap-size1">
                <div className="flex flex-col gap-size0">
                  <span className="text-base font-medium tracking-tight">
                    Add URLs
                  </span>
                  <div className="flex flex-col">
                    <span className="text-xs font-regular tracking-tight text-grey-50">
                      Paste website links, YouTube videos, Google Docs, or
                      Google Sheets.
                    </span>
                    <span className="text-xs font-regular tracking-tight text-grey-50">
                      These links help build your AI Chatbot's memory—so it can
                      learn from the content.
                    </span>
                    <span className="text-xs font-regular tracking-tight text-grey-50">
                      🔗 Enter one or more URLs, then click Next to continue.
                    </span>
                  </div>
                </div>
                {chatbotData.chatbotUrls.map((url, index) => (
                  <div key={index} className="flex flex-col gap-size4">
                    <div className="flex gap-size1">
                      <DInput
                        placeholder="Enter a valid URL"
                        icon={<LinkIcon />}
                        value={url.url}
                        onChange={(e) =>
                          handleChangeAddUrl(e.target.value, index)
                        }
                        error={errors.knowledgeUrlError}
                        className="url-input"
                      />
                      {chatbotData.chatbotUrls.length > 1 && index !== 0 && (
                        <button
                          onClick={() => handleDeleteUrl(index)}
                          className="dbutton bg-grey-2 rounded-size1 p-size1 w-12 h-12 flex items-center justify-center"
                        >
                          <DeleteIcon />
                        </button>
                      )}
                    </div>
                    <div className="flex gap-size1 items-start">
                      <DCheckbox
                        label="Sweep the entire domain"
                        checked={url.sweepEntireDomain}
                        onChange={(checked) =>
                          handleChangeSweepEntireDomain(checked, index)
                        }
                        className=""
                        style={{
                          width: "auto",
                        }}
                      />
                      <DTooltip
                        position={isAboveSm ? "right" : "top center"}
                        content="With this feature, we crawl the website starting from the specified URL and record every URL we encounter during the process."
                      >
                        <InfoIcon className="w-3 h-3 mt-[5px]" />
                      </DTooltip>
                    </div>
                  </div>
                ))}

                <button
                  className={`text-xs font-regular tracking-tight flex items-center gap-size0 p-size0 rounded-size2 max-w-36 w-auto ${
                    !isValidUrl(chatbotData.chatbotUrls[0].url)
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:bg-grey-1"
                  }`}
                  onClick={handleAddUrl}
                  disabled={!isValidUrl(chatbotData.chatbotUrls[0].url)}
                >
                  <AddIcon height={16} width={16} className="mr-size0" />
                  <span>Add another URL</span>
                </button>
              </div>
              <div className="h-px bg-grey-5 w-full"></div>
              <DSwitchAccordion
                title="Exclude URLs"
                switchOpen={isExcludeMode}
                onToggle={() => setIsExcludeMode(!isExcludeMode)}
                titleClassName="text-sm font-medium tracking-tight"
              >
                <div className="flex flex-col gap-size1 px-size0">
                  <div className="flex flex-col gap-size0">
                    <span className="text-xs font-regular tracking-tight text-grey-50">
                      Enter URLs you wish to exclude from the added data.
                    </span>
                  </div>
                  {chatbotData.chatbotExcludedUrls.map((url, index) => (
                    <div key={index} className="flex flex-col gap-size4">
                      <div className="flex gap-size1">
                        <DInput
                          placeholder="Enter a valid URL"
                          icon={<LinkIcon />}
                          value={url.url}
                          onChange={(e) =>
                            handleChangeExcludeUrl(e.target.value, index)
                          }
                          className="exclude-url-input"
                        />
                        {chatbotData.chatbotExcludedUrls.length > 1 &&
                          index !== 0 && (
                            <button
                              className="dbutton bg-grey-2 rounded-size1 p-size1 w-12 h-12 flex items-center justify-center "
                              onClick={() => handleDeleteExcludeUrl(index)}
                            >
                              <DeleteIcon />
                            </button>
                          )}
                      </div>
                    </div>
                  ))}
                  <button
                    className="text-xs font-regular tracking-tight flex items-center gap-size0 p-size0 rounded-size2 max-w-40 w-auto hover:bg-grey-1"
                    onClick={() => handleAddExcludeUrl()}
                  >
                    <AddIcon height={16} width={16} className="mr-size0" />
                    <span>Exclude another URL</span>
                  </button>
                </div>
              </DSwitchAccordion>

              <DSwitchAccordion
                title="Bulk upload"
                switchOpen={isBulkUploadMode}
                onToggle={() => setIsBulkUploadMode(!isBulkUploadMode)}
                titleClassName="text-sm font-medium tracking-tight"
              >
                <div className="flex flex-col gap-size1 px-size0">
                  <span className="text-xs font-regular tracking-tight text-grey-50">
                    Upload URLs in bulk. Add text file with all URLs (one per
                    line).
                  </span>
                  <DUpload
                    title="Drop your file here"
                    subtitle="only one URL per line"
                    onChangeFile={(e) => handleUploadBulkUrls(e)}
                  />
                  {chatbotData.chatbotBulkUploadedUrls.length > 0 &&
                    chatbotData.chatbotBulkUploadedUrls.map((file, index) => (
                      <div key={index} className="flex flex-col gap-size0">
                        <div className="border-b border-b-grey5 p-size1 flex items-center justify-between">
                          <div className="flex gap-size1 items-center">
                            <FilesIcon className="w-[30px] h-[30px]" />
                            <div className="flex flex-col">
                              <span className="text-base font-medium tracking-tight">
                                {file.name}
                              </span>
                              <div className="flex gap-size0 items-center text-[10px] font-regular tracking-tight text-grey-20 h-[11px]">
                                <span className="truncate max-w-[300px]">
                                  {file.type}
                                </span>
                                <div className="w-px h-[11px] bg-grey-20"></div>
                                <span>
                                  {file.size >= 1024 * 1024
                                    ? `${(file.size / (1024 * 1024)).toFixed(
                                        2
                                      )} MB`
                                    : `${(file.size / 1024).toFixed(2)} KB`}
                                </span>
                              </div>
                            </div>
                          </div>
                          <button
                            className="dbutton"
                            onClick={() => handleDeleteBulkUrl(index)}
                          >
                            <DeleteIcon />
                          </button>
                        </div>
                      </div>
                    ))}
                </div>
              </DSwitchAccordion>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatbotKnowledge;
