// Visualization utilities for the AI Voice Demo

// Calculate circle size based on speaking state and audio levels
export const getCircleSize = (speaking, userAudioLevel, smoothedBotLevel, isCalling = false) => {
  const baseSize = 180;

  if (isCalling) {
    // Slightly larger base size during calling state
    return baseSize * 1.1;
  }

  const scale = speaking === 'user'
    ? 1 + userAudioLevel * 0.3
    : speaking === 'bot'
      ? 1 + smoothedBotLevel * 0.3
      : 1;
  return baseSize * scale;
};

// Get circle color based on speaking state
export const getCircleColor = (speaking, isCalling = false) => {
  if (isCalling) return '#3B82F6'; // Blue color for calling state
  if (speaking === 'user') return '#A78BFA'; // Light Purple
  if (speaking === 'bot') return '#8B5CF6';  // Purple
  return '#6B7280'; // Gray
};

// Get glow effect for the main circle with enhanced animation
export const getGlowEffect = (speaking, userAudioLevel, smoothedBotLevel, isCalling = false) => {
  if (isCalling) {
    return '0 0 30px rgba(59, 130, 246, 0.7), 0 0 50px rgba(59, 130, 246, 0.3)'; // Enhanced blue glow for calling
  } else if (speaking === 'user') {
    return `0 0 ${20 + userAudioLevel * 40}px rgba(167, 139, 250, 0.7), 0 0 ${40 + userAudioLevel * 60}px rgba(167, 139, 250, 0.3)`;
  } else if (speaking === 'bot') {
    return `0 0 ${20 + smoothedBotLevel * 40}px rgba(139, 92, 246, 0.7), 0 0 ${40 + smoothedBotLevel * 60}px rgba(139, 92, 246, 0.3)`;
  }
  return 'none';
};

// Get outer rings based on speaking state
export const getOuterRings = (speaking, userAudioLevel, smoothedBotLevel, getCircleSize, isCalling = false) => {
  const rings = [];
  const maxRings = isCalling ? 4 : 3;
  const transitionStyle = 'all 600ms cubic-bezier(0.34, 1.56, 0.64, 1)';
  const circleSize = getCircleSize(speaking, userAudioLevel, smoothedBotLevel, isCalling);

  if (isCalling) {
    // Create pulsing rings for calling state
    for (let i = 0; i < maxRings; i++) {
      rings.push({
        key: `calling-ring-${i}`,
        style: {
          width: `${circleSize + 40 + i * 50}px`,
          height: `${circleSize + 40 + i * 50}px`,
          background: `radial-gradient(circle, transparent 60%, rgba(59, 130, 246, ${0.3 - i * 0.06}))`,
          opacity: 1,
          animation: `pulse-${i + 1} 1.5s infinite ease-in-out`,
          animationDelay: `${i * 0.3}s`,
          transition: transitionStyle,
          willChange: 'width, height, opacity, transform'
        }
      });
    }
  } else if (speaking === 'user') {
    for (let i = 0; i < maxRings; i++) {
      rings.push({
        key: `user-ring-${i}`,
        style: {
          width: `${circleSize + 40 + i * 50}px`,
          height: `${circleSize + 40 + i * 50}px`,
          background: `radial-gradient(circle, transparent 60%, rgba(167, 139, 250, ${0.3 - i * 0.08}))`,
          opacity: userAudioLevel > 0.05 ? 1 : 0.3,
          transition: transitionStyle,
          willChange: 'width, height, opacity'
        }
      });
    }
  } else if (speaking === 'bot') {
    for (let i = 0; i < maxRings; i++) {
      rings.push({
        key: `bot-ring-${i}`,
        style: {
          width: `${circleSize + 40 + i * 50}px`,
          height: `${circleSize + 40 + i * 50}px`,
          background: `radial-gradient(circle, transparent 60%, rgba(139, 92, 246, ${0.3 - i * 0.08}))`,
          opacity: smoothedBotLevel > 0.05 ? 1 : 0.3,
          transition: transitionStyle,
          willChange: 'width, height, opacity'
        }
      });
    }
  }

  return rings;
};

// Define keyframes for the pulsing animation with smoother transitions
export const pulseKeyframes = `
  @keyframes pulse-1 {
    0% { transform: scale(0.95); opacity: 0.7; }
    50% { transform: scale(1.05); opacity: 0.3; }
    100% { transform: scale(0.95); opacity: 0.7; }
  }

  @keyframes pulse-2 {
    0% { transform: scale(0.9); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 0.2; }
    100% { transform: scale(0.9); opacity: 0.6; }
  }

  @keyframes pulse-3 {
    0% { transform: scale(0.85); opacity: 0.5; }
    50% { transform: scale(1.15); opacity: 0.1; }
    100% { transform: scale(0.85); opacity: 0.5; }
  }

  @keyframes pulse-4 {
    0% { transform: scale(0.8); opacity: 0.4; }
    50% { transform: scale(1.2); opacity: 0; }
    100% { transform: scale(0.8); opacity: 0.4; }
  }

  @keyframes wave {
    0% { transform: translateY(0); }
    25% { transform: translateY(-5px); }
    50% { transform: translateY(0); }
    75% { transform: translateY(5px); }
    100% { transform: translateY(0); }
  }

  @keyframes glow {
    0% { box-shadow: 0 0 10px rgba(139, 92, 246, 0.5); }
    50% { box-shadow: 0 0 25px rgba(139, 92, 246, 0.8); }
    100% { box-shadow: 0 0 10px rgba(139, 92, 246, 0.5); }
  }
`;

// Visualize audio levels
export const visualize = (
  analyserRef,
  botAnalyserRef,
  speaking,
  setUserAudioLevel,
  setSmoothedBotLevel,
  animationFrameRef,
  visualizeCallback
) => {
  if (!analyserRef.current) return;

  animationFrameRef.current = requestAnimationFrame(visualizeCallback);

  // User audio visualization
  const userDataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
  analyserRef.current.getByteFrequencyData(userDataArray);
  const userAverageVolume = userDataArray.reduce((acc, val) => acc + val, 0) / userDataArray.length;
  setUserAudioLevel(userAverageVolume / 128); // Normalize to 0-1

  // Bot audio visualization (if speaking state is 'bot')
  if (speaking === 'bot') {
    if (botAnalyserRef.current) {
      try {
        const botDataArray = new Uint8Array(botAnalyserRef.current.frequencyBinCount);
        botAnalyserRef.current.getByteFrequencyData(botDataArray);
        const botAverageVolume = botDataArray.reduce((acc, val) => acc + val, 0) / botDataArray.length;
        const rawBotLevel = botAverageVolume / 128; // Normalize to 0-1

        // Apply very light smoothing just like user animation
        setSmoothedBotLevel(prev => {
          // Quick response like user animation (70% new data)
          return prev * 0.3 + rawBotLevel * 0.7;
        });
      } catch (err) {
        console.warn('Bot analyzer error, using fallback animation');
        // Maintain current level with slight decay
        setSmoothedBotLevel(prev => Math.max(0.1, prev * 0.95));
      }
    } else {
      // No analyzer - maintain a moderate level
      setSmoothedBotLevel(prev => Math.max(0.1, prev * 0.95));
    }
  }
};
