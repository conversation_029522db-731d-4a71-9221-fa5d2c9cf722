import { useState } from 'react';
import * as authService from '@/services/auth.service';
import validateEmail from '@/helpers/validateEmail';
import EyeIcon from '@/components/Global/Icons/EyeIcon';
import EyeClosedIcon from '@/components/Global/Icons/EyeClosedIcon';
import { useUserStore } from '@/stores/user/userStore';
import { Link, useNavigate } from 'react-router-dom';
import { trackFirstLogin } from '@/helpers/analytics';

// SVG Icons as inline components
const GoogleIcon = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.8 9.2C17.8 8.4 17.7 7.8 17.6 7.1H9.1V10.2H14C13.8 11.2 13.2 12 12.3 12.5V14.3H15.1C16.8 12.9 17.8 11.2 17.8 9.2Z"
      fill="#4285F4"
    />
    <path
      d="M9.1 18C11.4 18 13.3 17.2 14.7 15.8L11.9 14C11.2 14.5 10.3 14.8 9.1 14.8C6.7 14.8 4.7 13.2 4 11H1.1V13C2.8 16.1 5.8 18 9.1 18Z"
      fill="#34A853"
    />
    <path
      d="M4 11C3.8 10.4 3.7 9.7 3.7 9C3.7 8.3 3.8 7.6 4 7V5H1.1C0.4 6.2 0 7.6 0 9C0 10.4 0.4 11.8 1.1 13L4 11Z"
      fill="#FBBC05"
    />
    <path
      d="M9.1 3.2C10.4 3.2 11.6 3.7 12.5 4.5L15 2C13.5 0.8 11.4 0 9.1 0C5.8 0 2.8 1.9 1.1 5L4 7C4.7 4.8 6.7 3.2 9.1 3.2Z"
      fill="#EA4335"
    />
  </svg>
);

const AppleIcon = () => (
  <svg
    width="16"
    height="18"
    viewBox="0 0 16 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.6512 9.47411C12.6347 7.25389 14.5303 6.17008 14.6129 6.12092C13.4883 4.45389 11.6965 4.23103 11.1209 4.21308C9.54353 4.04758 8.02000 5.15933 7.22235 5.15933C6.40706 5.15933 5.17882 4.23103 3.86000 4.26695C2.16235 4.30286 0.590588 5.28581 -0.173647 6.79231C-1.75718 9.85667 -0.0401176 14.3747 1.46941 16.9337C2.47412 18.1847 3.65647 19.5933 5.19765 19.5394C6.69353 19.4854 7.28000 18.5928 9.06353 18.5928C10.83 18.5928 11.3835 19.5394 12.9476 19.5035C14.5653 19.4854 15.5841 18.2405 16.5529 16.9756C17.7065 15.525 18.1729 14.1104 18.1905 14.0384C18.1553 14.0205 15.2271 12.9626 15.1918 9.47411H12.6512Z"
      fill="black"
    />
    <path
      d="M10.29 2.86344C11.0877 1.88852 11.64 0.543525 11.4753 -0.835938C10.3487 -0.781319 8.96459 -0.0681944 8.13165 0.871781C7.38753 1.73211 6.72941 3.13149 6.91176 4.45693C8.18459 4.54723 9.45747 3.82041 10.29 2.86344Z"
      fill="black"
    />
  </svg>
);

const MicrosoftIcon = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M8.5 8.5H0V0H8.5V8.5Z" fill="#F25022" />
    <path d="M18 8.5H9.5V0H18V8.5Z" fill="#7FBA00" />
    <path d="M8.5 18H0V9.5H8.5V18Z" fill="#00A4EF" />
    <path d="M18 18H9.5V9.5H18V18Z" fill="#FFB900" />
  </svg>
);

const EmailIcon = () => (
  <svg
    width="20"
    height="16"
    viewBox="0 0 20 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 0H2C0.9 0 0.00999999 0.9 0.00999999 2L0 14C0 15.1 0.9 16 2 16H18C19.1 16 20 15.1 20 14V2C20 0.9 19.1 0 18 0ZM18 4L10 9L2 4V2L10 7L18 2V4Z"
      fill="white"
    />
  </svg>
);

const LoginForm = ({
  email,
  setEmail,
  password,
  setPassword,
  handleGoogleLogIn,
  goToOTP,
  goToForgotPassword,
  loading,
  setLoading,
  error,
  setError,
}) => {
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const saveAuthDetail = useUserStore((state) => state.saveAuthDetail);
  const navigate = useNavigate();

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const validateForm = () => {
    let isValid = true;

    // Email validation
    if (!email || email.trim() === '') {
      setEmailError('Email is required');
      isValid = false;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    } else {
      setEmailError('');
    }

    // Password validation
    if (!password || password.trim() === '') {
      setPasswordError('Password is required');
      isValid = false;
    } else {
      setPasswordError('');
    }

    return isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      const payload = {
        username: email,
        password: password,
      };

      const response = await authService.login(payload);

      if (response && response.status === 200) {
        // Save auth details to store
        saveAuthDetail({
          access_token: response.data.access_token,
          user_id: response.data.user_id,
          first_name: response.data.first_name,
          last_name: response.data.last_name,
          date_created: response.data.date_created,
          login_count: response.data.login_count || 0,
        });

        // if (response.data.login_count === 2) {
        //   trackFirstLogin({
        //     user_id: response.data.user_id,
        //     email: email,
        //     signup_method: 'Email'
        //   });
        // }

        // Use React Router's navigate instead of window.location
        navigate('/');
      } else if (response && response.status === 428) {
        // Need 2FA verification
        goToOTP();
      } else if (
        response &&
        response.data &&
        response.data.login_count === -1
      ) {
        navigate('/change-password');
      }
    } catch (err) {
      console.error(err);
      // Check for 401 unauthorized error
      if (err.response && err.response.status === 401) {
        setError('Invalid credentials.');
      } else if (err.response && err.response.status === 428) {
        // Need 2FA verification
        goToOTP();
      }
      // Check if error message starts with "Invalid credentials"
      else if (
        err.response?.data?.detail &&
        err.response.data.detail.startsWith('Invalid credentials')
      ) {
        setError('Invalid credentials.');
      }
      // Handle other errors
      else {
        setError(err.response?.data?.detail || 'Error logging in');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSocialSignIn = (provider) => {
    if (provider === 'google') {
      handleGoogleLogIn();
    }
    // Apple and Microsoft implementations would go here
  };

  return (
    <div className="bg-white rounded-t-3xl shadow-lg p-7 mx-2 md:mx-0">
      {/* Header */}
      <div className="text-center mb-6 pt-4 animate-fadeInUpDelayed1">
        <h1 className="text-2xl font-medium mb-2">Welcome back</h1>
        <p className="text-gray-500 text-sm">
          Log in to your account and start building
        </p>
      </div>

      {/* Social Login Buttons */}
      <div className="space-y-3 mb-6 animate-fadeInUpDelayed2">
        <button
          onClick={() => handleSocialSignIn('google')}
          className="flex items-center justify-center w-full py-3 px-4 border border-gray-200 rounded-lg text-gray-700 font-medium text-base transition hover:bg-gray-50"
          disabled={loading}
        >
          <span className="mr-3">
            <GoogleIcon />
          </span>
          Sign in with Google
        </button>

        {/* <button
          onClick={() => handleSocialSignIn('apple')}
          className="flex items-center justify-center w-full py-3 px-4 border border-gray-200 rounded-lg text-gray-700 font-medium text-base transition hover:bg-gray-50"
          disabled={loading}
        >
          <span className="mr-3"><AppleIcon /></span>
          Sign in with Apple
        </button> */}

        {/* <button
          onClick={() => handleSocialSignIn('microsoft')}
          className="flex items-center justify-center w-full py-3 px-4 border border-gray-200 rounded-lg text-gray-700 font-medium text-base transition hover:bg-gray-50"
          disabled={loading}
        >
          <span className="mr-3"><MicrosoftIcon /></span>
          Sign in with Microsoft
        </button> */}
      </div>

      {/* Divider */}
      <div className="flex items-center mb-5">
        <div className="flex-grow border-t border-gray-200"></div>
        <span className="px-4 text-gray-400 text-sm font-medium">OR</span>
        <div className="flex-grow border-t border-gray-200"></div>
      </div>

      {/* Email Form */}
      <div className="mb-5 animate-fadeInUpDelayed3">
        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <input
              type="email"
              placeholder="Email address"
              className={`w-full py-3 px-4 border ${
                emailError ? 'border-red-500' : 'border-gray-200'
              } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-gray-700 text-min-safe-input`}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
            <div className="h-5 mt-1">
              {emailError && (
                <p className="text-red-500 text-xs">{emailError}</p>
              )}
            </div>
          </div>

          <div className="mb-3">
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                placeholder="Password"
                className={`w-full py-3 px-4 border ${
                  passwordError ? 'border-red-500' : 'border-gray-200'
                } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-gray-700 text-min-safe-input`}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none p-1"
                onClick={togglePasswordVisibility}
                tabIndex="-1"
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                <span className="flex items-center justify-center w-5 h-5">
                  {showPassword ? <EyeIcon /> : <EyeClosedIcon />}
                </span>
              </button>
            </div>
            <div className="h-5 mt-1">
              {passwordError && (
                <p className="text-red-500 text-xs">{passwordError}</p>
              )}
            </div>
          </div>

          <div className="flex justify-end mb-4">
            <Link
              to="/forgot-password"
              className="text-indigo-600 text-sm hover:underline disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              Forgot password?
            </Link>
          </div>

          <button
            type="submit"
            className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2 hover:bg-indigo-700 transition"
            disabled={loading}
          >
            {loading ? (
              <svg
                className="animate-spin h-5 w-5 mr-2"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            ) : (
              <EmailIcon />
            )}
            Sign in with email
          </button>

          <div className="h-6 mt-3">
            {error && (
              <p className="text-red-500 text-sm text-center animate-fadeIn">
                {error}
              </p>
            )}
          </div>
        </form>
      </div>

      {/* Terms */}
      <div className="text-center text-xs text-gray-500 mb-2">
        By proceeding, you agree to our{' '}
        <a
          href="https://www.dante-ai.com/terms-of-service"
          target="_blank"
          className="text-indigo-600 hover:underline"
        >
          Terms
        </a>{' '}
        and{' '}
        <a
          href="https://www.dante-ai.com/privacy-policy"
          target="_blank"
          className="text-indigo-600 hover:underline"
        >
          Privacy Policy
        </a>
      </div>
    </div>
  );
};

export default LoginForm;
