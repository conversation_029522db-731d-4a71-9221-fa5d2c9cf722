/**
 * WebSocket connection states
 */
export const WS_STATES = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
};

/**
 * WebSocket configuration defaults
 */
export const WS_CONFIG = {
  MAX_RETRIES: 5,
  RETRY_DELAY_MS: 1000,
  CONNECTION_TIMEOUT_MS: 5000,
  HEARTBEAT_INTERVAL_MS: 30000
};

/**
 * WebSocket event types
 */
export const WS_EVENTS = {
  OPEN: 'open',
  MESSAGE: 'message',
  CLOSE: 'close',
  ERROR: 'error'
};

/**
 * WebSocket close codes
 * @see https://developer.mozilla.org/en-US/docs/Web/API/CloseEvent/code
 */
export const WS_CLOSE_CODES = {
  NORMAL_CLOSURE: 1000,
  GOING_AWAY: 1001,
  PROTOCOL_ERROR: 1002,
  UNSUPPORTED_DATA: 1003,
  NO_STATUS: 1005,
  ABNORMAL_CLOSURE: 1006,
  INVALID_FRAME_PAYLOAD_DATA: 1007,
  POL<PERSON>Y_VIOLATION: 1008,
  MESSAGE_TOO_BIG: 1009,
  MISSING_EXTENSION: 1010,
  INTERNAL_ERROR: 1011,
  SERVICE_RESTART: 1012,
  TRY_AGAIN_LATER: 1013,
  BAD_GATEWAY: 1014,
  TLS_HANDSHAKE: 1015
};
