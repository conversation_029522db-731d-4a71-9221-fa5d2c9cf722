import { DANTE_THEME_CHAT, SYSTEM_FONTS } from '@/constants';

const generateGoogleFonts = (fontName) => {
  if (SYSTEM_FONTS.includes(fontName)) {
    return '';
  }
  if (fontName === 'Default' || !fontName) {
    fontName = DANTE_THEME_CHAT.font_name;
  }
  const url = `https://fonts.googleapis.com/css2?family=${fontName?.replaceAll(' ', '+')}:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap`;
  return url;
};

export default generateGoogleFonts;
