@keyframes glow {
    0% {
      box-shadow: 0 0 15px rgba(128, 112, 242, 0.4), 0 0 30px rgba(147, 112, 219, 0.3), 0 0 45px rgba(138, 43, 226, 0.2);
    }
    25% {
      box-shadow: 0 0 30px rgba(106, 90, 205, 0.5), 0 0 60px rgba(123, 104, 238, 0.4), 0 0 90px rgba(147, 112, 219, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(72, 61, 139, 0.5), 0 0 60px rgba(65, 105, 225, 0.4), 0 0 90px rgba(0, 0, 255, 0.3);
    }
    75% {
      box-shadow: 0 0 30px rgba(138, 43, 226, 0.5), 0 0 60px rgba(148, 0, 211, 0.4), 0 0 90px rgba(153, 50, 204, 0.3);
    }
    100% {
      box-shadow: 0 0 15px rgba(128, 112, 242, 0.4), 0 0 30px rgba(147, 112, 219, 0.3), 0 0 45px rgba(138, 43, 226, 0.2);
    }
  }

  @keyframes glow<PERSON><PERSON>ald {
    0%, 25% {
      box-shadow: 0 0 15px rgba(128, 112, 242, 0.4), 0 0 30px rgba(147, 112, 219, 0.3), 0 0 45px rgba(138, 43, 226, 0.2);
    }
    100% {
      box-shadow: 0 0 30px rgba(51, 192, 142, 0.5), 0 0 60px rgba(51, 192, 142, 0.4), 0 0 90px rgba(51, 192, 142, 0.3);
    }
  }

  /* Smoother glow effect using CSS variables and transitions */
  .glow-effect {
    position: relative;
    z-index: 1;
    --glow-shadow: 0 0 15px rgba(128, 112, 242, 0.4), 0 0 30px rgba(147, 112, 219, 0.3), 0 0 45px rgba(138, 43, 226, 0.2);
    box-shadow: var(--glow-shadow);
    transition: box-shadow 0.3s cubic-bezier(0.4,0,0.2,1);
    animation: glow 4s ease-in-out infinite;
    will-change: box-shadow;
  }

  .glow-effect::before {
    content: "";
    position: absolute;
    inset: 0;
    z-index: -1;
    border-radius: inherit;
    box-shadow: 0 0 60px 20px rgba(128, 112, 242, 0.4); /* expanded shadow */
    transition: box-shadow 0.4s cubic-bezier(0.4,0,0.2,1);
    pointer-events: none;
  }

  .glow-effect:hover {
    --glow-shadow: 0 0 30px rgba(51, 192, 142, 0.5), 0 0 60px rgba(51, 192, 142, 0.4), 0 0 90px rgba(51, 192, 142, 0.3);
    animation: none;
  }

  .glow-effect:hover::before {
    box-shadow: 0 0 120px 40px rgba(51, 192, 142, 0.5); /* expanded emerald shadow */
  }

  /* Add styles for the icon gradient transition */
  .icon-transition stop {
    transition: stop-color 0.3s ease-in-out;
  }

  .glow-effect:hover .icon-transition stop:first-child {
    stop-color: rgb(51, 192, 142);
  }

  .glow-effect:hover .icon-transition stop:last-child {
    stop-color: rgb(51, 192, 142);
  }
