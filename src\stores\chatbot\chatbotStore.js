import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { createSteps, customizeSteps } from '@/components/Chatbot/Create/steps';
import { LLM_MODEL_DEFAULT } from '@/constants';


const initialState = {
  chatbots: [],
  selectedChatbot: { llmModel: LLM_MODEL_DEFAULT },
  steps: {
    createSteps: createSteps
    // customizeSteps: customizeSteps
  },
  currentStep: 0,
  conversation: {
    chatbotId: null,
    isNew: false
  },
  saveSignUpBtn: false
};

export const useChatbotStore = create(
  persist(
    (set) => ({
      ...initialState,
      setSelectedName: (selectedName) =>
        set((state) => ({
          selectedChatbot: {
            ...state.selectedChatbot,
            name: selectedName,
            knowledge_base: { ...state.selectedChatbot.knowledge_base, knowledge_base_name: selectedName }
          }
        })),
      setChatbots: (chatbots) => set({ chatbots }),
      setSelectedChatbot: (selectedChatbot) => set({ selectedChatbot }),
      setCurrentStep: (stepId) =>
        set((state) => {
          const updatedSteps = {
            ...state.steps,
            createSteps: state.steps.createSteps.map((step) => ({
              ...step,
              active: step.id === stepId
            }))
          };
          return { currentStep: stepId, steps: updatedSteps };
        }),
      updateStep: (stepId, updates) =>
        set((state) => ({
          steps: {
            ...state.steps,
            createSteps: state.steps.createSteps.map((step) =>
              step.id === stepId ? { ...step, ...updates } : step
            )
          }
        })),
      setSelectedLLModel: (selectedLLM) =>
        set((state) => ({
          selectedChatbot: {
            ...state.selectedChatbot,
            llmModel: selectedLLM,
            last_model_used: selectedLLM
          }
        })),

      resetChatbots: () => set({ chatbots: [] }),
      resetSelectedChatbot: () => set({ selectedChatbot: { llmModel: LLM_MODEL_DEFAULT.value } }),
      resetCurrentStep: () => set({ currentStep: 0 }),
      reset: () => set({ ...initialState }),
      setSaveSignUpBtn: (saveSignUpBtn) => set({ saveSignUpBtn })
    }),
    {
      name: 'chatbot-store',
      storage: createJSONStorage(() => localStorage)
    }
  )
);
