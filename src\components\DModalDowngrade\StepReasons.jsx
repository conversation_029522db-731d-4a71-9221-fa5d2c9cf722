import { useUserStore } from '@/stores/user/userStore';
import { downgradeStepsEnum } from './stepDowngrade';
import { useEffect, useState } from 'react';
import useDanteApi from '@/hooks/useDanteApi';
import { sendDowngradeFeedback } from '@/services/plans.service';
import CloseIcon from '../Global/Icons/CloseIcon';
import DButton from '../Global/DButton';
import { Field, Label, Radio, RadioGroup } from '@headlessui/react';
import DTransition from '../Global/DTransition';
import DTextArea from '../Global/DInput/DTextArea';
import QuestionMarkIcon from '../Global/Icons/QuestionMarkIcon';

const REASONS_OPTIONS = [
  {
    id: 'thought_something_else',
    label: 'I thought it does something else',
    title: 'What confused you?',
    description:
      'Please provide us with some extra clarity on what confused you and what you thought Dante AI will do.',
    placeholder: 'Type your answer here...',
  },
  {
    id: 'temporary_subscription',
    label: 'It\'s temporary, I will subscribe again',
    title: '',
    description: '',
    placeholder: 'Type your answer here...',
  },
  {
    id: 'missing_features',
    label: 'It\'s missing features',
    title: 'What feature was missing?',
    description:
      'Please provide us with some extra clarity on what feature you were missing, so we can improve.',
    placeholder: 'Type your answer here...',
  },
  {
    id: 'switching_competitor',
    label: 'I\'m switching to a competitor',
    title: 'Switching to a competitor? Which one?',
    description: '',
    placeholder: 'Competitor\'s name',
  },
  {
    id: 'budget_constraints',
    label: 'No longer fits into my budget',
    title: '',
    description: '',
    placeholder: 'Type your answer here...',
  },
  {
    id: 'other',
    label: 'Other',
    title: 'Please share a reason',
    description: 'Please provide us with more information.',
    placeholder: 'Type your answer here...',
  },
];

const StepReasons = ({
  handleNextStep,
  currentStep,
  setStep,
  setTitle,
  new_tier,
  handleClose,
}) => {
  const { user } = useUserStore();
  const [selectedReason, setSelectedReason] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [textDetails, setTextDetails] = useState('');
  const [loading, setLoading] = useState(false);
  const [canContinue, setCanContinue] = useState(false);

  const handleNext = async () => {
    // handleNextStep(downgradeStepsEnum.REASONS);
    try {
      setLoading(true);
      const response = await sendDowngradeFeedback({
        current_tier: user.tier_type,
        new_tier,
        user_id: user.id,
        feedback: `${selectedReason?.label} - ${textDetails}`,
      });
      if (response.status === 200) {
        handleNextStep(downgradeStepsEnum.SALE_OFFER);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleReasonChange = (reason) => {
    setSelectedReason(reason);
    if (
      reason.id === 'other' ||
      reason.id === 'switching_competitor' ||
      reason.id === 'missing_features' ||
      reason.id === 'thought_something_else'
    ) {
      setShowDetails(true);
    } else {
      setShowDetails(false);
    }
  };

  useEffect(() => {
    const hasSelectedReason = !!selectedReason;

    const hasValidDetails =
      !showDetails || (showDetails && textDetails.length > 0);

    setCanContinue(hasSelectedReason && hasValidDetails);
  }, [selectedReason, showDetails, textDetails]);

  useEffect(() => {
    setTitle('');
  }, []);

  return (
    <div className="flex flex-col gap-size4">
      <div className="absolute top-[-45px] left-0 right-0 -mt-14 bg-grey-5 rounded-t-size1 border border-grey-5 border-b-0 flex items-center py-size1 px-size3">
        <div className="flex items-center gap-size2">
          <div className="bg-purple-5 rounded-full p-[6px] flex items-center justify-center">
            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-purple-200">
              <QuestionMarkIcon className="w-4 h-4 text-white" color={'#FFFFFF'}/>
            </div>
          </div>
          <span className="font-medium text-sm">Tell Us Why</span>
        </div>
        <button
          onClick={handleClose}
          className="ml-auto text-grey-50 hover:text-grey-75 text-xl font-bold"
          aria-label="Close"
        >
          ×
        </button>
      </div>

      <div className="flex flex-col gap-size3">
        <RadioGroup
          value={selectedReason}
          onChange={handleReasonChange}
          className="flex flex-col gap-size2"
        >
          {REASONS_OPTIONS.map((option) => (
            <Field
              key={option.id}
              className="flex items-center gap-size2 cursor-pointer"
            >
              <Radio
                key={option.id}
                value={option}
                className="transition-all duration-300 group flex size-5 items-center justify-center rounded-full border bg-white data-[checked]:bg-black"
              >
                <span className="transition-all duration-300 invisible size-2 rounded-full bg-white group-data-[checked]:visible" />
              </Radio>
              <Label className="text-sm cursor-pointer">{option.label}</Label>
            </Field>
          ))}
        </RadioGroup>
      </div>

      <div className="h-px bg-grey-5" />

      <DTransition show={showDetails}>
        <div className="flex flex-col gap-size1">
          <h3 className="font-semibold">{selectedReason?.title}</h3>
          {selectedReason?.description && (
            <p className="text-xs text-grey-50">
              {selectedReason?.description}
            </p>
          )}
          <DTextArea
            placeholder={selectedReason?.placeholder}
            value={textDetails}
            onChange={(e) => setTextDetails(e.target.value)}
          />
        </div>
      </DTransition>

      <footer className="flex flex-col gap-size2">
        <DButton
          variant="grey"
          size="lg"
          onClick={handleNext}
          fullWidth
          disabled={!canContinue}
          loading={loading}
        >
          Continue canceling
        </DButton>
        <DButton
          variant="outlined"
          size="lg"
          onClick={handleClose}
          fullWidth
          disabled={loading}
        >
          I don't want to cancel
        </DButton>
      </footer>
    </div>
  );
};

export default StepReasons;
