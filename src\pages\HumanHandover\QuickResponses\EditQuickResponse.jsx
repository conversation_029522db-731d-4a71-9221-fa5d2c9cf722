import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DInputBlock from '@/components/Global/DInput/DInputBlock';
import DTextArea from '@/components/Global/DInput/DTextArea';
import DModal from '@/components/Global/DModal';
import { useEffect, useState } from 'react';

const EditQuickResponse = ({
  open,
  onClose,
  handleEditQuickResponse,
  response,
  isLoading,
}) => {
  const [responseTitle, setResponseTitle] = useState(response?.title || '');
  const [responseContent, setresponseContent] = useState(
    response?.content || ''
  );

  const [error, setError] = useState({});

  const validate = () => {
    let newHasError = false;
    const newError = {};
    if (!responseTitle) {
      newError.responseTitle = 'Title is required';
      newHasError = true;
    }
    if (!responseContent) {
      newError.responseContent = 'Content is required';
      newHasError = true;
    }
    setError(newError);
    return newHasError;
  };

  const handleClickEdit = () => {
    const hasError = validate();

    if (hasError) {
      return;
    }
    if (!validate()) {
      handleEditQuickResponse(responseTitle, responseContent);
    }
  };

  useEffect(() => {
    setResponseTitle(response?.title || '');
    setresponseContent(response?.content || '');
  }, [response]);

  return (
    <DModal
      title="Edit quick response"
      isOpen={open}
      onClose={onClose}
      footer={
        <div className="flex gap-size1 w-full">
          <DButton variant="light" fullWidth onClick={onClose}>
            Cancel
          </DButton>
          <DButton variant="dark" fullWidth onClick={handleClickEdit} loading={isLoading}>
            Save
          </DButton>
        </div>
      }
    >
      <div className="flex flex-col gap-size5">
        <DInputBlock label="Response name">
          <DInput
            placeholder="Enter quick response name"
            fullWidth
            value={responseTitle}
            error={error.responseTitle}
            onChange={(e) => {
              setResponseTitle(e.target.value);
              setError({
                ...error,
                responseTitle: '',
              });
            }}
          />
        </DInputBlock>
        <DInputBlock label="Response content">
          <DTextArea
            placeholder="Enter quick response content"
            fullWidth
            value={responseContent}
            error={error.responseContent}
            onChange={(e) => {
              setresponseContent(e.target.value);
              setError({
                ...error,
                responseContent: '',
              });
            }}
          />
        </DInputBlock>
      </div>
    </DModal>
  );
};

export default EditQuickResponse;
