import React, { useEffect, useRef, useState } from "react";

import DButton from "../DButton";
import clsx from "clsx";
import { Transition } from "@headlessui/react";
import UploadArrowIcon from "../Icons/UploadArrowIcon";

const DUpload = React.forwardRef(
  (
    {
      title,
      subtitle,
      note,
      onChangeFile,
      multiple = false,
      accept,
      name,
      id,
      btnClassName,
    },
    ref
  ) => {
    const [dragActive, setDragActive] = useState(false);

    const handleDrag = (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.type === "dragenter" || e.type === "dragover") {
        setDragActive(true);
      } else if (e.type === "dragleave") {
        setDragActive(false);
      }
    };

    const handleDrop = (e) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);
      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        onChangeFile({
          target: {
            files: e.dataTransfer.files,
          },
        });
      }
    };

    const refUpload = useRef(ref);

    useEffect(() => {
      document.addEventListener("dragover", handleDrag);
      document.addEventListener("dragleave", handleDrag);
      document.addEventListener("drop", handleDrop);

      return () => {
        document.removeEventListener("dragover", handleDrag);
        document.removeEventListener("dragleave", handleDrag);
        document.removeEventListener("drop", handleDrop);
      };
    }, []);

    return (
      <div
        onClick={() => refUpload?.current.click()}
        className={clsx(
          "w-full px-10 py-12 border border-grey-10 rounded-[12px] flex flex-col justify-center items-center text-center gap-3 relative overflow-hidden bg-white dark:bg-[#212121] cursor-pointer hover:bg-grey-5 dark:hover:bg-grey-10 transition-colors",
          {
            "border-dashed border-grey-20": dragActive,
          }
        )}
      >
        <Transition show={dragActive}>
          <div className="transition-all duration-300 absolute inset-0 bg-white/80 dark:bg-[#212121]/80 flex justify-center items-center">
            <div className="flex flex-col justify-center items-center text-center gap-size1">
              <span className="text-base font-medium tracking-tight">
                Drop files here
              </span>
            </div>
          </div>
        </Transition>

        <UploadArrowIcon />
        <span className="text-base font-medium">Upload or drop</span>

        <input
          ref={refUpload}
          type="file"
          name={name}
          id={id}
          className="hidden"
          onChange={onChangeFile}
          multiple={multiple}
          accept={accept}
        />
      </div>
    );
  }
);

DUpload.displayName = "DUpload";

export default DUpload;
