const mimeTypeToLabel = {
    'application/pdf': 'PDF',
    'image/jpeg': 'JPEG',
    'image/png': 'PNG',
    'text/plain': 'Text',
    'application/msword': 'Word',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
    'application/vnd.ms-excel': 'Excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',
    'application/zip': 'ZIP',
  };
  
  export const getFriendlyFileType = (mimeType) => {
    return mimeTypeToLabel[mimeType] || mimeType.split('/')[1].toUpperCase();
  };
  