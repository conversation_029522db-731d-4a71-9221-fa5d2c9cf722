import { useEffect, useRef, useState, useCallback } from 'react';

import ChatbotKnowledge from '@/components/Chatbot/Create/ChatbotKnowledge';
import ChatbotName from '@/components/Chatbot/Create/ChatbotName';
import ChatbotReview from '@/components/Chatbot/Create/ChatbotReview';
import { StepEnum } from '@/components/Chatbot/Create/StepEnum';
import { createSteps } from '@/components/Chatbot/Create/steps';
import TrainingFinished from '@/components/Chatbot/Create/TrainingFinished';
import TrainingInProgress from '@/components/Chatbot/Create/TrainingInProgress';
import Personality from '@/components/Chatbot/Personality';
import {
  MAX_URLS_PER_ENTIRE_DOMAIN,
  TASK_POLLING_INTERVAL,
  TASK_TYPES,
} from '@/constants';
import useTaskHandler from '@/hooks/useTaskHandler';
import * as chatbotService from '@/services/chatbot.service';
import * as userService from '@/services/user.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useTaskStore from '@/stores/task/taskStore';
import { useUserStore } from '@/stores/user/userStore';
import { useCreateChatbotStore } from '@/stores/chatbot/createChatbotStore';
import useToast from '@/hooks/useToast';
import { useNavigate } from 'react-router-dom';
import {
  trackChatbotTraining,
  trackFirstChatbotCreation,
} from '@/helpers/analytics';
import checkEnv from '@/helpers/environmentCheck';

const Creation = ({
  tempCustomizationData,
  setTempCustomizationData,
  setProgressBar,
  knowledgeBase,
  setKnowledgeBase,
  isTraining,
  setIsTraining,
  trainingFinished,
  setTrainingFinished,
  errors,
  saveChanges,
  setLastStep,
  personalities,
  chatbotData,
  setChatbotData,
  setPendingUpload,
  onNextClick,
  onBackClick,
  onTrainClick,
  setUnsavedChanges,
  setSaveChanges,
}) => {
  const navigate = useNavigate();
  //store
  const steps = useChatbotStore((state) => state.steps);
  const currentStep = useChatbotStore((state) => state.currentStep);
  const setCurrentStep = useChatbotStore((state) => state.setCurrentStep);
  const setUser = useUserStore((state) => state.setUser);
  const resetChatbotData = useCreateChatbotStore(
    (state) => state.resetChatbotData
  );
  const chatbots = useChatbotStore((state) => state.chatbots);

  //form data
  const formData = new FormData();
  const user = useUserStore((state) => state.user);

  //tab
  const [activeTab, setActiveTab] = useState('urls');

  //task
  const [taskId, setTaskId] = useState(null);
  const { tasks, removeTask } = useTaskStore();
  const { handleTask } = useTaskHandler();

  const [processTrainingMessage, setProcessTrainingMessage] = useState('');
  const [progressTrainingText, setProgressTrainingText] = useState('');
  const [whenTrainingStarted, setWhenTrainingStarted] = useState(null);

  const isIncrementingProgressIntervalRef = useRef(null);
  const uploadingContentIntervalRef = useRef(null);
  const finishingTrainingIntervalRef = useRef(null);

  const progressTraining = useRef(0);
  const [progressTrainingPercentage, setProgressTrainingPercentage] =
    useState(0);
  const [selectedPersonality, setSelectedPersonality] = useState(null);

  const percentageIncrement = 0.005;

  const { addErrorToast, removeToast } = useToast();
  //functions
  const handleCreateChatbotAll = async () => {
    try {
      setPendingUpload(true);
      const filteredUrls = chatbotData.chatbotUrls
        .filter((item) => item.url && item.url !== 'https://')
        .map((item) => item.url);
      const filteredMaxUrls = chatbotData.chatbotUrls
        .filter((item) => item.url && item.url !== 'https://')
        .map((item) =>
          item.sweepEntireDomain ? MAX_URLS_PER_ENTIRE_DOMAIN : item.max_urls
        );
      const filteredExcludedUrls = chatbotData.chatbotExcludedUrls
        .filter((item) => item.url && item.url !== 'https://')
        .map((item) => item.url);

      return await chatbotService.createChatbotAll(
        filteredUrls,
        filteredMaxUrls,
        filteredExcludedUrls,
        chatbotData.chatbotName,
        formData,
        chatbotData.personalities_template,
        chatbotData.base_system_prompt,
        chatbotData.allowAccessToInternet,
        chatbotData.temperature / 100
      );
    } catch (error) {
      console.log(error);
      setCurrentStep(StepEnum.REVIEW_CREATE);
      setSaveChanges(false);
      resetProgressTraining();
      setIsTraining(false);
    } finally {
      setPendingUpload(false);
    }
  };

  const handleCreateChatbotUrl = async () => {
    try {
      setPendingUpload(true);
      const filteredUrls = chatbotData.chatbotUrls
        .filter((item) => item.url && item.url !== 'https://')
        .map((item) => {
          return {
            url: item.url,
            max_urls: item.sweepEntireDomain ? MAX_URLS_PER_ENTIRE_DOMAIN : 1,
          };
        });
      const filteredExcludedUrls = chatbotData.chatbotExcludedUrls
        .filter((item) => item.url && item.url !== 'https://')
        .map((item) => {
          return { url: item.url, max_urls: 1 };
        });

      return await chatbotService.createChatbotUrl(
        filteredUrls,
        filteredExcludedUrls,
        chatbotData.chatbotName,
        user.id,
        chatbotData.personalities_template,
        chatbotData.base_system_prompt,
        chatbotData.allowAccessToInternet,
        chatbotData.temperature / 100
      );
    } catch (error) {
      console.log(error);
      setCurrentStep(StepEnum.REVIEW_CREATE);
      setIsTraining(false);
      resetProgressTraining();
    } finally {
      setPendingUpload(false);
    }
  };

  const handleCreateChatbotFiles = async () => {
    try {
      setPendingUpload(true);
      return await chatbotService.createChatbotFile(
        chatbotData.chatbotName,
        formData,
        user.id,
        chatbotData.personalities_template,
        chatbotData.base_system_prompt,
        chatbotData.allowAccessToInternet,
        chatbotData.temperature / 100
      );
    } catch (error) {
      console.log(error);
      setCurrentStep(StepEnum.REVIEW_CREATE);
      setSaveChanges(false);
      resetProgressTraining();
      setIsTraining(false);
    } finally {
      setPendingUpload(false);
    }
  };

  const handleSaveChanges = async () => {
    handleProgressTraining(0);
    chatbotData.chatbotUrls.length > 0 &&
      chatbotData.chatbotUrls.forEach((item) => {
        if (item.url && item.url !== 'https://') {
          formData.append('urls', item.url);
        }
      });

    chatbotData.chatbotFiles.forEach((file) => {
      formData.append('files', file);
    });

    try {
      let response;
      setIsTraining(true);
      setCurrentStep(null);
      setWhenTrainingStarted(new Date());
      handleProgressTraining(0.01);
      setProcessTrainingMessage('AI Chatbot files uploading...');
      setProgressTrainingText('');

      uploadingContentIntervalRef.current = setInterval(() => {
        if (progressTraining.current < 0.25) {
          handleProgressTraining(
            progressTraining.current + percentageIncrement
          );
        } else {
          clearInterval(uploadingContentIntervalRef.current);
        }
      }, TASK_POLLING_INTERVAL);

      if (
        chatbotData.chatbotUrls.filter(
          (item) => item.url && item.url !== 'https://'
        ).length > 0 &&
        chatbotData.chatbotFiles.length > 0
      ) {
        response = await handleCreateChatbotAll();
      } else if (
        chatbotData.chatbotUrls.filter(
          (item) => item.url && item.url !== 'https://'
        ).length > 0
      ) {
        response = await handleCreateChatbotUrl();
      } else if (
        chatbotData.chatbotFiles.length > 0 &&
        chatbotData.chatbotUrls.filter(
          (item) => item.url && item.url !== 'https://'
        ).length === 0
      ) {
        response = await handleCreateChatbotFiles();
      }

      if (response?.status === 200) {
        setLastStep(false);
        clearInterval(uploadingContentIntervalRef.current);
        const trainingTaskId = await handleTask(
          TASK_TYPES.TRAINING,
          chatbotService.trainChatbot,
          response.data.results.knowledge_base.id,
          response.data.results.knowledge_base.type
        );
        setKnowledgeBase(response.data.results.knowledge_base);

        if (trainingTaskId) {
          handleProgressTraining(0.33);
          setProcessTrainingMessage('Training in progress...');
          setTaskId(trainingTaskId);
          trackChatbotTraining({
            user_id: user.id,
            email: user.email,
            chatbot_id: response.data.results.knowledge_base.id,
            chatbot_name:
              response.data.results.knowledge_base.knowledge_base_name,
          });
        } else {
          setIsTraining(false);
        }
        const profileRes = await userService.getUserProfile();

        //check if is first chatbot
        if (chatbots.length === 0 && checkEnv()) {
          trackFirstChatbotCreation({
            user_id: user.id,
            email: user.email,
            chatbot_id: response.data.results.knowledge_base.id,
            chatbot_name:
              response.data.results.knowledge_base.knowledge_base_name,
            chatbot_type: response.data.results.knowledge_base.type,
            tier_name: user.tier_name,
          });
        }
        setUser(profileRes.data);
      }
    } catch (error) {
      console.log('error', error);
      setIsTraining(false);
      setSaveChanges(false);
      setCurrentStep(StepEnum.REVIEW_CREATE);
      resetProgressTraining();
      resetChatbotData();
    }
  };

  const handleProgressTraining = useCallback((progressAmount) => {
    let progress = progressTraining.current;
    if (isIncrementingProgressIntervalRef.current) {
      clearInterval(isIncrementingProgressIntervalRef.current);
    }
    isIncrementingProgressIntervalRef.current = setInterval(() => {
      if (progress < progressAmount) {
        progress += percentageIncrement;

        progressTraining.current = progress;
        setProgressTrainingPercentage(progress);
      } else {
        clearInterval(isIncrementingProgressIntervalRef.current);
      }
    }, 150);
  }, []);

  const resetProgressTraining = useCallback(() => {
    progressTraining.current = 0;
    setProgressTrainingPercentage(0);
    setProcessTrainingMessage('');
    setProgressTrainingText('');
    setSaveChanges(false);
    if (uploadingContentIntervalRef.current) {
      clearInterval(uploadingContentIntervalRef.current);
    }
    if (finishingTrainingIntervalRef.current) {
      clearInterval(finishingTrainingIntervalRef.current);
    }
    if (isIncrementingProgressIntervalRef.current) {
      clearInterval(isIncrementingProgressIntervalRef.current);
    }
  }, []);

  const finishingTraining = useCallback(() => {
    let progressMidway = false;
    let progressAlmostFinished = false;

    finishingTrainingIntervalRef.current = setInterval(() => {
      if (progressTraining.current >= 1) {
        setIsTraining(false);
        setTrainingFinished(true);
        setProgressBar((prev) =>
          prev.map((step) => ({
            ...step,
            completed: true,
            pending: false,
            active: false,
          }))
        );
        resetChatbotData();
        // Navigate with state to indicate we just created a chatbot
        navigate(`/chatbot/${knowledgeBase.id}`, { state: { fromChatbotCreation: true } });
        removeToast('training-toast');
        clearInterval(finishingTrainingIntervalRef.current);
      } else if (progressTraining.current < 0.8) {
        if (!progressMidway) {
          progressMidway = true;
          handleProgressTraining(0.81);
        }
      } else {
        setProcessTrainingMessage('Processing training almost finished...');
        if (!progressAlmostFinished) {
          progressAlmostFinished = true;
          handleProgressTraining(1);
        }
      }
    }, TASK_POLLING_INTERVAL);
  }, [handleProgressTraining, resetChatbotData, setProgressBar, knowledgeBase]);

  useEffect(() => {
    if (chatbotData.chatbotName === '') {
      setProgressBar([...createSteps]);
      setCurrentStep(0);
      resetProgressTraining();
    }
    return () => {
      resetProgressTraining();
    };
  }, []);

  useEffect(() => {
    if (tasks[TASK_TYPES.TRAINING]) {
      tasks[TASK_TYPES.TRAINING]?.forEach((task) => {
        if (task.id === taskId && task.status === 'SUCCESS') {
          setUnsavedChanges(false);
          finishingTraining();
        } else if (task.id === taskId && task.status === 'FAILURE') {
          setIsTraining(false);
          setCurrentStep(StepEnum.REVIEW_CREATE);
          addErrorToast({ message: 'Training failed. Please try again.' });
          resetProgressTraining();
          console.error(`Task ${task.id} failed.`);
        } else if (task.id === taskId && task.status === 'ERROR') {
          setIsTraining(false);
          resetProgressTraining();
          console.error(`Task ${task.id} encountered an error.`);
        } else if (task.id === taskId && task.status === 'PENDING') {
          if (progressTraining.current < 0.8) {
            handleProgressTraining(
              progressTraining.current + percentageIncrement
            );
          }
        }
      });
    }
  }, [tasks]);

  useEffect(() => {
    if (saveChanges) {
      handleSaveChanges();
    }
  }, [saveChanges]);

  return (
    <div className="h-full w-full overflow-y-hidden">
      {currentStep === StepEnum.CHATBOT_NAME && (
        <ChatbotName
          error={errors.nameError}
          chatbotData={chatbotData}
          setChatbotData={setChatbotData}
          onNextClick={onNextClick}
        />
      )}
      {currentStep === StepEnum.KNOWLEDGE && (
        <>
          <ChatbotKnowledge
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            chatbotData={chatbotData}
            setChatbotData={setChatbotData}
            errors={errors}
            onNextClick={onNextClick}
            onBackClick={onBackClick}
          />
        </>
      )}
      {currentStep === StepEnum.PERSONALITY_CREATE && (
        <Personality
          customizationData={chatbotData}
          updateCustomizationData={setChatbotData}
          canEditTemplate={false}
          personalities={personalities}
          errors={errors}
          onNextClick={onNextClick}
          onBackClick={onBackClick}
          setSelectedPersonality={setSelectedPersonality}
          selectedPersonality={selectedPersonality}
        />
      )}
      {currentStep === StepEnum.REVIEW_CREATE && (
        <ChatbotReview
          setActiveTab={setActiveTab}
          setCurrentStep={setCurrentStep}
          chatbotData={chatbotData}
          setChatbotData={setChatbotData}
          onBackClick={onBackClick}
          onTrainClick={onTrainClick}
        />
      )}
      {isTraining && (
        <TrainingInProgress
          message={processTrainingMessage}
          progress={progressTrainingPercentage}
          progressText={progressTrainingText}
        />
      )}
      {/* {trainingFinished && (
        <TrainingFinished
          config={{
            kb_id: knowledgeBase.id,
            name: knowledgeBase.knowledge_base_name,
          }}
          isInApp={true}
          setTempCustomizationData={setTempCustomizationData}
          payload={tempCustomizationData.initial_messages}
          setCustomization={setChatbotData}
        />
      )} */}
    </div>
  );
};

export default Creation;
