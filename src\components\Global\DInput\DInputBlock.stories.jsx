import { fn } from '@storybook/test';

import DInput from './DInput';
import DInputBlock from './DInputBlock';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Global/DInputBlock',
  component: DInputBlock,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    hiddenLabel: {
      default: false,
      control: { type: 'boolean' }
    },
    type: {
      options: ['text', 'password'],
      control: { type: 'radio' }
    },
    iconPlacement: {
      options: ['pre', 'post'],
      control: { type: 'radio' }
    }
  },
  args: {
    hiddenLabel: false,
    name: 'full_name',
    label: 'Title',
    description: 'Description'
  }
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    hiddenLabel: false,
    name: 'full_name',
    label: 'Title',
    description: 'Description',
    children: <DInput />
  }
};
