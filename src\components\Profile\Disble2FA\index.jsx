import { disable2FA } from '@/services/user.service';
import Box2FA from '@/components/Global/Box2FA';
import useToast from '@/hooks/useToast';
import { useState } from 'react';
import DButton from '@/components/Global/DButton';

const Disable2FA = ({ onClose, user, setUser }) => {
    const { addSuccessToast } = useToast();
    const [otp, setOtp] = useState(Array(6).fill(''));
    const [error, setError] = useState('');

    const handleDisable2FA = async () => {
        if(otp.length !== 6){
            setError('Please enter a valid 6-digit code');
            return;
        }
        try {
          const response = await disable2FA(otp);
          if(response.status === 200) {
            addSuccessToast({
              message: '2FA disabled successfully',
            });
            setUser({...user, requires_2FA: false});
            onClose();
          }
        } catch (error) {
          console.log(error);
        }
      }


    return (
        <div className="flex flex-col gap-size3 p-size2 bg-grey-2 rounded-size0 w-full">
            <p className="text-sm font-regular tracking-tight">To disable 2FA, please enter the 6-digit code generated by your authenticator app</p>
                <Box2FA
                value={otp}
                onChange={(value) => setOtp(value)}
                error={error}
            />
            <DButton
                variant="dark"
                size="sm"
                fullWidth
                onClick={handleDisable2FA}
            >
                Disable 2FA
            </DButton>
        </div>
    )
}

export default Disable2FA;