import { useEffect, useRef, useState } from 'react';

import { useCreateChatbotStore } from '@/stores/chatbot/createChatbotStore';

import DAlert from '../../Global/DAlert';
import DCheckbox from '../../Global/DCheckbox';
import DButton from '@/components/Global/DButton';
import ReturnIcon from '@/components/Global/Icons/ReturnIcon';
import DSelect from '@/components/Global/DSelect';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
import DTextArea from '@/components/Global/DInput/DTextArea';
import DTooltip from '@/components/Global/DTooltip';

import './index.css';
import RightArrowIcon from '@/components/Global/Icons/RightArrowIcon';
import ChevronRightIcon from '@/components/Global/Icons/ChevronRightIcon';
import DSwitch from '@/components/Global/DSwitch';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import { activateNewDesign } from '@/services/chatbot.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';

const Personality = ({
  canEditTemplate = true,
  customizationData,
  updateCustomizationData,
  personalities,
  selectedPersonality,
  setSelectedPersonality,
  errors,
  onBackClick,
  onNextClick,
}) => {
  // states
  const [openActivateNewDesign, setOpenActivateNewDesign] = useState(false);
  const [activateNewDesignLoading, setActivateNewDesignLoading] = useState(false);
  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);

  const [currentPersonalityName, setCurrentPersonalityName] = useState(
    customizationData?.personalities_template ||
      (canEditTemplate ? customizationData?.personalities_template : 'formal')
  );
  const marks = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      onNextClick();
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onNextClick]);

  // Create options for DSelect
  const personalityOptions = personalities
    ?.filter(
      (personality) =>
        canEditTemplate || personality.name?.toLowerCase() !== 'custom'
    )
    .map((personality) => ({
      value: personality.personality_id,
      label: personality.name,
    }));

  const handlePersonalityChange = (personalityId) => {
    const personality = personalities.find(
      (p) => p.personality_id === personalityId
    );
    if (personality) {
      setSelectedPersonality && setSelectedPersonality(personality);
      setCurrentPersonalityName(personality.name?.toLowerCase());
      updateCustomizationData(
        'personalities_template',
        personality.name?.toLowerCase()
      );
      updateCustomizationData('base_system_prompt', personality.description);
    }
  };

  const handleCreativityChange = (e) => {
    const inputValue = parseInt(e.target.value, 10);

    const closestMark = marks.reduce((prev, curr) =>
      Math.abs(curr - inputValue) < Math.abs(prev - inputValue) ? curr : prev
    );
    updateCustomizationData('temperature', closestMark / 100);
  };

  const handleActivateNewDesign = async () => {
    try {
      setActivateNewDesignLoading(true);
      const response = await activateNewDesign(customizationData?.kb_id);
      if (response.status === 200) {
        setSelectedChatbot({
          ...selectedChatbot,
          knowledge_base: {
            ...selectedChatbot.knowledge_base,
            new_design_activated: true,
          },
        });
        setOpenActivateNewDesign(false);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setActivateNewDesignLoading(false);
    }
  };

  useEffect(() => {
    if (!canEditTemplate) {
      updateCustomizationData('personalities_template', 'formal');
      updateCustomizationData(
        'base_system_prompt',
        personalities?.find(
          (personality) => personality.personality_id === 'formal'
        )?.description
      );
    }
  }, []);

  useEffect(() => {
    if (personalities) {
      setCurrentPersonalityName(
        customizationData?.personalities_template ||
          (canEditTemplate
            ? customizationData?.personalities_template
            : 'formal')
      );
    }
  }, [customizationData, personalities]);

  return (
    <div>
      {!canEditTemplate && (
        <div className="flex justify-end gap-size1">
          <DButton
            variant="outlined"
            size="md"
            fullWidth
            onClick={onBackClick}
            className="!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3"
          >
            Back
          </DButton>
          <DButton
            variant="contained"
            size="md"
            fullWidth
            onClick={onNextClick}
            disabled={!customizationData?.personalities_template}
            className="!h-[42px] !w-[150px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3 personality-next-btn"
          >
            <div className="flex items-center justify-center gap-1">
              <span className="text-white text-lg ml-size2">Next</span>
              <ChevronRightIcon className="w-5 h-5" />
            </div>
          </DButton>
        </div>
      )}
      <div
        className={`flex flex-col gap-size5  justify-center mx-auto  ${
          !canEditTemplate ? 'max-w-[650px] pt-size5' : ''
        }`}
      >
        {!selectedChatbot?.knowledge_base?.new_design_activated && canEditTemplate && (
          <div className="flex flex-col gap-size1 mb-size2">
            <div className="flex items-center gap-size0">
              <DSwitch
                label="Activate new design"
                onChange={() => setOpenActivateNewDesign(true)}
                id="activate-new-design"
              />
              <DTooltip content="By enabling the new design, your AI Chatbot will adopt a refreshed appearance and gain access to exciting new features. If you’d prefer to stick with the old design, you can continue using it until <b>31 December</b>. To keep the old design, make sure not to toggle this option on.">
                <InfoIcon className="text-grey-50 size-3 ml-1" />
              </DTooltip>
            </div>
            <p className="text-xs tracking-tight text-grey-50">
              Activate the new deisgn to upgrade your AI Chatbot’s look. The
              changes shown below will only take effect once your activate
              this setting.
            </p>
          </div>
        )}
        <div className="flex flex-col gap-size8">
          {!canEditTemplate && (
            <div className="flex flex-col gap-size1">
              <p className="text-xl font-medium tracking-tight">
                Define your AI Chatbot's personality
              </p>
              <p className="text-grey-50 text-xs font-light tracking-tight">
                Choose how your AI Chatbot speaks and interacts. You can
                customize or create a new personality later.
              </p>
            </div>
          )}

          <div className="flex flex-col gap-size4">
            {/* Left side - Personality Select */}
            <div className="flex flex-col gap-size3 flex-1">
              <div className="flex items-center gap-size0">
                <p className="text-base font-medium tracking-tight">
                  Personality
                </p>
                <DTooltip
                  position="right"
                  content="Choose a personality to define the style your AI Chatbot responds. You can create your own personality later."
                >
                  <InfoIcon className="size-3 text-grey-50" />
                </DTooltip>
              </div>
              <DSelect
                options={personalityOptions}
                value={
                  selectedPersonality?.personality_id ||
                  personalities?.find(
                    (personality) => personality.personality_id === 'formal'
                  )?.personality_id
                }
                onChange={handlePersonalityChange}
                placeholder="Select a personality"
                error={errors?.personalityError}
                selectClassName="personality-input"
              />

              {canEditTemplate && (
                <DTextArea
                  placeholder="Write a detailed prompt outlining your desired AI Chatbot behavior..."
                  className="border border-grey-5 rounded-size1 py-size3 px-size2"
                  rows={8}
                  value={selectedPersonality?.description}
                  onChange={(e) => {
                    updateCustomizationData(
                      'base_system_prompt',
                      e.target.value
                    );
                    setSelectedPersonality({
                      ...selectedPersonality,
                      description: e.target.value,
                    });
                  }}
                />
              )}
            </div>

            {/* Right side - Creativity slider */}
            <div className="flex flex-col gap-size3 flex-1">
              <div className="flex items-center gap-size0">
                <p className="text-base font-medium tracking-tight">
                  Creativity
                </p>
                <DTooltip
                  position="right"
                  content="Choose 'Rigid' for similar responses to repeat questions. Choose 'Dynamic' for dynamic responses to repeat questions."
                >
                  <InfoIcon className="size-3 text-grey-50" />
                </DTooltip>
              </div>
              <div className="flex items-baseline gap-size0 bg-grey-2 rounded-size1 px-size1 h-[44px]">
                <span className="text-[8px] mt-[15px]">Static</span>
                <div className="relative w-full">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    step="10"
                    value={customizationData?.temperature * 100 || 0}
                    onChange={(e) => handleCreativityChange(e)}
                    list="creativity-ticks"
                    className="dbutton range-input w-full rounded-lg appearance-none cursor-pointer outline-none accent-black"
                    style={{
                      background: `linear-gradient(to right, var(--dt-color-element-100) ${
                        customizationData?.temperature * 100
                      }%, var(--dt-color-element-10) ${
                        customizationData?.temperature * 100
                      }%)`,
                    }}
                  />
                  <div className="flex justify-between mt-[-11px]">
                    {marks.map((mark, index) => (
                      <div key={index} className="relative">
                        <span
                          className="size-[2px] bg-black rounded-full"
                          style={{
                            position: 'absolute',
                            bottom: '-3px',
                            left: '50%',
                            transform: 'translateX(-50%)',
                          }}
                        ></span>
                      </div>
                    ))}
                  </div>
                </div>
                <span className="text-[8px] mt-[15px]">Dynamic</span>
              </div>
            </div>
          </div>
        </div>
        {canEditTemplate && <div className="flex gap-size1">
          <DCheckbox
            label="Use full AI knowledge (not just uploaded memory)"
            checked={customizationData?.open_to_internet_knowledge}
            hideError
            style={{
              width: 'auto',
            }}
            onChange={(checked) => {
              updateCustomizationData('open_to_internet_knowledge', checked);
              if (!canEditTemplate) {
                updateCustomizationData('allowAccessToInternet', checked);
              }
            }}
          />
          <DTooltip
            position="right"
            content={
              <div className="flex flex-col gap-size1">
                <p className="text-sm">When enabled, your AI Chatbot will use its full general knowledge (from the language model) to answer questions—not just the files or links you uploaded.</p>
                <p className="text-sm">This means it won’t be limited to your specific content (memory), and may generate broader or less controlled responses.</p>
                <p className="text-sm">Turn this off to keep your chatbot focused only on the content you’ve provided.</p>
              </div>
            }
          >
            <InfoIcon className="size-3 text-grey-50" />
          </DTooltip>
          {/* <DAlert state="info">
            <p className="text-xs mt-[1px]">
              This will allow the AI Chatbot to access the LLM's wider general knowledge. Do not select this option if you want your AI Chatbot to only answer from the knowledge files and/or URLs you have provided.
            </p>
          </DAlert> */}
      </div>}
      <DConfirmationModal
        open={openActivateNewDesign}
        onClose={() => setOpenActivateNewDesign(false)}
        onConfirm={handleActivateNewDesign}
        title="Activate New Design"
        description="Are you sure you want to activate the new design? This action cannot be undone."
        confirmText="Activate"
        cancelText="Cancel"
        loading={activateNewDesignLoading}
      />
      </div>
    </div>
  );
};

export default Personality;
