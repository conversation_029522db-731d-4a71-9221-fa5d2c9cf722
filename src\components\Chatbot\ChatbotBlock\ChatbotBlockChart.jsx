import React, { useEffect, useState } from 'react';
import { DateTime } from 'luxon';
import { Line } from 'react-chartjs-2';
import 'chart.js/auto';
import { STATUS } from '@/constants';

const defaultDataChart = {
  labels: [],
  datasets: [],
};

let width, height, gradient;
function getGradient(ctx, chartArea, status = STATUS.ACTIVE) {
  const chartWidth = chartArea.right - chartArea.left;
  const chartHeight = chartArea.bottom - chartArea.top;
  if (!gradient || width !== chartWidth || height !== chartHeight) {
    // Create the gradient because this is either the first render
    // or the size of the chart has changed
    width = chartWidth;
    height = chartHeight;
    gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
    gradient.addColorStop(
      0,
      status === STATUS.ACTIVE ? 'rgba(130, 117, 247, 0)' : 'rgba(0, 0, 0, 0)'
    );

    gradient.addColorStop(
      1,
      status === STATUS.ACTIVE
        ? 'rgba(130, 117, 247, 0.1)'
        : 'rgba(0, 0, 0, 0.2)'
    );
  }

  return gradient;
}

const ChatbotBlockChart = ({ options = {}, data = [], status }) => {
  const [correctData, setCorrectData] = useState(defaultDataChart);
  const [dates, setDates] = useState([' ', ' ']);
  
  const [isDarkTheme, setIsDarkTheme] = useState(false);

  useEffect(() => {
    const handleThemeChange = () => {
      const theme = localStorage.getItem('theme');
      setIsDarkTheme(theme === 'dark');
    };
  
    // Listen for the custom event (or you could also listen for 'storage')
    window.addEventListener('themeChanged', handleThemeChange);
    // If you also dispatch the native 'storage' event, you can listen to that:
    window.addEventListener('storage', handleThemeChange);
  
    return () => {
      window.removeEventListener('themeChanged', handleThemeChange);
      window.removeEventListener('storage', handleThemeChange);
    };
  }, []);
  

  useEffect(() => {
    const labels = [];
    const displayedDates = [...dates];
    
    // Determine border color based on status and theme
    let borderColor = '#8275F7'; // Default active color
    if (status !== STATUS.ACTIVE) {
      borderColor = isDarkTheme ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)';
    }
    const dataset = {
      data: [],
      label: '',
      borderColor: borderColor,
      borderWidth: 1,
      backgroundColor: function (context) {
        const chart = context.chart;
        const { ctx, chartArea } = chart;

        if (!chartArea) return;
        return getGradient(ctx, chartArea, status);
      },
      fill: true,
      pointStyle: false,
    };

    data?.forEach((date, i) => {
      const parsedDate = DateTime.fromISO(date.date);
      labels.push(parsedDate.toFormat('MMM dd'));

      if (i === 0) {
        displayedDates[0] = parsedDate.toFormat('MMM dd');
      }
      if (i === data.length - 1) {
        displayedDates[1] = parsedDate.toFormat('MMM dd');
      }

      dataset.data.push(date.value);
    });

    setCorrectData({
      labels,
      datasets: [dataset],
    });
    setDates(displayedDates);
  }, [data, status, isDarkTheme]);

  return (
    <div className="flex flex-col gap-size2 w-full bg-grey-2 p-size1 rounded-size0">
      <div
        style={{ position: 'relative', width: '100%', aspectRatio: '300/60' }}
      >
        <Line
          options={{
            responsive: true,
            aspectRatio: 300 / 60,
            layout: {
              autoPadding: false,
              padding: {
                left: -8,
                right: -8,
              },
            },
            plugins: {
              legend: false,
              title: false,
              tooltip: false,
            },
            scales: {
              x: {
                offset: false,
                grid: { display: false },
                ticks: {
                  display: false,
                  maxTicksLimit: 3,
                  padding: 0,
                },
                border: { display: false },
              },
              y: {
                display: false,
                suggestedMax: 1,
                grid: { display: false },
                ticks: { padding: 0 },
              },
            },
            ...options,
          }}
          data={correctData}
        />
      </div>
      <div className="flex justify-between">
        <div className="value text-xs text-grey-20">{dates[0]}</div>
        <div className="value text-xs text-grey-20">{dates[1]}</div>
      </div>
    </div>
  );
};

export default ChatbotBlockChart;
