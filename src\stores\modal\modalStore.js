import { create } from 'zustand';

const useModalStore = create((set) => ({
  showPlans: false,
  showInlinePlans: false,
  currentFeature: null,
  inlineCurrentFeature: null,
  setShowPlans: (show) => set({ showPlans: show }),
  setShowInlinePlans: (show) => set({ showInlinePlans: show }),
  setCurrentFeature: (feature) => set({ currentFeature: feature }),
  setInlineCurrentFeature: (feature) => set({ inlineCurrentFeature: feature }),
  openPlansModal: (feature) => set({ showPlans: true, currentFeature: feature }),
  openInlinePlansModal: (feature) => set({ showInlinePlans: true, inlineCurrentFeature: feature }),
  closePlansModal: () => set({ showPlans: false, currentFeature: null }),
  closeInlinePlansModal: () => set({ showInlinePlans: false, inlineCurrentFeature: null }),
}));

export default useModalStore; 