import * as React from 'react';
const ConnectIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.5 3a1.375 1.375 0 1 0 .55 2.636l.009-.004.009-.004A1.375 1.375 0 0 0 7.5 3m0 3.75q.251 0 .486-.05L8.6 8.081a2.37 2.37 0 0 0-.668 3.088l-2.154 1.915a2.375 2.375 0 1 0 .665.747l2.153-1.915a2.37 2.37 0 0 0 2.93-.096l1.959 1.524a2.375 2.375 0 1 0 .614-.79l-1.959-1.523a2.37 2.37 0 0 0 .22-1.291l1.22-.406A2.374 2.374 0 0 0 18 8.125a2.375 2.375 0 1 0-4.736.26l-1.22.406a2.37 2.37 0 0 0-2.53-1.116L8.9 6.294a2.375 2.375 0 1 0-1.4.456m6.75 7.625a1.37 1.37 0 0 1 .302-.86 1.375 1.375 0 1 1-.302.86m.075-5.8-.005-.015-.005-.015a1.374 1.374 0 0 1 1.31-1.795 1.375 1.375 0 1 1-1.3 1.824m-4.903.177a1.375 1.375 0 0 0-.46 2.15l.01.01.01.012a1.372 1.372 0 0 0 2.091-.064l.007-.01.005-.006.013-.016a1.37 1.37 0 0 0 .21-1.252l-.003-.011-.004-.01a1.376 1.376 0 0 0-1.879-.803M3 15a1.375 1.375 0 1 1 2.75 0A1.375 1.375 0 0 1 3 15"
      fill="currentColor"
    />
  </svg>
);
export default ConnectIcon;
