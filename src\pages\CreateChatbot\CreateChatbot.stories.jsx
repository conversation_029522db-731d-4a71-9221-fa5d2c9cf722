import CreateChatbot from './index';

export default {
  title: 'Pages/CreateChatbot',
  component: CreateChatbot,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen'
  },
  argTypes: {
    onSubmit: {
      action: 'submitted'
    }
  },
  decorators: [
    (Story) => (
      <div className="h-screen">
        <Story />
      </div>
    )
  ]
};

export const Default = {
  args: {
    onSubmit: () => {}
  }
};
