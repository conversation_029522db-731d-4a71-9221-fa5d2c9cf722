import { DANTE_THEME_CHAT, DANTE_THEME_COLOR_CHAT } from '@/constants';
import convertThemeToCSSVariablesStyle, {
  convertThemeToCSSVariablesObj
} from '@/helpers/convertThemeToCSSVariables';
import {
  fakerInitialMessages,
  fakerLoadingConversation,
  fakerLongConversation,
  fakerShortConversation,
  fakerSuggestionsPrompts
} from '@/helpers/stories/generateChatMessages';
import { fn } from '@storybook/test';

import Bubble from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Bubble',
  component: Bubble,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'fullscreen',
    backgrounds: {
      default: 'transparent'
    }
  },
  decorators: [
    (Story) => (
      <div className="h-screen">
        <Story />
      </div>
    )
  ],
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {
    type: 'chatbot',
    hiddenConversation: false,
    showBookMeeting: false,
    showLiveAgent: false,
    hiddenLlmSelector: false,
    hiddenPoweredByDante: false,
    messages: fakerInitialMessages,
    suggestionPrompts: [],
    customization: DANTE_THEME_COLOR_CHAT,
    handleCloseButton: fn(),
    handleOpenDanteConversations: fn()
  },
  render: (args) => (
    <div style={convertThemeToCSSVariablesObj(args.customization)} className="h-screen">
      <Bubble {...args} />
    </div>
  )
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    customization: { ...DANTE_THEME_CHAT, name: 'Support AI' },
    messages: fakerInitialMessages,
    suggestionPrompts: fakerSuggestionsPrompts,
    hiddenConversation: false
  }
};
export const DefaultNoCustomization = {
  args: {
    customization: { name: 'Support AI' },
    messages: fakerInitialMessages,
    suggestionPrompts: fakerSuggestionsPrompts,
    hiddenConversation: false
  },
  render: (args) => (
    <>
      <style>{`.bubble  {
  ${convertThemeToCSSVariablesStyle(args.customization)}
}`}</style>
      <Bubble {...args} />
    </>
  )
};

// export const SmallConversation = {
//   args: {
//     customization: DANTE_THEME_COLOR_CHAT,
//     messages: fakerShortConversation,
//     hiddenConversation: false
//   }
// };
// export const SmallConversationRemoveLlmSelector = {
//   args: {
//     customization: DANTE_THEME_COLOR_CHAT,
//     messages: fakerShortConversation,
//     hiddenConversation: false,
//     hiddenLlmSelector: true
//   }
// };
// export const SmallConversationRemovePoweredByDante = {
//   args: {
//     customization: DANTE_THEME_COLOR_CHAT,
//     messages: fakerShortConversation,
//     hiddenConversation: false,
//     hiddenPoweredByDante: true
//   }
// };
// export const SmallConversationBookMeeting = {
//   args: {
//     customization: DANTE_THEME_COLOR_CHAT,
//     messages: fakerShortConversation,
//     suggestionPrompts: fakerSuggestionsPrompts,
//     hiddenConversation: false,
//     showBookMeeting: true
//   }
// };
// export const SmallConversationBookMeetingAndLiveAgent = {
//   args: {
//     customization: DANTE_THEME_COLOR_CHAT,
//     messages: fakerShortConversation,
//     suggestionPrompts: fakerSuggestionsPrompts,
//     hiddenConversation: false,
//     showBookMeeting: true,
//     showLiveAgent: true
//   }
// };
// export const LoadingMessage = {
//   args: {
//     customization: DANTE_THEME_COLOR_CHAT,
//     messages: fakerLoadingConversation,
//     hiddenConversation: false
//   }
// };
// export const LongConversation = {
//   decorators: [],
//   args: {
//     customization: DANTE_THEME_COLOR_CHAT,
//     messages: fakerLongConversation,
//     suggestionPrompts: fakerSuggestionsPrompts,
//     hiddenConversation: false
//   }
// };

// export const ChatDanteFAQInitial = {
//   decorators: [],
//   args: {
//     messages: [],
//     hiddenConversation: true
//   }
// };

// export const ChatDanteFAQActive = {
//   args: {
//     ...ChatDanteFAQInitial.args,
//     hiddenConversation: false
//   }
// };
