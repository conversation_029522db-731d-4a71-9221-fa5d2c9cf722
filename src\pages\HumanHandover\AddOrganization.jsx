import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DModal from '@/components/Global/DModal';
import { useState } from 'react';
import {
  getDefaultImages,
} from '@/services/teamManagement.service';
import DToastContainer from '@/components/DToast/DToastContainer';
import DProfileImage from '@/components/DProfileImage';
import useDanteApi from '@/hooks/useDanteApi';


const AddOrganization = ({ open, onClose, onSubmit, newOrganization, setNewOrganization, iconOrganizationFile, iconOrganizationPreview, handleImageChange }) => {
  const { data: defaultTeamImages } = useDanteApi(getDefaultImages);
  const [errors, setErrors] = useState([]);

  const [imageError, setImageError] = useState({
    icon: '',
  });

  const handleBeforeSubmit = () => {
    const newErrors = [];

    if (newOrganization.name === '') {
      newErrors.push({ field: 'name', message: 'Name is required' });
    }
    if (newOrganization.icon === '') {
      newErrors.push({ field: 'icon', message: 'Icon is required' });
    }
    if (imageError.icon) {
      newErrors.push({ field: 'icon', message: imageError.icon });
    }

    setErrors(newErrors);
    if (newErrors.length === 0) {
      onSubmit();
    }
  };

  return (
    <DModal
      title="Add Organization"
      isOpen={open}
      onClose={onClose}
      footer={
        <div className="flex items-center gap-size1 w-full">
          <DButton onClick={onClose} variant="grey" fullWidth size="md">
            Cancel
          </DButton>
          <DButton
            onClick={handleBeforeSubmit}
            variant="dark"
            fullWidth
            size="md"
          >
            Confirm
          </DButton>
        </div>
      }
    >
      <DToastContainer showFixed />

      <div className="flex flex-col gap-size5">
        <div className="flex flex-col gap-size0">
          <p className="text-base font-medium tracking-tight">Name</p>
          <DInput
            placeholder="Enter name"
            value={newOrganization.name}
            error={errors.find((error) => error.field === 'name')?.message}
            onChange={(e) => setNewOrganization({ ...newOrganization, name: e.target.value })}
          />
        </div>

        <DProfileImage
          label="Organization icon"
          name="organization_icon"
          imageUrl={iconOrganizationPreview}
          defaultImages={defaultTeamImages?.results}
          handleImageChange={(e) => {
            handleImageChange(e, 'icon')
          }}
          imageFile={iconOrganizationFile}
          setImageError={setImageError}
          error={errors.find((error) => error.field === 'icon')?.message}
          maxSizeFile={150 * 1024}
        />


      </div>
    </DModal>
  );
};

export default AddOrganization;
