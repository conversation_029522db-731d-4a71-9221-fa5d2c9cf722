import * as React from 'react';
const DashboardIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.834 16.5H3.5v-4.667h6.334zm-6.584 1a.75.75 0 0 1-.75-.75v-5.167a.75.75 0 0 1 .75-.75h6.834a.75.75 0 0 1 .75.75v5.167a.75.75 0 0 1-.75.75zm6.917-9.333H16.5V3.5h-6.333zm6.583 1a.75.75 0 0 0 .75-.75V3.25a.75.75 0 0 0-.75-.75H9.917a.75.75 0 0 0-.75.75v5.167c0 .414.336.75.75.75zM13.5 16.5h3v-4.667h-3zm-1 .25c0 .414.336.75.75.75h3.5a.75.75 0 0 0 .75-.75v-5.167a.75.75 0 0 0-.75-.75h-3.5a.75.75 0 0 0-.75.75zm-9-8.583h3V3.5h-3zm3.25 1a.75.75 0 0 0 .75-.75V3.25a.75.75 0 0 0-.75-.75h-3.5a.75.75 0 0 0-.75.75v5.167c0 .414.336.75.75.75z"
      fill="currentColor"
    />
  </svg>
);
export default DashboardIcon;
