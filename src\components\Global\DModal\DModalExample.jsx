import { useState } from 'react';
import DModal from '.';
import DButton from '../DButton';

// Sample envelope image component matching the reference image
const EnvelopeImage = () => (
  <div className="flex items-center justify-center w-full h-full">
    <svg width="180" height="180" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" className="max-w-full">
      <path d="M150 30H30C24.4772 30 20 34.4772 20 40V140C20 145.523 24.4772 150 30 150H150C155.523 150 160 145.523 160 140V40C160 34.4772 155.523 30 150 30Z" fill="white" stroke="#E6E6E6" strokeWidth="2"/>
      <path d="M160 40L94.6863 105.314C91.7889 108.211 87.2111 108.211 84.3137 105.314L20 41" stroke="#E6E6E6" strokeWidth="2"/>
      <circle cx="90" cy="90" r="15" fill="#FF6B81"/>
    </svg>
  </div>
);

const DModalExample = () => {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  return (
    <div className="flex flex-col items-center gap-size3">
      <DButton variant="dark" size="md" onClick={handleOpen}>
        Open Newsletter Modal
      </DButton>

      <DModal
        isOpen={isOpen}
        onClose={handleClose}
        title="Would you like to hear from us?"
        subtitle="You can control email preferences in Account Settings."
        imageSection={<EnvelopeImage />}
        footer={
          <div className="flex gap-size2 w-full">
            <DButton variant="outlined" size="md" className="w-1/2" onClick={handleClose}>
              Maybe later
            </DButton>
            <DButton variant="dark" size="md" className="w-1/2" onClick={handleClose}>
              Yes, Opt me in!
            </DButton>
          </div>
        }
      >
        <div className="flex flex-col gap-size4 py-size2">
          <div className="flex items-center gap-size2">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z" fill="#8275F7"/>
            </svg>
            <span>Receive discounts, tips and expert advice</span>
          </div>
          <div className="flex items-center gap-size2">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z" fill="#8275F7"/>
            </svg>
            <span>Early access to new features and tools</span>
          </div>
          <div className="flex items-center gap-size2">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z" fill="#8275F7"/>
            </svg>
            <span>What's new updates and releases</span>
          </div>
          <div className="flex items-center gap-size2">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z" fill="#8275F7"/>
            </svg>
            <span>Free online courses and expert advice</span>
          </div>
        </div>
      </DModal>
    </div>
  );
};

export default DModalExample;
