import { Button } from '@headlessui/react';
import clsx from 'clsx';
import { forwardRef } from 'react';

/**
 * DButtonIcon component renders a customizable button with various sizes, styles (variants)

 */
const DButtonIcon = forwardRef(
  (
    { size = 'md', variant = '', onClick, children, className = '', ...props },
    ref
  ) => {
    const buttonSize = clsx({
      'size-8': size === 'md' || size === 'contained', // Default size
      'size-11': size === 'lg',
      'size-6': size === 'sm',
      'size-4': size === 'xs',
      'h-8': variant === 'squared',
    });

    const buttonVariant = clsx({
      'bg-purple-200 text-white': variant === 'contained',
      'border border-grey-10': variant === 'outlined',
      'bg-black text-white': variant === 'dark',
      'bg-transparent text-[--dt-color-element-100]': variant === 'ghost',
      'bg-grey-5 text-[--dt-color-element-100]': variant === 'grey',
      'bg-grey-2 text-[--dt-color-element-100]': variant === 'lightgrey',
      'rounded-size1 border': variant === 'squared',
      'rounded-size0 bg-white text-dark border border-gray-10 disabled:bg-white/20':
        variant === 'light',
    });

    return (
      <Button
        data-testid={`d-button-icon-${props.name ?? props.id ?? ''}`}
        {...props}
        ref={ref}
        className={clsx(
          'dbutton',
          'rounded-size1 flex items-center justify-center gap-2 font-medium tracking-tighter disabled:text-grey-5',
          buttonSize,
          buttonVariant,
          className
        )}
        onClick={onClick}
      >
        {children}
      </Button>
    );
  }
);

DButtonIcon.displayName = 'DButtonIcon';

export default DButtonIcon;
