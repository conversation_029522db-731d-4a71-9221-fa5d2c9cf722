import ChatbotBlock from '@/components/Chatbot/ChatbotBlock';
import ChatbotDashboard from '@/components/Chatbot/Dashboard';
import { listFakeChatbots } from '@/helpers/stories/generateListChatbots';
import { fn } from '@storybook/test';

import LayoutWithButtons from '../LayoutWithButtons';

import LayoutMediumScreen from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Layout/LayoutMediumScreen',
  component: LayoutMediumScreen,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'fullscreen'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {}
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    title: 'Lorem ipsum',
    children: <ChatbotDashboard chatbots={listFakeChatbots} />
  }
};

export const WithLayoutWithButtons = {
  args: {
    title: '',
    children: (
      <LayoutWithButtons buttons={true}>
        <h1>Hello</h1>
        <h1>Hello</h1>
        <h1>Hello</h1>
        <h1>Hello</h1>
        <h1>Hello</h1>
        <h1>Hello</h1>
      </LayoutWithButtons>
    )
  }
};
