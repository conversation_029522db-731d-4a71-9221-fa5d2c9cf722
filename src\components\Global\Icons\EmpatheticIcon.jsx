const EmpatheticIcon = (props) => {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M4.99902 19.087H7.60902C7.94902 19.087 8.28802 19.127 8.61802 19.209L11.376 19.879C11.974 20.025 12.598 20.039 13.202 19.921L16.252 19.328C17.0535 19.1733 17.7925 18.7887 18.379 18.221L20.537 16.121C20.6831 15.9807 20.7992 15.8124 20.8786 15.6261C20.958 15.4399 20.9989 15.2395 20.9989 15.037C20.9989 14.8345 20.958 14.6341 20.8786 14.4479C20.7992 14.2616 20.6831 14.0933 20.537 13.953C20.2649 13.6915 19.9095 13.5339 19.533 13.5077C19.1564 13.4816 18.7827 13.5886 18.477 13.81L15.962 15.645C15.602 15.908 15.163 16.05 14.712 16.05H12.285H13.83C14.701 16.05 15.407 15.363 15.407 14.516V14.209C15.407 13.505 14.915 12.892 14.213 12.722L11.828 12.142C11.4397 12.0477 11.0416 12.0001 10.642 12C9.67702 12 7.93102 12.799 7.93102 12.799L4.99902 14.025M0.999023 13.6V19.4C0.999023 19.96 0.999023 20.24 1.10802 20.454C1.2039 20.6422 1.35687 20.7951 1.54502 20.891C1.75902 21 2.03902 21 2.59902 21H3.39902C3.95902 21 4.23902 21 4.45302 20.891C4.64118 20.7951 4.79415 20.6422 4.89002 20.454C4.99902 20.24 4.99902 19.96 4.99902 19.4V13.6C4.99902 13.04 4.99902 12.76 4.89002 12.546C4.79415 12.3578 4.64118 12.2049 4.45302 12.109C4.23902 12 3.95902 12 3.39902 12H2.59902C2.03902 12 1.75902 12 1.54502 12.109C1.35687 12.2049 1.2039 12.3578 1.10802 12.546C0.999023 12.76 0.999023 13.04 0.999023 13.6ZM16.19 2.59202C15.594 1.34302 14.218 0.682017 12.88 1.32002C11.541 1.95902 10.971 3.47302 11.532 4.80302C11.878 5.62402 12.87 7.22002 13.577 8.31902C13.838 8.72502 13.969 8.92902 14.16 9.04702C14.324 9.14902 14.529 9.20402 14.721 9.19702C14.946 9.19002 15.161 9.07902 15.591 8.85802C16.752 8.26002 18.409 7.37502 19.12 6.83602C19.6811 6.41964 20.0559 5.79915 20.1633 5.10868C20.2708 4.4182 20.1021 3.71319 19.694 3.14602C18.833 1.92902 17.332 1.80902 16.19 2.59202Z"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default EmpatheticIcon;
