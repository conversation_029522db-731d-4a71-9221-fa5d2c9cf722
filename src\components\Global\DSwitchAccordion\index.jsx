import React from 'react';
import DSwitch from '../DSwitch';
import DTooltip from '../DTooltip';
import InfoIcon from '../Icons/InfoIcon';
import DTransition from '../DTransition';

const DSwitchAccordion = React.memo(
  ({ title, children, tooltip, tooltipContent, switchOpen, onToggle, titleClassName, extraContent }) => {
    return (
      <div className="rounded-lg">
        <button
          aria-expanded={switchOpen}
          className={`dbutton flex w-full items-center justify-between bg-grey-2 p-size2 rounded-size1 ${
            switchOpen
              ? 'bg-gradient-to-r from-purple-300/10 to-transparent'
              : ''
          }`}
          onClick={() => onToggle(!switchOpen)}
        >
          <div className="flex items-center gap-size1">
            <span className={`text-lg tracking-tight font-medium ${titleClassName}`}>{title}</span>
            {tooltip && (
              <DTooltip content={tooltipContent}>
                <InfoIcon className="w-3 h-3" />
              </DTooltip>
            )}
          {extraContent && (
            <div className="flex items-center gap-size1">
              {extraContent}
            </div>
          )}
          </div>
          <DSwitch
            checked={switchOpen}
            onChange={() => onToggle(!switchOpen)}
          />
        </button>
        {/* {switchOpen && <div className="p-size2">{children}</div>} */}
        <DTransition
          show={switchOpen}
          onInitialMount={switchOpen}
          type="collapse"
        >
          <div className="py-size2">{children}</div>
        </DTransition>
      </div>
    );
  }
);

DSwitchAccordion.displayName = 'DSwitchAccordion';

export default DSwitchAccordion;
