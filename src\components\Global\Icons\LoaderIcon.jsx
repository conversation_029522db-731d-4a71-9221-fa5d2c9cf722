import * as React from 'react';
const LoaderIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.833 1.876a.833.833 0 0 0-1.667 0V3.96a.833.833 0 0 0 1.667 0zm0 13.125a.833.833 0 0 0-1.667 0v3.334a.833.833 0 1 0 1.667 0zm-9.791-5c0-.46.373-.833.833-.833h2.917a.833.833 0 1 1 0 1.667H1.875A.833.833 0 0 1 1.042 10m15.416-.833a.833.833 0 0 0 0 1.667h1.25a.833.833 0 1 0 0-1.667zm-2.256 5.035a.833.833 0 0 1 1.179 0l.59.59a.833.833 0 0 1-1.18 1.178l-.589-.59a.833.833 0 0 1 0-1.178m1.94-9.1a.833.833 0 0 0-1.178-1.18l-1.178 1.18a.833.833 0 1 0 1.178 1.178zm-9.095 7.85a.833.833 0 0 1 0 1.179L4.69 16.488a.833.833 0 1 1-1.178-1.178l2.357-2.357a.833.833 0 0 1 1.178 0M4.864 3.752A.833.833 0 0 0 3.685 4.93l1.768 1.768A.833.833 0 0 0 6.63 5.52z"
      fill="currentColor"
    />
  </svg>
);
export default LoaderIcon;
