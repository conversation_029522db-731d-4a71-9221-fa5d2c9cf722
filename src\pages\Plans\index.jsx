import DButton from '@/components/Global/DButton';
import useDanteApi from '@/hooks/useDanteApi';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import * as plansService from '@/services/plans.service';
import useLayoutStore from '@/stores/layout/layoutStore';
import DModalDowngrade from '@/components/DModalDowngrade';
import AllPlans from '@/components/AllPlans';
import { useUserStore } from '@/stores/user/userStore';
import { EMAILS } from '@/constants';

const Plans = () => {
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const [isDowngradeModalOpen, setIsDowngradeModalOpen] = useState(false);
  const [nextTier, setNextTier] = useState(null);
  const navigate = useNavigate();
  const user = useUserStore((state) => state.user);
  // const { data: plans } = useDanteApi(plansService.getPlans);
  // const [selectedMessages, setSelectedMessages] = useState(10000);
  // const [period, setPeriod] = useState('yearly');
  // const [selectedPlan, setSelectedPlan] = useState('Starter');
  // const { data: tiers } = useDanteApi(plansService.getTiers);

  const handleUpgrade = async (tierId) => {
    try {
      const response = await plansService.getCheckoutSession(tierId);
      if (response.status === 200) {
        window.open(response.data.checkout_session.url, '_blank');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleDowngrade = (tier) => {
    setNextTier(tier);
    setIsDowngradeModalOpen(true);
  };

  useEffect(() => {
    if (window.innerWidth < 768) {
      setLayoutTitle('Membership');
    }
  }, [window.innerWidth]);

  useEffect(() => {
    setProgressBar([]);
  }, []);

  return (
    <div className="w-full h-full bg-white rounded-size1 p-size5 flex flex-col gap-size5">
      <div className="flex flex-col gap-size0">
        <div className="flex justify-between items-center">
          <p className="text-xl font-medium tracking-tight">Plans</p>
          <DButton variant="grey" size="sm" onClick={() => navigate(-1)}>
            Close Plans
          </DButton>
        </div>
        <p className="text-base text-grey-75">
          You are currently on {user.tier_name}. If you would like to go further <a href={`mailto:${EMAILS.SALES}`} className="text-purple-300">
          contact our sales team
          </a>.
        </p>
      </div>

      <div className="flex-1 overflow-y-auto">
        {/* Move overflow-y-auto here, wrapping the AllPlans */}
        <AllPlans
          handleUpgrade={handleUpgrade}
          handleDowngrade={handleDowngrade}
        />
      </div>

      <DModalDowngrade
        isOpen={isDowngradeModalOpen}
        onClose={() => setIsDowngradeModalOpen(false)}
        nextTier={nextTier}
      />
    </div>
  );
};

export default Plans;
