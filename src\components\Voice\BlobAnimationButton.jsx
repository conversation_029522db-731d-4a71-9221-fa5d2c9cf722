import React from 'react';
import BlobAnimation from './BlobAnimation';
import AddIcon from '@/components/Global/Icons/AddIcon';
import './BlobAnimationButton.css';
import BlobButton from './BlobButton';

const BlobAnimationButton = ({ onClick, className, buttonText, whiteBlob4 = false }) => {
  return (
    <button
      className={`relative flex flex-col items-center justify-center blob-animation-button ${className} ${whiteBlob4 ? 'white-blob-4' : ''} `}
      onClick={onClick}
    >
      <div className="relative w-full h-28 md:h-40 rounded-size1 flex items-center justify-center overflow-visible transition-transform duration-200 ease-in-out hover:scale-110">
        <div className="absolute inset-0 rounded-size1 overflow-visible">
          <BlobButton className="overflow-visible" />
        </div>
        <div className="relative z-10 flex items-center justify-center rounded-full p-2">
          <AddIcon className="w-7 h-7 text-purple-500" />
        </div>
      </div>
      {buttonText && (
        <span className="mt-7 text-base md:text-lg font-medium">{buttonText}</span>
      )}
    </button>
  );
};

export default BlobAnimationButton;
