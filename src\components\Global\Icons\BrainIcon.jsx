import * as React from 'react';
const BrainIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.977 3.53A1.25 1.25 0 0 1 9.5 4.75V15.6a.9.9 0 0 1-1.8 0q0-.03-.004-.06a.5.5 0 0 0-.662-.531 1.602 1.602 0 0 1-2.075-1.928 1.6 1.6 0 0 1 1.542-******** 0 1 0-.002-1 2.6 2.6 0 0 0-2.028.977 2.3 2.3 0 0 1-.172-******** 0 0 0 .152-.235.5.5 0 0 0-.041-.48A1.25 1.25 0 0 1 6.2 5.85c.**************.254.098a.499.499 0 0 0 .665-.666 1.25 1.25 0 0 1 .858-1.752m-3.97 9.242a3.3 3.3 0 0 1-.62-5.023A2.25 2.25 0 0 1 6 4.668a2.25 2.25 0 0 1 4-1.332 2.25 2.25 0 0 1 2.241-.782 2.25 2.25 0 0 1 1.758 2.114 2.25 2.25 0 0 1 2.614 3.08 3.3 3.3 0 0 1 .887 2.253c0 .578-.15 1.124-.413 1.599a.5.5 0 1 1-.874-.486 2.3 2.3 0 0 0-.512-2.856.5.5 0 0 1-.152-.235.5.5 0 0 1 .041-.48A1.25 1.25 0 0 0 13.8 5.85a.5.5 0 0 1-.254.098.499.499 0 0 1-.665-.666A1.25 1.25 0 1 0 10.5 4.75V6.5a1.6 1.6 0 0 0 1.6 ******* 0 0 1 0 1 2.6 2.6 0 0 1-1.6-.55v7.05a.9.9 0 0 0 .******* 0 1 1 0 1 1.9 1.9 0 0 1-1.4-.615q-.027.03-.057.059a1.9 1.9 0 0 1-3.18-.857 2.6 2.6 0 0 1-2.4-1.109 2.6 2.6 0 0 1-.356-2.206m11.097.27a.5.5 0 0 0-.874-.485l-1.167 2.1a.5.5 0 0 0 .437.743h1.95l-.754 1.357a.5.5 0 1 0 .874.486l1.167-2.1a.5.5 0 0 0-.437-.743h-1.95z"
      fill="currentColor"
    />
  </svg>
);
export default BrainIcon;
