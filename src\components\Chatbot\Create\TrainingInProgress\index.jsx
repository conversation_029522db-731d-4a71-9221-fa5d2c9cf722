import DLoaderTraining from '@/components/Global/DLoaderTraining';
import { TASK_POLLING_INTERVAL } from '@/constants';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import { Transition } from '@headlessui/react';
import { useEffect, useState } from 'react';

const messages = [
  'Analyzing the structure of your content...',
  'Teaching your chatbot to understand intent.',
  'Indexing files for faster access later.',
  'Cross-referencing data like a pro librarian.',
  'Optimizing conversation flows behind the scenes.',
  'Running a quick sanity check on your inputs.',
  'Cleaning up noisy data — neat and tidy now.',
  'Loading language patterns into memory.',
  'Simulating small talk for better engagement.',
  'Extracting key insights from your files.',
  'Warming up neural pathways for smarter replies.',
  'Aligning data points for smoother conversations.',
  'Adjusting parameters for natural interaction.',
  'Running silent checks — all systems listening.',
];

const TrainingInProgress = ({ progress = 0, progressText = '' }) => {
  const [message, setMessage] = useState(messages[0]);

  const isAboveSm = useIsAboveBreakpoint('sm');

  useEffect(() => {
    const interval = setInterval(() => {
      setMessage(messages[Math.floor(Math.random() * messages.length)]);
    }, TASK_POLLING_INTERVAL * 3);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="h-full w-full flex flex-col items-center justify-center">
      <DLoaderTraining size={isAboveSm ? 500 : 250} />
      <p className="text-xl tracking-tight text-center">{message}</p>
      <div className="mt-size2 max-w-[300px] w-full flex flex-col items-center justify-center gap-size2">
        <div className="w-full h-1 bg-grey-5 rounded-full  ">
          <div
            className="h-full bg-purple-200 rounded-full"
            style={{ width: `${progress * 100}%` }}
          ></div>
        </div>
        <p className="text-grey-50 text-sm tracking-tight">
          {(progress * 100).toFixed(0)}%
        </p>

        <Transition show={!!progressText}>
          <p className="transition transition-opacity duration-300 text-grey-50 text-xs tracking-tight">
            {progressText}
          </p>
        </Transition>
      </div>
    </div>
  );
};
export default TrainingInProgress;
