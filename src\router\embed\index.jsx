import DLoading from '@/components/DLoading';
import BubblePage from '@/pages/BubblePage';
import { lazy, Suspense } from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
const ErrorPage = lazy(() => import('@/pages/Error'));
const EmbedPage = lazy(() => import('@/pages/EmbedPage'));
const TooltipsPage = lazy(() => import('@/pages/TooltipsPage'));
const Demo = lazy(() => import('@/pages/DemoEmbed'));
/** @type {import('react-router-dom').RouteObject[]} */
const sharedRoutes = [
  {
    path: '/',
    errorElement: (
      <>
        <ErrorPage />
      </>
    ),
    element: (
      <>
        <EmbedPage />
      </>
    ),
  },
  {
    path: '/bubble',
    errorElement: (
      <>
        <ErrorPage />
      </>
    ),
    element: (
      <>
        <BubblePage />
      </>
    ),
  },
  {
    path: '/tooltips',
    errorElement: (
      <>
        <ErrorPage />
      </>
    ),
    element: (
      <>
        <TooltipsPage />
      </>
    ),
  },
  {
    path: '/demo',
    errorElement: (
      <>
        <ErrorPage />
      </>
    ),
    element: <Demo />,
  },
];

const routes = [...sharedRoutes];

const router = createBrowserRouter(routes, { basename: '/embed' });

const Routers = () => {
  return (
    <Suspense fallback={<DLoading show={true} />}>
      <RouterProvider router={router} />
    </Suspense>
  );
};
export default Routers;
