import React, { useState } from 'react';

import DButton from '../../Global/DButton';
import DUserAvatar from '../../Global/DUserAvatar';
import DeleteIcon from '../../Global/Icons/DeleteIcon';
import OpenAiIcon from '../../Global/Icons/OpenAiLogoIcon';
import DInput from '../../Global/DInput/DInput';
import * as userService from '@/services/user.service';
import useToast from '@/hooks/useToast';
import useLogout from '@/application/auth/logout';
import DConfirmationModal from '@/components/Global/DConfirmationModal';

const ProfileDetails = ({ user }) => {
  const [linkCopied, setLinkCopied] = useState(false);
  const [editOpenAiKey, setEditOpenAiKey] = useState(false);
  const [openAiKey, setOpenAiKey] = useState(user?.openai_api_key);
  const [isLoading, setIsLoading] = useState(false);
  const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
  const { addSuccessToast } = useToast();
  const logout = useLogout();

  const handleSaveOpenAiKey = async () => {
    try {
      const response = await userService.createOpenAIKey(openAiKey);
      console.log(response);
    } catch (e) {
      console.log(e);
    }
  };

  const handleCopyReferralLink = () => {
    const text = `${import.meta.env.VITE_APP_BASE_URL}/sign-up?reffered_by=${
      user?.id
    }`;
    navigator.clipboard.writeText(text);
    setLinkCopied(true);
  };

  return (
    <>
      <div className="w-full  h-px bg-grey-5"></div>

      <div className="flex gap-size3 items-center">
        <span className="flex items-center justify-center size-[56px] md:size-[80px] rounded-full">
          <DUserAvatar
            size="xl"
            image={user?.profile_image || ''}
            fullName={user?.full_name}
          />
        </span>
        <div className="flex flex-col">
          <p className="text-xl">{user?.full_name}</p>
          <p className="">{user?.email}</p>
        </div>
      </div>
      {user?.team_key && (
        <div className="flex flex-col md:flex-row bg-grey-2 rounded-size0 p-size3 justify-between gap-size2 items-start">
          <div className="flex flex-col">
            <p className="text-xl">{user?.team_key?.team_name}</p>
            <p className="text-grey-50">
              <span className="text-black">{user?.team_key?.used_seats}</span>/
              {user?.team_key?.total_seats} free seats left
            </p>
          </div>
          <DButton
            variant="grey"
            size="sm"
            className="w-max"
            onClick={() =>
              navigate(`/team-management/${user?.selected_team_id}`)
            }
          >
            Manage team
          </DButton>
        </div>
      )}
      <div className="w-full  h-px bg-grey-5"></div>
      <div className="flex gap-size2 rounded-size0 bg-purple-5 p-size3 justify-between text-lg invite-background">
        <div className="flex flex-col gap-size2 ">
          <p className="flex flex-col gap-size0">
            <span>Invite friends and receive </span>
            <span>
              <span className="text-purple-300 text-medium">$10</span> worth of
              credits{' '}
            </span>
          </p>
          <DButton variant="dark" onClick={handleCopyReferralLink}>
            {linkCopied ? 'Link copied' : 'Copy referral link'}
          </DButton>
        </div>
      </div>

      {/* <div className="flex gap-size2 rounded-size2 p-size2 justify-between items-center">
        <div className="flex gap-size2">
          <OpenAiIcon />
          <p>OpenAI API key</p>
        </div>
        {editOpenAiKey ? (
          <DButton variant="grey" onClick={handleSaveOpenAiKey}>
            Save
          </DButton>
        ) : (
          <DButton variant="grey" onClick={() => setEditOpenAiKey(true)}>
            Edit
          </DButton>
        )}
      </div> */}
      {editOpenAiKey && (
        <DInput
          value={openAiKey}
          onChange={(e) => setOpenAiKey(e.target.value)}
        />
      )}

      <DButton
        className="bg-negative-2 text-negative-100 !justify-start !text-left"
        onClick={() => setOpenConfirmDelete(true)}
      >
        <DeleteIcon /> Delete my account
      </DButton>
      <DConfirmationModal
        open={openConfirmDelete}
        onClose={() => setOpenConfirmDelete(false)}
        onConfirm={handleDeleteAccount}
        title="Delete account"
        description="Are you sure you want to delete your account? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variantConfirm="danger"
        loading={isLoading}
      />
    </>
  );
};

export default ProfileDetails;
