import React from 'react';
import clsx from 'clsx';

const NewBadge = ({ className, size = 'sm', textSize, ...props }) => {
  const badgeSize = size === 'sm' ? 'text-xs' : 'text-sm';
  
  return (
    <div
      className={clsx(
        'h-4 px-1.5 rounded-full font-medium inline-flex items-center justify-center text-white',
        badgeSize,
        className
      )}
      style={{
        background: 'linear-gradient(87deg, #6474FF -15.34%, #8078FD 21.77%, #CF78FE 83.94%)',
        boxShadow: '0 2px 4px rgba(207, 120, 254, 0.3)'
      }}
      {...props}
    >
     <span className={clsx('text-white leading-none', textSize)}>New</span>
    </div>
  );
};

export default NewBadge; 