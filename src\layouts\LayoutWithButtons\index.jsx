import DButton from '@/components/Global/DButton';
import DToastContainer from '@/components/DToast/DToastContainer';
import clsx from 'clsx';
import './index.css';
import GlobalModals from '@/components/GlobalModals';

const LayoutWithButtons = ({
  children,
  footer,
  className,
  removePadding = false,
}) => {
  return (
    <div
      className={clsx(
        'layout_buttons_wrapper',
        'bg-white',
        'grow',
        !removePadding && 'p-size5',
        'gap-size0',
        'rounded-size1',
        'flex',
        'flex-col',
        'h-[1px]',
        'md:h-full',
        className && className
      )}
    >
      <div className="layout_buttons-childred_wrapper grow h-full">
        <div className="flex flex-col grow overflow-y-auto h-full overflow-x-hidden no-scrollbar 3xl:max-w-[1200px] 3xl:mx-auto">
          {children}
        </div>
      </div>
      {footer}
      <GlobalModals />
    </div>
  );
};

export default LayoutWithButtons;
