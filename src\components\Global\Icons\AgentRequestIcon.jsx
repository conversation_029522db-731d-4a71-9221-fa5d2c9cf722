// src/components/Global/Icons/AgentRequestIcon.jsx

import * as React from 'react';

const AgentRequestIcon = (props) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path fillRule="evenodd" clipRule="evenodd" d="M10.339 2.23704C10.2437 2.158 10.1238 2.11475 10 2.11475C9.8762 2.11475 9.75629 2.158 9.661 2.23704C7.66352 3.89906 5.17862 4.86451 2.583 4.98704C2.46605 4.99064 2.35406 5.03514 2.26654 5.1128C2.17901 5.19045 2.1215 5.29635 2.104 5.41204C2.03455 5.93852 1.99981 6.469 2 7.00004C2 12.163 5.26 16.564 9.834 18.257C9.94114 18.2965 10.0589 18.2965 10.166 18.257C14.74 16.564 18 12.163 18 7.00004C18 6.46204 17.965 5.93104 17.896 5.41104C17.8785 5.29518 17.8208 5.18916 17.7331 5.11148C17.6454 5.03381 17.5331 4.98941 17.416 4.98604C14.8207 4.8633 12.3362 3.89886 10.339 2.23704ZM10 6.00004C10.1989 6.00004 10.3897 6.07906 10.5303 6.21971C10.671 6.36036 10.75 6.55113 10.75 6.75004V10.25C10.75 10.449 10.671 10.6397 10.5303 10.7804C10.3897 10.921 10.1989 11 10 11C9.80109 11 9.61032 10.921 9.46967 10.7804C9.32902 10.6397 9.25 10.449 9.25 10.25V6.75004C9.25 6.55113 9.32902 6.36036 9.46967 6.21971C9.61032 6.07906 9.80109 6.00004 10 6.00004ZM10 15C10.2652 15 10.5196 14.8947 10.7071 14.7071C10.8946 14.5196 11 14.2653 11 14C11 13.7348 10.8946 13.4805 10.7071 13.2929C10.5196 13.1054 10.2652 13 10 13C9.73478 13 9.48043 13.1054 9.29289 13.2929C9.10536 13.4805 9 13.7348 9 14C9 14.2653 9.10536 14.5196 9.29289 14.7071C9.48043 14.8947 9.73478 15 10 15Z" fill="#FF9C28"/>
    </svg>
);

export default AgentRequestIcon;
