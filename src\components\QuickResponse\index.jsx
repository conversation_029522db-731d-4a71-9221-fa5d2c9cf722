import DButton from '../Global/DButton';
import DButtonIcon from '../Global/DButtonIcon';
import DeleteIcon from '../Global/Icons/DeleteIcon';

const QuickResponse = ({ title, description, onDelete, onEdit }) => {
    return (
        <div className="rounded-size1 bg-grey-2 p-size3 flex flex-col gap-size3 w-full">
            <p className="text-lg tracking-tight font-medium">{title}</p>
            <div className="w-full h-[1px] bg-grey-5"></div>
            <p className="text-xs tracking-tight">{description}</p>
            <div className="flex gap-size3 mt-auto">
                <DButton variant="grey" fullWidth onClick={onEdit}>
                    Edit
                </DButton>
                <DButtonIcon variant="outlined" className="!rounded-size0" onClick={onDelete}>
                    <DeleteIcon />
                </DButtonIcon>
            </div>
        </div>
    )
}

export default QuickResponse;