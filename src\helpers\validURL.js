function isValidURL(url) {
  // Regular expression to validate URL format
  const urlPattern = new RegExp(
    '^((https?|ftp):\\/\\/)?' + // Protocol (optional)
    '([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}|localhost)' + // Domain name or localhost
    '(\\:\\d+)?' + // Port (optional)
    '(\\/[-a-zA-Z0-9%_.~+]*)*' + // Path (optional)
    '(\\?[;&a-zA-Z0-9%_.~+=-]*)?' + // Query string (optional)
    '(\\#[-a-zA-Z0-9_]*)?$' // Fragment identifier (optional)
  );

  try {
    const parsedURL = new URL(url);
    return urlPattern.test(url) && parsedURL.hostname !== '';
  } catch (error) {
    return false;
  }
}

export default isValidURL;