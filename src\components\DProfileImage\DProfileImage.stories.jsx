import React, { useState } from 'react';
import DProfileImage from '.';
import { ACCEPTED_IMAGE_TYPES } from '@/constants';

export default {
  title: 'Components/DProfileImage',
  component: DProfileImage,
  argTypes: {
    label: { control: 'text' },
    isRounded: { control: 'boolean' },
    imageUrl: { control: 'text' },
    defaultImages: { control: 'array' },
    maxSizeFile: { control: 'number', defaultValue: 150 * 1024 },
  },
};

const Template = (args) => {
  const [imageUrl, setImageUrl] = useState(args.imageUrl || '');
  const [imageFile, setImageFile] = useState(null);
  const [errors, setErrors] = useState({});

  const handleImageChange = (e) => {
    if (e?.target?.files?.[0]) {
      const file = e.target.files[0];
      if (ACCEPTED_IMAGE_TYPES.includes(file.type)) {
        setImageFile(file);
        setImageUrl(URL.createObjectURL(file));
      } else {
        alert('Invalid file type!');
      }
    } else if (typeof e === 'string') {
      setImageUrl(e);
      setImageFile(null);
    }
  };

  const setImageError = (name, errorMessage) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: errorMessage,
    }));
  };

  return (
    <DProfileImage
      {...args}
      name="image"
      imageUrl={imageUrl}
      imageFile={imageFile}
      handleImageChange={handleImageChange}
      setImageError={setImageError}
      error={errors.image}
    />
  );
};

export const Default = Template.bind({});
Default.args = {
  label: 'Profile Image',
  isRounded: true,
  imageUrl: '',
  defaultImages: [
    {
      small_image_url: 'https://robohash.org/F7L.png?set=set1',
      big_image_url: 'https://robohash.org/F7L.png?set=set1',
    },
    {
      small_image_url: 'https://robohash.org/F8L.png?set=set1',
      big_image_url: 'https://robohash.org/F8L.png?set=set1',
    },
  ],
};

export const Squared = Template.bind({});
Squared.args = {
  label: 'Profile Image',
  isRounded: false,
  imageUrl: '',
  defaultImages: [
    {
      small_image_url: 'https://robohash.org/F7L.png?set=set1',
      big_image_url: 'https://robohash.org/F7L.png?set=set1',
    },
    {
      small_image_url: 'https://robohash.org/F8L.png?set=set1',
      big_image_url: 'https://robohash.org/F8L.png?set=set1',
    },
  ],
};
