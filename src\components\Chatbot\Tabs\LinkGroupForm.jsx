import { useState } from 'react';

import DDraggableContainer from '../../Global/DDraggableContainer';
import DCheckbox from '../../Global/DCheckbox';
import DButton from '../../Global/DButton';
import AddIcon from '../../Global/Icons/AddIcon';
import DInput from '@/components/Global/DInput/DInput';
import SortableEditableInput from '@/components/Global/SortableEditableInput';
import { useParams } from 'react-router-dom';
import * as tabsService from '@/services/tabs.service';
import { v4 as uuidv4 } from 'uuid';
const LinkGroupForm = ({ setIsSaving, closeModal, item, refetch, slots, setSlots, chatbotId }) => {
  const params = useParams();
  const [linkGroupTitle, setLinkGroupTitle] = useState(item?.title ?? '');
  const [openAllInNewTab, setOpenAllInNewTab] = useState(item?.open_all_in_new_tab ?? true);
  const [links, setLinks] = useState(item?.items?.map(item => ({
    id: item.id,
    frontend_id: item.id || item.frontend_id,
    input_label: item.name,
    input_value: item.url,
    is_mandatory: false,
    is_deletable: true,
    is_editable: true,
    hasInput: true,
    placeholder: 'Please enter link name',
    placeholder_input: 'https://www.example.com',
  })) ?? [
    {
      frontend_id: uuidv4(),
      input_label: '',
      input_value: 'https://',
      is_mandatory: false,
      is_deletable: true,
      is_editable: true,
      hasInput: true,
      placeholder: 'Please enter link name',
      placeholder_input: 'https://www.example.com',
    }
  ]);
  const [error, setError] = useState({});
  const [submitted, setSubmitted] = useState(false);

  const validateForm = () => {
    const errors = {};

    // Validate link group title
    if (linkGroupTitle.trim() === '') {
      errors.linkGroupTitle = 'Link group title is required';
    }

    // Check if each link has both name and URL
    links.forEach((link) => {
      if (link.input_label.trim() === '' || link.input_value.trim() === '') {
        errors[`linkItem${link.id}`] = 'Both link name and URL are required';
      }
      try {
        new URL(link.input_value);
      } catch (error) {
        errors[`linkItem${link.id}`] = 'Invalid URL';
      }
    });

    setError(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddLink = () => {
    setLinks([...links, {
      frontend_id: uuidv4(),
      input_label: '',
      input_value: 'https://',
      is_mandatory: false,
      is_deletable: true,
      is_editable: true,
      hasInput: true,
      placeholder: 'Please enter link name',
      placeholder_input: 'https://www.example.com',
    }]);
  };

  const handleItemFieldChange = (id, value) => {
    const updatedLinks = links.map(link => link.frontend_id === id ? { ...link, input_label: value } : link);
    setLinks(updatedLinks);
  };

  const handleInputChange = (id, value) => {
    const updatedLinks = links.map(link => link.frontend_id === id ? { ...link, input_value: value } : link);
    setLinks(updatedLinks);
  };

  const handleDeleteLink = (id) => {
    const updatedLinks = links.filter(link => link.frontend_id !== id);
    setLinks(updatedLinks);
  };

  const handleSubmit = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);
    const payload = {
      kb_id: params.id ?? chatbotId,
      title: linkGroupTitle,
      frontend_id: uuidv4(),
      items: links.map((link, index) => ({
        id: link.id,
        frontend_id: link.frontend_id,
        name: link.input_label,
        url: link.input_value,
        order: index
      })),
      open_all_in_new_tab: openAllInNewTab,
      order: slots.length + 1
    }
    setSlots((prevSlots) => [...prevSlots, { ...payload, type: 'link_group', disclaimer: 'Link group' }]);
    closeModal();
  };

  const handleUpdate = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);
    setSlots((prevSlots) => {
      return prevSlots.map(slot => 
        (slot.frontend_id && item.frontend_id ? slot.frontend_id === item.frontend_id : slot.id === item.id) ? { 
          ...slot, 
          title: linkGroupTitle, 
          order: item.order,
          frontend_id: item.frontend_id,
          items: links.map((link, index) => ({
            id: link?.id ?? null,
            frontend_id: link.frontend_id ?? link.id,
            name: link.input_label,
            url: link.input_value,
            order: index
          })),
          open_all_in_new_tab: openAllInNewTab,
          disclaimer: 'Link group'
        } : slot
      )}
    );
    closeModal();
    setIsSaving(false);
  };

  const handleEditLink = (id) => {
    console.log('id', id);
  };

  return (
    <div className="flex flex-col gap-size5">
      <div className="flex flex-col gap-size1">
        <p className="text-base tracking-tight font-medium">Link group title</p>
        <DInput
          placeholder="Enter link group title"
          value={linkGroupTitle}
          onChange={(e) => setLinkGroupTitle(e.target.value)}
          error={submitted ? error.linkGroupTitle : ''}
        />
      </div>
      <div className="w-full h-px bg-grey-5"></div>
      <div className="flex flex-col gap-size1 overflow-y-auto max-h-[300px] no-scrollbar">
        <DDraggableContainer
          items={links}
          setItems={setLinks}
          ItemComponent={SortableEditableInput}
          handleFieldChange={handleItemFieldChange}
          handleDelete={handleDeleteLink}
          handleInputChange={handleInputChange}
          onEdit={handleEditLink}
          error={error} 
        />
      </div>
      <button className="dbutton flex gap-size1 items-center text-xs tracking-tight" onClick={handleAddLink}>
        <AddIcon />
        <span>Add another link</span>
      </button>
      <div className="w-full h-px bg-grey-5"></div>
      <DCheckbox
        label="Open all in new tab"
        checked={openAllInNewTab}
        onChange={(checked) => setOpenAllInNewTab(checked)}
      />
      {item && Object.keys(item).length > 0 ? (
        <DButton variant="dark" onClick={handleUpdate} fullWidth>Update</DButton>
      ) : (
        <DButton variant="dark" onClick={handleSubmit} fullWidth>Complete</DButton>
      )}
    </div>
  );
};

export default LinkGroupForm;
