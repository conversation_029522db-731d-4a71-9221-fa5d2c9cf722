// Import necessary components
import DButton from '../DButton';
import DModal from '.';

// Sample envelope image for the story
const EnvelopeImage = () => (
  <div className="flex items-center justify-center w-full h-full">
    <svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg" className="max-w-full">
      <path d="M150 30H30C24.4772 30 20 34.4772 20 40V140C20 145.523 24.4772 150 30 150H150C155.523 150 160 145.523 160 140V40C160 34.4772 155.523 30 150 30Z" fill="white" stroke="#E6E6E6" strokeWidth="2"/>
      <path d="M160 40L94.6863 105.314C91.7889 108.211 87.2111 108.211 84.3137 105.314L20 41" stroke="#E6E6E6" strokeWidth="2"/>
      <circle cx="90" cy="90" r="15" fill="#FF6B81"/>
    </svg>
  </div>
);

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Global/DModal',
  component: DModal,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    isOpen: {
      default: true,
      control: { type: 'boolean' }
    },
    type: {
      options: ['text', 'password'],
      control: { type: 'radio' }
    },
    iconPlacement: {
      options: ['pre', 'post'],
      control: { type: 'radio' }
    }
  },
  args: {
    title: 'Title',
    isOpen: true,
    onClose: () => {},
    children: <div>Children</div>,
    footer: <>Footer</>
  },
  decorators: [
    (Story) => (
      <div className="static">
        <Story />
      </div>
    )
  ]
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    title: 'Confirm',
    isOpen: true,
    onClose: () => {},
    children: <div>You are about to log out of your account, are you sure you want to that?</div>,
    footer: (
      <>
        <DButton variant="dark" size="sm" className="w-[184px]">
          Yes, log me out
        </DButton>
        <DButton variant="outlined" size="sm">
          No, keep me inside
        </DButton>
      </>
    )
  }
};
export const WithoutTitle = {
  args: {
    isOpen: true,
    onClose: () => {},
    children: <div>You are about to log out of your account, are you sure you want to that?</div>,
    footer: (
      <>
        <DButton variant="dark" size="sm" className="w-[184px]">
          Yes, log me out
        </DButton>
        <DButton variant="outlined" size="sm">
          No, keep me inside
        </DButton>
      </>
    )
  }
};

export const WithImageSection = {
  args: {
    title: 'Would you like to hear from us?',
    subtitle: 'You can control email preferences in Account Settings.',
    isOpen: true,
    onClose: () => {},
    imageSection: <EnvelopeImage />,
    children: (
      <div className="flex flex-col gap-size3">
        <div className="flex items-center gap-size2">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z" fill="#8275F7"/>
          </svg>
          <span>Receive discounts, tips and expert advice</span>
        </div>
        <div className="flex items-center gap-size2">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z" fill="#8275F7"/>
          </svg>
          <span>Early access to new features and tools</span>
        </div>
        <div className="flex items-center gap-size2">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z" fill="#8275F7"/>
          </svg>
          <span>What's new updates and releases</span>
        </div>
        <div className="flex items-center gap-size2">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM16.59 7.58L10 14.17L7.41 11.59L6 13L10 17L18 9L16.59 7.58Z" fill="#8275F7"/>
          </svg>
          <span>Free online courses and expert advice</span>
        </div>
      </div>
    ),
    footer: (
      <div className="flex gap-size2 w-full">
        <DButton variant="outlined" size="md" className="w-1/2">
          Maybe later
        </DButton>
        <DButton variant="dark" size="md" className="w-1/2">
          Yes, Opt me in!
        </DButton>
      </div>
    )
  }
};

export const WithGradientBackground = {
  args: {
    title: 'Subscribe to our newsletter',
    isOpen: true,
    onClose: () => {},
    imageSection: (
      <div className="flex items-center justify-center w-full h-full">
        <svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg" className="max-w-full">
          <path d="M90 20C51.34 20 20 51.34 20 90C20 128.66 51.34 160 90 160C128.66 160 160 128.66 160 90C160 51.34 128.66 20 90 20ZM90 150C56.86 150 30 123.14 30 90C30 56.86 56.86 30 90 30C123.14 30 150 56.86 150 90C150 123.14 123.14 150 90 150Z" fill="white"/>
          <path d="M95 50H85V95H130V85H95V50Z" fill="white"/>
        </svg>
      </div>
    ),
    // Using custom gradient background
    imageBgColor: 'bg-gradient-to-br from-[#F0F2FF] to-[#E6F8F0]',
    children: (
      <div className="flex flex-col gap-size3">
        <p>Stay updated with our latest news, product updates, and exclusive offers. We promise not to spam your inbox!</p>
        <div className="flex flex-col gap-size1">
          <label className="text-sm font-medium">Email address</label>
          <input type="email" placeholder="Enter your email" className="border border-grey-5 rounded-size1 p-size2" />
        </div>
      </div>
    ),
    footer: (
      <div className="flex gap-size2 w-full">
        <DButton variant="outlined" size="md" className="w-1/2">
          No, thanks
        </DButton>
        <DButton variant="dark" size="md" className="w-1/2">
          Subscribe
        </DButton>
      </div>
    )
  }
};

export const WithCompactContent = {
  args: {
    title: 'Compact Modal',
    isOpen: true,
    onClose: () => {},
    imageSection: (
      <div className="flex items-center justify-center w-full h-full">
        <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg" className="max-w-full">
          <circle cx="60" cy="60" r="40" fill="#F0F2FF" stroke="#E6E6E6" strokeWidth="2"/>
          <path d="M70 50L55 65L50 60" stroke="#8275F7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </div>
    ),
    children: (
      <div className="flex flex-col gap-size2">
        <p className="text-sm">This is a compact modal with minimal content.</p>
      </div>
    ),
    footer: (
      <div className="flex justify-end w-full">
        <DButton variant="dark" size="sm">
          Got it
        </DButton>
      </div>
    )
  }
};

export const WithExpandedContent = {
  args: {
    title: 'Expanded Modal with Longer Content',
    subtitle: 'This modal will expand to fit the content',
    isOpen: true,
    onClose: () => {},
    imageSection: (
      <div className="flex items-center justify-center w-full h-full">
        <svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" className="max-w-full">
          <rect x="40" y="40" width="120" height="120" rx="8" fill="#F0F2FF" stroke="#E6E6E6" strokeWidth="2"/>
          <path d="M70 100H130M70 80H130M70 120H110" stroke="#8275F7" strokeWidth="2" strokeLinecap="round"/>
        </svg>
      </div>
    ),
    children: (
      <div className="flex flex-col gap-size4">
        <p>This modal contains more content to demonstrate how it expands to fit the content while maintaining the minimum width.</p>
        <p>The modal will automatically adjust its width based on the content, but will never be smaller than the minimum width or larger than the maximum width.</p>
        <div className="flex flex-col gap-size2">
          <h3 className="font-medium">Features:</h3>
          <ul className="list-disc pl-size5 space-y-size1">
            <li>Responsive sizing</li>
            <li>Minimum width of 600px</li>
            <li>Maximum width of 900px</li>
            <li>Automatic expansion based on content</li>
            <li>Maintains side-by-side layout on desktop</li>
            <li>Stacks vertically on mobile</li>
          </ul>
        </div>
      </div>
    ),
    footer: (
      <div className="flex gap-size2 w-full">
        <DButton variant="outlined" size="md" className="w-1/2">
          Cancel
        </DButton>
        <DButton variant="dark" size="md" className="w-1/2">
          Continue
        </DButton>
      </div>
    )
  }
};
