import * as React from 'react';
const AiAvatarIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.24 4.558c0-.952.788-1.724 1.76-1.724.973 0 1.762.772 1.762 1.724S10.973 6.282 10 6.282 8.24 5.51 8.24 4.558M10 2C8.559 2 7.389 3.145 7.389 4.558s1.17 2.558 2.613 2.558 2.613-1.145 2.613-2.558S11.444 2 10 2M6.277 9.64a3.07 3.07 0 0 1 2.24-.965h2.967c.852 0 1.665.35 2.24.965l3.73 3.984a.713.713 0 0 1-.022 1 .75.75 0 0 1-.966.068l-3.202-2.35a.43.43 0 0 0-.5-.009.41.41 0 0 0-.163.462l1.503 4.908c.068.22.306.346.53.28a.416.416 0 0 0 .287-.52l-1.132-3.694 2.165 1.59c.634.466 1.52.404 2.08-.144.603-.59.624-1.54.049-2.155l-3.73-3.984a3.93 3.93 0 0 0-2.869-1.235H8.517a3.93 3.93 0 0 0-2.869 1.235l-3.73 3.984a1.535 1.535 0 0 0 .048 2.155c.56.548 1.447.61 2.08.145l2.166-1.59-1.132 3.693c-.067.22.06.453.286.52a.43.43 0 0 0 .53-.28L7.4 12.795a.41.41 0 0 0-.164-.462.43.43 0 0 0-.5.008l-3.2 2.351a.75.75 0 0 1-.967-.067.713.713 0 0 1-.022-1.001z"
      fill="currentColor"
    />
  </svg>
);
export default AiAvatarIcon;
