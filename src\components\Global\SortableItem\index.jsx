import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

const SortableItem = ({ id, item, ItemComponent, ...rest }) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const dragHandleProps = {
    ...listeners,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes}>
      <ItemComponent item={item} dragHandleProps={dragHandleProps} {...rest}  />
    </div>
  );
};

export default SortableItem;