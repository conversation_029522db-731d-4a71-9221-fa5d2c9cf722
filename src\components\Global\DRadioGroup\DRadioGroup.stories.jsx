import React, { useState } from 'react';
import DRadioGroup from './index';

const options = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
];

export default {
  title: 'Global/DRadioGroup',
  component: DRadioGroup,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { control: 'text' },
    size: { control: 'select', options: ['sm', 'md'] },
    variant: { control: 'select', options: ['stack', 'inline'] },
    error: { control: 'text' },
    required: { control: 'boolean' },
    hideError: { control: 'boolean' },
  },
};

const Template = ({ label, size, variant, error, required, hideError }) => {
  const [value, setValue] = useState('');

  return (
    <DRadioGroup
      options={options}
      value={value}
      onChange={setValue}
      label={label}
      size={size}
      variant={variant}
      error={error}
      required={required}
      hideError={hideError}
      name="example-radio-group"
    />
  );
};

export const Default = Template.bind({});
Default.args = {
  label: 'Select an option',
  size: 'md',
  variant: 'stack',
};

export const WithValidationError = Template.bind({});
WithValidationError.args = {
  label: 'Select an option',
  size: 'md',
  error: 'You must select an option.',
  required: true,
};

export const SmallSize = Template.bind({});
SmallSize.args = {
  label: 'Select an option',
  size: 'sm',
};

export const InlineVariant = Template.bind({});
InlineVariant.args = {
  label: 'Select an option',
  size: 'md',
  variant: 'inline',
};

export const HiddenError = Template.bind({});
HiddenError.args = {
  label: 'Select an option',
  size: 'md',
  error: 'You must select an option.',
  hideError: true,
};
