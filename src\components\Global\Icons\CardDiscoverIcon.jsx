import * as React from 'react';
const CardDiscoverIcon = (props) => (
  <svg
    width={24}
    height={16}
    viewBox="0 0 24 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M24 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h20a2 2 0 0 1 2 2"
      fill="#212B36"
    />
    <path
      d="M24 8v5.982A2.023 2.023 0 0 1 21.972 16H1.69a2 2 0 0 1-.69-.121 73 73 0 0 0 4.959-.888c.426-.08.842-.181 1.257-.272 6.704-1.524 12.505-3.763 15.77-5.962.365-.253.71-.505 1.014-.757"
      fill="#EE7623"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2 5.32h.91V5.3a1.61 1.61 0 0 1 1.15 2.84 1.7 1.7 0 0 1-1.15.36H2zM2.79 8c.313.027.623-.074.86-.28a1.07 1.07 0 0 0 .35-.8 1.06 1.06 0 0 0-.35-.8 1.17 1.17 0 0 0-.86-.26h-.16V8z"
      fill="#fff"
    />
    <path
      fill="#fff"
      d="M4.925 5.315h.625v3.21h-.625zm2.16 1.23c-.375-.135-.5-.23-.5-.405s.195-.355.465-.355c.**************.5.26l.325-.425a1.38 1.38 0 0 0-.94-.36.94.94 0 0 0-1 .92c0 .445.2.67.785.88q.226.072.435.185a.38.38 0 0 1 .*********** 0 0 1-.5.455.74.74 0 0 1-.68-.43l-.405.39c.236.4.676.635 1.14.61A1.03 1.03 0 0 0 8 7.55c0-.51-.205-.745-.915-1.005m1.12.375A1.66 1.66 0 0 0 9.9 8.595a1.7 1.7 0 0 0 .785-.185v-.735a1 1 0 0 1-.755.35 1.055 1.055 0 0 1-1.08-1.11 1.065 1.065 0 0 1 1.05-1.1c.301.001.588.132.785.36V5.44c-.236-.13-.5-.2-.77-.2a1.69 1.69 0 0 0-1.71 1.68m7.445.55-.86-2.155h-.68l1.36 3.29h.335l1.39-3.29h-.68zm1.825 1.055h1.78V7.98H18.1v-.865h1.11V6.57H18.1v-.71h1.155v-.545h-1.78z"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.73 6.26a.865.865 0 0 1-.73.92l1 1.32h-.76l-.87-1.27h-.07V8.5h-.63V5.32h.92c.73 0 1.14.34 1.14.94m-1.43.53h.2c.37 0 .59-.13.59-.47s-.21-.5-.59-.5h-.2z"
      fill="#fff"
    />
    <path
      d="M14.295 6.92a1.705 1.705 0 1 1-3.41 0 1.705 1.705 0 0 1 3.41 0"
      fill="#EE7623"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M22.27 5.47a.15.15 0 0 1-.12.14l.18.26h-.16l-.18-.24v.24h-.14v-.58h.2c.14 0 .22.06.22.18m-.14 0c0-.06-.04-.06-.1-.06v.12h.02c.053 0 .072 0 .079-.009.005-.008.001-.023.001-.051"
      fill="#fff"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.73 5.68a.5.5 0 1 1 1 0 .5.5 0 0 1-1 0m.48.42a.32.32 0 0 0 .26-.12.46.46 0 0 0 .12-.3.38.38 0 0 0-.76 0 .39.39 0 0 0 .38.42"
      fill="#fff"
    />
  </svg>
);
export default CardDiscoverIcon;
