import { DANTE_ICON } from '@/constants';
import { fn } from '@storybook/test';

import PoweredByDante from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Global/PoweredByDante',
  component: PoweredByDante,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    // layout: 'fullscreen'
    backgrounds: {
      default: 'white'
    }
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    type: {
      options: ['normal', 'book_meet', 'connect_live_agent'],
      control: { type: 'radio' }
    },
    element: { control: 'color' },
    brand: { control: 'color' },
    surface: { control: 'color' }
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {
    brand: '#8275F7',
    element: '#000000',
    surface: '#fff',
    type: 'normal',
    onClick: fn()
  }
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    children: <>Explain the contents of this chatbot.</>
  }
};
