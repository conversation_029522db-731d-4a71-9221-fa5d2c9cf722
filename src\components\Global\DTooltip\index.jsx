import {
  Popover,
  PopoverButton,
  PopoverPanel,
  Transition,
} from '@headlessui/react';
import { Fragment, useRef, useState } from 'react';

const DTooltip = ({ children, content, position = 'top center' }) => {
  const buttonRef = useRef(null);
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);

  return (
    <Popover className="relative inline-flex items-center">
      {() => (
        <>
          <PopoverButton
            ref={buttonRef}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            {children}
          </PopoverButton>

          <Transition
            show={isHovered}
            as={Fragment}
            enter="transition-opacity duration-300 ease-in-out"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity duration-300 ease-in-out"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <PopoverPanel
              static
              anchor={position}
              style={{ color: '#fff' }}
              className="bg-gray-800 text-white text-xs rounded px-2 py-1 shadow-sm !max-w-[400px] leading-normal z-20 mx-size2"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <span className="leading-normal" dangerouslySetInnerHTML={{ __html: content }} />
            </PopoverPanel>
          </Transition>
        </>
      )}
    </Popover>
  );
};

export default DTooltip;
