import { useState } from 'react';
import DButton from '@/components/Global/DButton';
import useDanteApi from '@/hooks/useDanteApi';
import { getDefaultImages } from '@/services/user.service';
import { getDefaultImages as getDefaultTeamImages } from '@/services/teamManagement.service';
import * as userService from '@/services/user.service';
import './index.css';
import LayoutMain from '@/layouts/LayoutMain';
import { useNavigate } from 'react-router-dom';
import DAddCreditsModal from '@/components/DAddCreditsModal';
import AiAvatarIcon from '@/components/Global/Icons/AiAvatarIcon';
import SubscriptionIcon from '@/components/Global/Icons/SubscriptionIcon';
import LeaveTeamIcon from '@/components/Global/Icons/LeaveTeamIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import DModalProfileForm from '@/components/DModalProfileForm';
import useLogout from '@/application/auth/logout';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import useToast from '@/hooks/useToast';
import DLoading from '@/components/DLoading';

const Profile = () => {
  const { data: defaultImages, isLoading: isLoadingDefaultImages } = useDanteApi(getDefaultImages);
  const { data: defaultTeamImages, isLoading: isLoadingDefaultTeamImages } = useDanteApi(getDefaultTeamImages);
  const { data: user, isLoading: isLoadingUser } = useDanteApi(userService.getUserProfile);
  const navigate = useNavigate();
  const logout = useLogout()
  const { addSuccessToast } = useToast();
  
  const [showUserForm, setShowUserForm] = useState(false);
  const [openAddCreditsModal, setOpenAddCreditsModal] = useState(false);
  const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteAccount = async () => {
    try {
      setIsDeleting(true);
      const response = await userService.deleteUser();
      if (response.status === 200) {
        addSuccessToast({ message: 'Account deleted successfully' });
        logout();
        setOpenConfirmDelete(false);
      }
    } catch (e) {
      console.error('Error deleting account:', e);
    } finally {
      setIsDeleting(false);
    }
  };

  if(isLoadingUser) {
    return <DLoading show={true} />
  };

  return (
    <LayoutMain
      className="bg-white grow p-size5 gap-size5 rounded-size1 flex flex-col h-[1px] md:h-full"
      title="Profile"
    >
      <div className='flex flex-col gap-size5 3xl:max-w-[1200px] 3xl:mx-auto'>
        <div className='flex flex-col gap-size3 p-size3 rounded-size1 border border-grey-5'>
          <div className='flex w-full justify-between items-center'>
            <div className='flex gap-size1 items-center'>
              <AiAvatarIcon />
              <p className='text-xl font-regular'>Account details</p>
            </div>
              <DButton variant='grey' size='sm' onClick={() => setShowUserForm(true)}>
                Edit
              </DButton>
          </div>
          <div className='w-full h-px bg-grey-5'></div>
          <ul className='text-grey-50 text-lg font-regular list-disc list-inside'>
            <li>Edit account details</li>
            <li>Edit your team details</li>
          </ul>
        </div>

        <div className='flex gap-size3 items-center justify-between w-full p-size3 rounded-size1 border border-grey-5'>
          <p><span className='text-xl font-regular'>{Math.round(user?.credits_key?.credits_available - user?.credits_key?.credits_used)}</span> <span className='text-base font-regular text-grey-50'> / credits available</span></p>
          <DButton variant='grey' size='sm' onClick={() => setOpenAddCreditsModal(true)}>
            Add more
          </DButton>
        </div>

        <div className='flex gap-size3 items-center w-full p-size3 rounded-size1 border border-grey-5'>
          <SubscriptionIcon className="text-grey-50"/>
          <a className='text-xl font-regular text-black' href='https://billing.stripe.com/p/login/9AQ3cQ88NaZ2f8k144' target='_blank'>
            Manage subscription
          </a>
        </div>

        <div className='flex gap-size3 items-center w-full p-size3 rounded-size1 border border-grey-5'>
          <LeaveTeamIcon className="text-grey-50 w-7 h-5"/>
          <a className='text-xl font-regular text-black' onClick={logout}>
            Sign out
          </a>
        </div>

        <div className='flex gap-size3 items-center w-full p-size3 rounded-size1 border border-negative-10 bg-negative-2 text-negative-100'>
          <DeleteIcon />
          <p className='text-xl font-regular cursor-pointer' onClick={() => setOpenConfirmDelete(true)}>
            Delete account
          </p>
        </div>
      </div>
      <DModalProfileForm
        open={showUserForm}
        onClose={() => setShowUserForm(false)}
        user={user}
        defaultImages={defaultImages}
        defaultTeamImages={defaultTeamImages}
      />
      <DAddCreditsModal open={openAddCreditsModal} onClose={() => setOpenAddCreditsModal(false)} />
      <DConfirmationModal
        open={openConfirmDelete}
        onClose={() => setOpenConfirmDelete(false)}
        onConfirm={handleDeleteAccount}
        title="Delete account"
        description="Are you sure you want to delete your account? This action is irreversible and will remove all your data from our system."
        confirmText="Delete"
        cancelText="Cancel"
        variantConfirm="danger"
        loading={isDeleting}
      />
    </LayoutMain>
  );
};

export default Profile;