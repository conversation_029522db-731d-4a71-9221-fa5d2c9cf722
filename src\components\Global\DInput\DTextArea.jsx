import React from 'react';

import clsx from 'clsx';

import { Textarea } from '@headlessui/react';
import DInputBlock from './DInputBlock';

const DTextArea = React.forwardRef(
  (
    {
      children,
      label,
      resize = false,
      error,
      type,
      className,
      required,
      minRows = 1,
      autoResize = false,
      value,
      ...props
    },
    ref
  ) => {
    return (
      <DInputBlock label={label} error={error} required={required}>
        <div className="flex flex-col gap-size1 w-full">
          <div
            className={clsx(
              'w-full flex  items-center rounded-size1 border-[1px] focus-within:border-black/20 border-black/5 py-size1 bg-[--dt-color-surface-100] min-h-11 px-size2 gap-size1',
              error && 'border-negative-100',
              autoResize &&
                'grid [&>textarea]:text-inherit after:text-inherit [&>textarea]:resize-none [&>textarea]:overflow-hidden [&>textarea]:[grid-area:1/1/2/2] after:[grid-area:1/1/2/2] after:whitespace-pre-wrap after:invisible after:content-[attr(data-cloned-val)_\'_\'] after:leading-[1.4]'
            )}
            data-cloned-val={value}
          >
            <Textarea
              ref={ref}
              data-testid={`d-textarea-${props.name ?? props.id ?? ''}`}
              rows={autoResize ? 1 : minRows}
              value={value}
              {...props}
              type={type}
              className={clsx(
                'w-full resize-none placeholder-black/10 bg-transparent text-sm data-[focus]:border-black/20',
                'text-min-safe-input',
                resize && 'resize-y',
                autoResize &&
                  `resize-none h-full min-h-[${minRows * 16 * 1.4}px]`
              )}
            />
          </div>
        </div>
      </DInputBlock>
    );
  }
);

DTextArea.displayName = 'DTextArea';

export default DTextArea;
