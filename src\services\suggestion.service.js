import http from './http';

/**
 * Sends a POST request to submit suggestions.
 *
 * @param {Object} options - The options for the request.
 * @param {Object} options.payload - The payload data to be sent in the request.
 * @param {string} [options.kb] - Optional knowledge base ID.
 * @param {string} options.mode - The mode to be used for suggestions.
 * @param {string} [options.integration] - Optional integration type.
 * @returns {Promise} - A promise that resolves when the HTTP request completes.
 */
export const postSuggestions = ({ payload, kb_id, conversation_id }) => {
  const params = {
      kb_id,
      conversation_id,
  };

  return http.post(
    `${import.meta.env.VITE_APP_BASE_API}suggestion/v3`, // API base URL
    payload, // Request payload
    {
      params,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};
export const postSuggestionsV2 = ({ payload, kb_id, conversation_id }) => {
  const params = {
      kb_id,
      conversation_id,
  };

  return http.post(
    `${import.meta.env.VITE_APP_BASE_API}suggestion/v2`, // API base URL
    payload, // Request payload
    {
      params,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};
