import DAlert from '@/components/Global/DAlert';
import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DModal from '@/components/Global/DModal';
import DMultiselect from '@/components/Global/DMultiselect';
import DSelect from '@/components/Global/DSelect';
import AddIcon from '@/components/Global/Icons/AddIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import { ListboxOption } from '@headlessui/react';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import DButtonIcon from '@/components/Global/DButtonIcon';
import EditIcon from '@/components/Global/Icons/EditIcon';

const EditMember = ({
  open,
  onClose,
  member,
  setMember,
  roles,
  onSave,
  setOpenAddRole,
  deleteTeamMember,
  setDeleteMember,
  handleResendInvitation,
  loading,
  setOpenEditRole,
  setOpenDeleteRole,
  setSelectedRole,
}) => {
  const chatbots = useChatbotStore((state) => state.chatbots);
  return (
    <DModal title="Edit Member" isOpen={open} onClose={onClose}>
      <div className="flex flex-col gap-size5">
        {!member?.accepted && (
          <DAlert state="pending">
            <div className="flex flex-col gap-size0">
              <p className="text-xs tracking-tight">Pending confirmation</p>
              <p className="text-xs tracking-tight text-grey-50">
                Your <NAME_EMAIL> is still not accepted -
                do you wish to
                <a
                  className="text-negative-50 underline cursor-pointer"
                  onClick={() => handleResendInvitation(member.id)}
                >
                  {' '}
                  resend invitation?
                </a>
              </p>
            </div>
          </DAlert>
        )}
        <div className="flex gap-size4">
          <img
            src={member?.user_data?.profile_image}
            alt={member?.user_data?.first_name}
            className="size-10"
          />
          <div className="flex flex-col gap-size1 items-start">
            <p className="text-sm font-regular">
              {member?.user_data?.first_name} {member?.user_data?.last_name}
            </p>
            <p className="text-sm font-regular">{member?.user_data?.email}</p>
          </div>
        </div>
        <div className="flex flex-col gap-size1 items-start">
          <p className="text-base font-medium">AI Chatbots</p>
          <DMultiselect
            options={chatbots?.map((kb) => ({
              label: kb.name,
              value: kb.kb_id,
            }))}
            selected={member?.allowed_kbs?.map((kb) => ({
              label: kb.knowledge_base_name,
              value: kb.id,
            }))}
            setSelected={(value) => {
              setMember({
                ...member,
                allowed_kbs: value.map((kb) => ({
                  knowledge_base_name: kb.label,
                  id: kb.value,
                })),
              });
            }}
          />
        </div>
        <div className="flex flex-col gap-size1 items-start">
          <div className="flex flex-col gap-size0 items-start">
            <p className="text-base font-medium">Credits</p>
            <span className="text-xs text-start font-light tracking-tight text-grey-50">
              Define maximum amount of credits member is allowed to spend in a
              month. Note credits will reset each month.
            </span>
          </div>
          <DInput
            value={member?.credits_available?.toString() || ''}
            type="number"
            min={0}
            onChange={(e) => {
              const value = Math.max(0, Number(e.target.value) || 0);
              setMember({ ...member, credits_available: value });
            }}
            placeholder="Enter credits"
          />
        </div>
        <div className="flex flex-col gap-size1 items-start">
          <p className="text-base font-medium">Role</p>
          <DSelect
            value={member?.team_role_id}
            onChange={(value) => {
              setMember({ ...member, team_role_id: value });
            }}
            selectedChild={
              <p>
                {roles?.find((role) => role.id === member?.team_role_id)?.name}
              </p>
            }
          >
            {roles?.map((role, index) => (
              <ListboxOption
                key={index}
                value={role.id}
                className={`cursor-pointer hover:bg-grey-5 rounded-size0 py-size1 px-size0 ${
                  role.id === member?.team_role_id ? 'bg-grey-5' : ''
                }`}
              >
                <div className='flex items-center gap-size1 justify-between'>
                  <p className="text-sm font-regular">{role.name}</p>
                  {role.team_id && <div className='flex items-center gap-size1'>
                      <DButtonIcon variant="outlined" size="sm" onClick={(e) => {
                        e.stopPropagation();
                        setOpenEditRole(true);
                        setSelectedRole(role);
                      }}>
                        <EditIcon />
                      </DButtonIcon>
                      <DButtonIcon variant="outlined" size="sm" onClick={(e) => {
                        e.stopPropagation();
                        setOpenDeleteRole(true);
                        setSelectedRole(role);
                      }}>
                        <DeleteIcon />
                      </DButtonIcon>
                    </div>}
                </div>
                <div className="flex gap-size0 flex-wrap pt-size1">
                  {role.permissions
                    .filter((permission) => permission.value)
                    .map((permission, index) => (
                      <span
                        className="text-xs font-light tracking-tight leading-3"
                        key={index}
                      >
                        {permission.label}
                        {index <
                        role.permissions.filter((p) => p.value).length - 1
                          ? ', '
                          : ''}
                      </span>
                    ))}
                </div>
              </ListboxOption>
            ))}
            <ListboxOption
              className="cursor-pointer border-t border-grey-5 flex items-center gap-size1 py-size2 hover:bg-grey-5"
              key={'add-role'}
              onClick={(e) => {
                e.preventDefault();
                setOpenAddRole(true);
                // onClose();
              }}
            >
              <AddIcon />
              <p>Add new role</p>
            </ListboxOption>
          </DSelect>
        </div>
        <DButton
          size="sm"
          fullWidth
          className="bg-negative-5 text-negative-100 !justify-start text-xs"
          onClick={() => {
            setDeleteMember(member.user_data.id);
            deleteTeamMember();
          }}
        >
          <DeleteIcon className="size-5" />
          Remove from team
        </DButton>
        <DButton
          size="sm"
          fullWidth
          variant="dark"
          onClick={onSave}
          loading={loading}
        >
          Save Changes
        </DButton>
      </div>
    </DModal>
  );
};

export default EditMember;
