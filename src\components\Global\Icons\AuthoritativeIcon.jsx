const AuthoritativeIcon = (props) => {
  return (
    <svg
      width="17"
      height="20"
      viewBox="0 0 17 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M3.98873 5.05609L4.00028 4.94221C4.06673 4.28702 4.61838 3.78849 5.27694 3.78849H5.46156C6.0683 3.78849 6.56016 4.28034 6.56016 4.88708V4.88708M12.8602 7.08426H12.3887C11.1579 7.08426 10.1602 6.10055 10.1602 4.88708M12.8602 7.08426C14.3513 7.08426 15.5602 5.89246 15.5602 4.42229V4.04201C15.5602 2.36182 14.1786 0.999756 12.4744 0.999756H8.36016H4.20241C2.52222 0.999756 1.16016 2.36182 1.16016 4.04201V4.38426C1.16016 5.87543 2.36899 7.08426 3.86016 7.08426V7.08426M12.8602 7.08426V15.704M12.7316 5.05609L12.7185 4.92697C12.6529 4.28067 12.1013 3.78849 11.4425 3.78849H11.2744C10.659 3.78849 10.1602 4.28034 10.1602 4.88708M10.1602 4.88708H6.56016M3.86016 7.08426H4.36297C5.57644 7.08426 6.56016 6.10055 6.56016 4.88708V4.88708M3.86016 7.08426V15.704M3.86016 15.704H12.8602M3.86016 15.704V15.704C3.07907 15.704 2.44587 16.3372 2.44587 17.1183V18.6617C2.44587 18.8484 2.59721 18.9998 2.7839 18.9998H13.9364C14.1231 18.9998 14.2744 18.8484 14.2744 18.6617V17.1183C14.2744 16.3372 13.6412 15.704 12.8602 15.704V15.704M5.96016 15.6998V11.7998M7.76016 15.6998V13.5998M9.56016 15.6998V14.1998"
        stroke="currentColor"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default AuthoritativeIcon;
