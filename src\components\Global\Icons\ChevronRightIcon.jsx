const ChevronRightIcon = (props) => {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.22882 12.4778L10.5458 8.40073C10.5996 8.34869 10.643 8.28585 10.6723 8.21694C10.7016 8.1478 10.7168 8.07344 10.7168 7.99831C10.7168 7.92318 10.7016 7.84882 10.6723 7.77967C10.643 7.71076 10.6001 7.64844 10.5463 7.59641L6.23341 3.52314L6.23235 3.52211C6.12559 3.4194 5.98241 3.36327 5.83429 3.36607C5.68622 3.36886 5.54531 3.43031 5.44251 3.53692C5.33979 3.64368 5.28366 3.78685 5.28646 3.93497C5.28925 4.08281 5.3505 4.2235 5.4568 4.32626L9.35451 7.99831L5.45611 11.671L5.45387 11.6731C5.39847 11.7233 5.35372 11.7842 5.32227 11.8521C5.29082 11.9199 5.27331 11.9934 5.27079 12.0682C5.26826 12.1429 5.28077 12.2174 5.30756 12.2873C5.33436 12.3571 5.3749 12.4209 5.42679 12.4747C5.47867 12.5286 5.54084 12.5715 5.60962 12.6009C5.6784 12.6304 5.75238 12.6457 5.82718 12.646C5.90198 12.6463 5.97607 12.6315 6.04508 12.6027C6.11409 12.5738 6.17651 12.5313 6.22882 12.4778Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default ChevronRightIcon;
