/**
 * Utility functions for Voice WebSocket connections
 */

/**
 * WebSocket endpoint types
 * @enum {string}
 */
export const WS_ENDPOINT_TYPES = {
  DEMO: 'demo',
  PREVIEW: 'preview',
  BROWSER: 'browser', // Legacy endpoint, kept for backward compatibility
  SHARED: 'shared'
};

/**
 * Get the appropriate WebSocket URL based on the endpoint type
 * @param {string} endpointType - The type of endpoint to use (from WS_ENDPOINT_TYPES)
 * @returns {string} The complete WebSocket URL
 */
export const getVoiceWebSocketUrl = (endpointType = WS_ENDPOINT_TYPES.BROWSER) => {
  const baseUrl = import.meta.env.VITE_APP_AI_VOICE_WEBSOCKET;

  switch (endpointType) {
    case WS_ENDPOINT_TYPES.DEMO:
      return `${baseUrl}/browser/demo/ws`;
    case WS_ENDPOINT_TYPES.PREVIEW:
      return `${baseUrl}/browser/preview/ws`;
    case WS_ENDPOINT_TYPES.BROWSER:
      return `${baseUrl}/browser/ws`;
    case WS_ENDPOINT_TYPES.SHARED:
      return `${baseUrl}/browser/ws/shared`;
    default:
      return `${baseUrl}/browser/ws`;
  }
};
