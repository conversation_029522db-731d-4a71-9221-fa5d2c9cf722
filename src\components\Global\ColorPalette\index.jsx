import React from 'react';

const ColorPalette = () => {
  const colorGroups = {
    'Grey': {
      'grey-1': 'rgba(var(--color-grey-opacity), 0.01)',
      'grey-2': 'rgba(var(--color-grey-opacity), 0.02)',
      'grey-5': 'rgba(var(--color-grey-opacity), 0.05)',
      'grey-10': 'rgba(var(--color-grey-opacity), 0.1)',
      'grey-20': 'rgba(var(--color-grey-opacity), 0.2)',
      'grey-30': 'rgba(var(--color-grey-opacity), 0.3)',
      'grey-50': 'rgba(var(--color-grey-opacity), 0.5)',
      'grey-75': 'rgba(var(--color-grey-opacity), 0.75)',
      'grey-100': 'rgba(var(--color-grey-100), 1)',
      'grey-300': 'rgba(var(--color-grey-opacity), 1)',
      'grey-400': 'rgba(var(--color-black), 1)'
    },
    'Purple': {
      'purple-5': 'rgba(var(--color-purple-opacity), 0.05)',
      'purple-10': 'rgba(var(--color-purple-opacity), 0.1)',
      'purple-20': 'rgba(var(--color-purple-opacity), 0.2)',
      'purple-50': 'rgba(var(--color-purple-opacity), 0.5)',
      'purple-100': 'rgba(var(--color-purple-opacity), 0.1)',
      'purple-200': 'rgba(var(--color-purple-200), 1)',
      'purple-300': 'rgba(var(--color-purple-300), 1)'
    },
    'Green': {
      'green-5': 'rgba(var(--color-green-5), 0.05)',
      'green-10': 'rgba(var(--color-green-5), 0.1)',
      'green-20': 'rgba(var(--color-green-5), 0.2)',
      'green-100': 'rgba(var(--color-green-100), 1)',
      'green-200': 'rgba(var(--color-green-200), 1)',
      'green-300': 'rgba(var(--color-green-300), 1)',
      'green-400': 'rgba(var(--color-green-400), 1)',
      'green-500': 'rgba(var(--color-green-500), 1)'
    },
    'Dark Green': {
      'darkGreen-10': 'rgba(var(--color-dark-green-300), 0.1)',
      'darkGreen-300': 'rgba(var(--color-dark-green-300), 1)'
    },
    'Negative': {
      'negative-2': 'rgba(var(--color-negative-100), 0.02)',
      'negative-5': 'rgba(var(--color-negative-100), 0.05)',
      'negative-10': 'rgba(var(--color-negative-100), 0.1)',
      'negative-20': 'rgba(var(--color-negative-100), 0.2)',
      'negative-100': 'rgba(var(--color-negative-100), 1)',
      'negative-200': 'rgba(var(--color-negative-200), 1)'
    },
    'Orange': {
      'orange-5': 'rgba(var(--color-orange-5), 0.05)',
      'orange-10': 'rgba(var(--color-orange-5), 0.1)',
      'orange-20': 'rgba(var(--color-orange-5), 0.2)',
      'orange-100': 'rgba(var(--color-orange-300), 1)',
      'orange-200': 'rgba(var(--color-orange-200), 1)',
      'orange-300': 'rgba(var(--color-orange-300), 1)'
    },
    'Yellow': {
      'yellow-300': 'rgba(var(--color-yellow-300), 1)'
    },
    'Black & White': {
      'black': 'rgba(var(--color-black), var(--color-black-opacity))',
      'white': 'rgba(var(--color-white), 1)'
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-8">Tailwind Color Palette</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {Object.entries(colorGroups).map(([groupName, colors]) => (
          <div key={groupName} className="border rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-4">{groupName}</h2>
            <div className="space-y-4">
              {Object.entries(colors).map(([colorName, colorValue]) => (
                <div key={colorName} className="flex items-center gap-4">
                  <div 
                    className="w-16 h-16 rounded-lg border border-grey-10"
                    style={{ backgroundColor: colorValue }}
                  ></div>
                  <div>
                    <p className="font-medium">{colorName}</p>
                    <p className="text-sm text-grey-50">bg-{colorName}</p>
                    <p className="text-xs text-grey-50">{colorValue}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ColorPalette; 