name: App Platform Preview

on:
  pull_request:
    branches: [staging]

permissions:
  contents: read
  pull-requests: write

jobs:
  test:
    name: preview
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set branch dynamically
        run: |
          BRANCH_NAME=${{ github.head_ref }}
          echo "Setting branch to $BRANCH_NAME in .do/app.yaml"
          sed -i "s|branch: staging|branch: $BRANCH_NAME|" .do/app.yaml

      - name: Deploy the app
        id: deploy
        uses: digitalocean/app_action/deploy@v2
        with:
          deploy_pr_preview: "true"
          token: ${{ secrets.DO_API_TOKEN }}

      - name: Save logs to files
        run: |
          echo "${{ steps.deploy.outputs.build_logs }}" > build_logs.txt
          echo "${{ steps.deploy.outputs.deploy_logs }}" > deploy_logs.txt

      - name: Upload Build Logs (First deployment)
        uses: actions/upload-artifact@v4
        with:
          name: build_logs
          path: build_logs.txt

      - name: Upload Deploy Logs (First deployment)
        uses: actions/upload-artifact@v4
        with:
          name: deploy_logs
          path: deploy_logs.txt

      # === Update the Spec File with the Captured Live URL ===
      - name: Update live URL in spec file
        run: |
          LIVE_URL="${{ fromJson(steps.deploy.outputs.app).live_url }}/"
          echo "Updating VITE_APP_BASE_URL to ${LIVE_URL} in .do/app.yaml"
          # Find the line with "- key: VITE_APP_BASE_URL", then update the following "value:" line with the LIVE_URL.
          sed -i '/- key: VITE_APP_BASE_URL/ {n;s|value:.*|value: '"${LIVE_URL}"'|;}' .do/app.yaml

      # === Second Deployment Phase: Redeploy with Updated Spec ===
      - name: Final Deployment (with updated live URL)
        id: deploy_final
        uses: digitalocean/app_action/deploy@v2
        with:
          deploy_pr_preview: "true"
          token: ${{ secrets.DO_API_TOKEN }}

      # Save final deployment logs
      - name: Save final deployment logs
        run: |
          echo "${{ steps.deploy_final.outputs.build_logs }}" > final_build_logs.txt
          echo "${{ steps.deploy_final.outputs.deploy_logs }}" > final_deploy_logs.txt

      - name: Upload Final Build Logs
        uses: actions/upload-artifact@v4
        with:
          name: final_build_logs
          path: final_build_logs.txt

      - name: Upload Final Deploy Logs
        uses: actions/upload-artifact@v4
        with:
          name: final_deploy_logs
          path: final_deploy_logs.txt

      - uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 The app was successfully deployed at ${{ fromJson(steps.deploy.outputs.app).live_url }}.`
            })

      - uses: actions/github-script@v7
        if: failure()
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `The app failed to be deployed. Logs have been uploaded as artifacts for debugging. [Download Logs](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}#artifacts).
              `
            })
