import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import * as userService from '@/services/user.service';

const EnterName = ({
  name,
  setName,
  loading,
  setLoading,
  error,
  setError
}) => {
  const navigate = useNavigate();
  const [nameError, setNameError] = useState('');

  const validateName = () => {
    if (!name || name.trim() === '') {
      setNameError('Please enter your name');
      return false;
    }

    if (name.length < 2) {
      setNameError('Name must be at least 2 characters');
      return false;
    }

    setNameError('');
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateName()) return;

    setLoading(true);
    setError('');

    try {
      // Update the user profile with the name
      const response = await userService.updateProfile({
        full_name: name
      });

      if (response.status === 200) {
        // Navigate to the dashboard or home page after successful name entry
        navigate('/');
      }
    } catch (err) {
      console.error(err);
      setError(err.response?.data?.detail || 'Error saving your name');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-t-xl md:rounded-t-3xl shadow-lg p-4 md:p-7 mx-2 md:mx-0">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-2xl font-medium mb-2">Welcome to DANTE!</h1>
        <h2 className="text-xl font-medium text-gray-700">What's your name?</h2>
      </div>

      {/* Name Input Form */}
      <form onSubmit={handleSubmit} className="mb-3">
        <div className="mb-6">
          <input
            type="text"
            placeholder="Enter your name..."
            value={name}
            onChange={(e) => setName(e.target.value)}
            className={`w-full py-3 px-4 border ${
              nameError ? 'border-red-500' : 'border-gray-200'
            } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-gray-700 text-min-safe-input`}
            disabled={loading}
          />
          <div className="h-5 mt-1">
            {nameError && <p className="text-red-500 text-xs">{nameError}</p>}
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center hover:bg-indigo-700 transition"
          disabled={loading}
        >
          {loading ? (
            <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : null}
          Continue
        </button>

        <div className="h-6 mt-3">
          {error && <p className="text-red-500 text-sm text-center animate-fadeIn">{error}</p>}
        </div>
      </form>
    </div>
  );
};

export default EnterName;
