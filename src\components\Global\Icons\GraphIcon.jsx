import * as React from 'react';
const GraphIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.458 3.5a.5.5 0 0 1 .5-.5h14.084a.5.5 0 1 1 0 1h-.584v7.625a2.125 2.125 0 0 1-2.125 2.125h-.931l.864 2.592a.5.5 0 0 1-.949.316l-.247-.741H6.93l-.247.741a.5.5 0 1 1-.949-.316l.864-2.592h-.931a2.125 2.125 0 0 1-2.125-2.125V4h-.584a.5.5 0 0 1-.5-.5m2.084.5v7.625a1.125 1.125 0 0 0 1.125 1.125h8.666a1.125 1.125 0 0 0 1.125-1.125V4zm3.11 9.75-.389 1.167h5.474l-.39-1.167zm4.515-8.042a.5.5 0 0 1 .5.5v4.334a.5.5 0 1 1-1 0V6.208a.5.5 0 0 1 .5-.5M10 7.333a.5.5 0 0 1 .5.5v2.709a.5.5 0 0 1-1 0V7.833a.5.5 0 0 1 .5-.5M7.833 8.958a.5.5 0 0 1 .5.5v1.084a.5.5 0 0 1-1 0V9.458a.5.5 0 0 1 .5-.5"
      fill="currentColor"
    />
  </svg>
);
export default GraphIcon;
