import { fn } from '@storybook/test';

import DAvatarBlock from './index';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'DAvatar/DAvatarBlock',
  component: DAvatarBlock,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    image: {
      control: { type: 'text' }
    },
    isDefault: {
      default: false,
      control: { type: 'boolean' }
    },
    name: {
      control: { type: 'text' }
    }
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {
    isDefault: false,
    image:
      'https://dante-chatbot-pictures.s3.amazonaws.com/764a6813-da65-45d3-a0e4-cc8ec47c0307/avatar-764a6813-da65-45d3-a0e4-cc8ec47c0307-1722278074914.png',
    name: 'Copy of James',
    avatarId: 'asd-123'
  }
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    isDefault: false,
    image:
      'https://dante-chatbot-pictures.s3.amazonaws.com/764a6813-da65-45d3-a0e4-cc8ec47c0307/avatar-764a6813-da65-45d3-a0e4-cc8ec47c0307-1722278074914.png',
    name: 'James',
    avatarId: 'asd-123'
  }
};
export const CopyAvatar = {
  args: {
    isDefault: false,
    image:
      'https://dante-chatbot-pictures.s3.amazonaws.com/764a6813-da65-45d3-a0e4-cc8ec47c0307/avatar-764a6813-da65-45d3-a0e4-cc8ec47c0307-1722278074914.png',
    name: 'Copy of James',
    avatarId: 'asd-123'
  }
};
export const DefaultAvatar = {
  args: {
    isDefault: true,
    image:
      'https://dante-chatbot-pictures.s3.amazonaws.com/764a6813-da65-45d3-a0e4-cc8ec47c0307/avatar-764a6813-da65-45d3-a0e4-cc8ec47c0307-1722278074914.png',
    name: 'James',
    avatarId: 'asd-123'
  }
};
