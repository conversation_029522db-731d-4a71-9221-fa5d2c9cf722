import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { useUserStore } from '@/stores/user/userStore';
import { HumanHandoverWebSocketService } from '@/services/websocket';

/**
 * Hook for managing WebSocket connections for human handover conversations in the dashboard
 *
 * @param {Object} config - Configuration for the WebSocket manager
 * @param {Array} config.openedConversations - Array of opened conversations
 * @param {Function} config.onMessageReceived - Callback for when a message is received
 * @param {Function} config.onAgentJoined - Callback for when an agent joins
 * @param {Function} config.onAgentResolved - Callback for when an agent resolves the conversation
 * @returns {Object} WebSocket manager state and methods
 */
const useHumanHandoverWebSocketManager = (config) => {
  const { openedConversations = [] } = config;
  const auth = useUserStore((state) => state.auth);
  const wsServices = useRef({});
  const [connections, setConnections] = useState({});

  // Initialize or update WebSocket connections based on opened conversations
  useEffect(() => {
    // Track which conversations are currently open
    const openConversationIds = new Set();

    // Process each open conversation
    openedConversations.forEach(conversation => {
      if (conversation.open) {
        const conversationId = conversation.id;
        openConversationIds.add(conversationId);

        // If we don't have a connection for this conversation yet, create one
        if (!wsServices.current[conversationId]) {

          // Create WebSocket URL
          const wsUrl = `${import.meta.env.VITE_APP_BASE_WEBSOCKET}/human-handover/conversations/${conversationId}/chat`;

          // Create WebSocket service
          const wsService = new HumanHandoverWebSocketService(
            {
              url: wsUrl,
              authToken: auth?.access_token,
              isShared: false, // We're in the app
              isInHumanHandoverApp: true
            },
            {
              onOpen: () => {
                setConnections(prev => ({
                  ...prev,
                  [conversationId]: {
                    ...prev[conversationId],
                    isConnected: true,
                    isConnecting: false,
                    error: null
                  }
                }));
              },
              onClose: () => {
                xwsetConnections(prev => ({
                  ...prev,
                  [conversationId]: {
                    ...prev[conversationId],
                    isConnected: false,
                    isConnecting: false
                  }
                }));
              },
              onError: (event) => {
                setConnections(prev => ({
                  ...prev,
                  [conversationId]: {
                    ...prev[conversationId],
                    error: event.error || new Error('WebSocket error'),
                    isConnecting: false
                  }
                }));
              },
              onMessageReceived: (data) => {
                if (config.onMessageReceived) {
                  config.onMessageReceived(data, conversationId);
                }
              },
              onAgentJoined: (data) => {
                if (config.onAgentJoined) {
                  config.onAgentJoined(data, conversationId);
                }
              },
              onAgentResolved: (data) => {
                if (config.onAgentResolved) {
                  config.onAgentResolved(data, conversationId);
                }
              }
            }
          );

          // Store the WebSocket service
          wsServices.current[conversationId] = wsService;

          // Initialize connection state
          setConnections(prev => ({
            ...prev,
            [conversationId]: {
              isConnected: false,
              isConnecting: true,
              error: null
            }
          }));

          // Connect to the WebSocket server
          wsService.connect().catch(error => {
            setConnections(prev => ({
              ...prev,
              [conversationId]: {
                ...prev[conversationId],
                error,
                isConnecting: false
              }
            }));
          });
        }
      }
    });

    // Cleanup function to disconnect WebSockets for conversations that are no longer open
    return () => {
      Object.keys(wsServices.current).forEach(conversationId => {
        if (!openConversationIds.has(conversationId)) {
          wsServices.current[conversationId].disconnect();
          delete wsServices.current[conversationId];
          setConnections(prev => {
            const newConnections = { ...prev };
            delete newConnections[conversationId];
            return newConnections;
          });
        }
      });
    };
  }, [openedConversations, auth?.access_token]);

  // Function to send a message to a specific conversation
  const sendMessageToConversation = useCallback((conversationId, text, isQuickResponse = false, images = []) => {
    if (wsServices.current[conversationId]) {
      const userData = useUserStore.getState().user;
      const additionalData = {};

      // Add agent info
      if (userData) {
        additionalData.agent_name = userData.full_name;
        additionalData.agent_profile_pic = userData.profile_image;
      }

      return wsServices.current[conversationId].sendUserMessage(text, isQuickResponse, images, additionalData);
    }
    return false;
  }, []);

  // Function to send a control message to a specific conversation
  const sendControlMessageToConversation = useCallback((conversationId, type) => {
    if (wsServices.current[conversationId]) {
      return wsServices.current[conversationId].sendControlMessage(type, conversationId);
    }
    return false;
  }, []);

  // Function to disconnect a specific conversation
  const disconnectConversation = useCallback((conversationId) => {
    if (wsServices.current[conversationId]) {
      wsServices.current[conversationId].disconnect();
      delete wsServices.current[conversationId];
      setConnections(prev => {
        const newConnections = { ...prev };
        delete newConnections[conversationId];
        return newConnections;
      });
    }
  }, []);

  // Create a hook-like interface for each conversation
  const webSocketHooks = useMemo(() => {
    const hooks = {};

    openedConversations.forEach(conversation => {
      if (conversation.open) {
        const conversationId = conversation.id;
        const connectionState = connections[conversationId] || {
          isConnected: false,
          isConnecting: false,
          error: null
        };

        hooks[conversationId] = {
          connect: () => {
            if (wsServices.current[conversationId]) {
              return wsServices.current[conversationId].connect();
            }
            return Promise.reject(new Error('WebSocket service not initialized'));
          },
          disconnect: () => disconnectConversation(conversationId),
          sendMessage: (text, isQuickResponse, images) =>
            sendMessageToConversation(conversationId, text, isQuickResponse, images),
          sendControlMessage: (type) =>
            sendControlMessageToConversation(conversationId, type),
          isConnected: connectionState.isConnected,
          isConnecting: connectionState.isConnecting,
          error: connectionState.error
        };
      }
    });

    return hooks;
  }, [openedConversations, connections, disconnectConversation, sendMessageToConversation, sendControlMessageToConversation]);

  // Clean up all connections when component unmounts
  useEffect(() => {
    return () => {
      Object.keys(wsServices.current).forEach(conversationId => {
        wsServices.current[conversationId].disconnect();
      });
      wsServices.current = {};
    };
  }, []);

  return {
    webSocketHooks,
    sendMessageToConversation,
    sendControlMessageToConversation,
    disconnectConversation
  };
};

export default useHumanHandoverWebSocketManager;
