import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { DANTE_THEME_CHAT } from '@/constants';

const initialState = {
  chatbotCustomization: { ...DANTE_THEME_CHAT },
  avatarCustomization: {},
  tabsCustomization: {
    home: {}
  },
  changedFields: new Set(),
};

export const useCustomizationStore = create(
  // persist(
  (set) => ({
    ...initialState,
    setChatbotCustomization: (customization) => set({ chatbotCustomization: customization }),
    updateChatbotCustomization: (key, value) =>
      set((state) => {
        state.changedFields.add(key);
        return {
          chatbotCustomization: {
            ...state.chatbotCustomization,
            [key]: value
          }
        };
      }),
    setAvatarCustomization: (customization) => set({ avatarCustomization: customization }),
    setTabsCustomization: (customization) => set({ tabsCustomization: customization }),
    updateTabsCustomization: (key, value) =>
      set((state) => {
        return {
          tabsCustomization: {
            ...state.tabsCustomization,
            [key]: value
          }
        };
      }),
    reset: () => set({ ...initialState })
  }),
  {
    name: 'customization-store',
    storage: createJSONStorage(() => localStorage)
  }
  // )
);
