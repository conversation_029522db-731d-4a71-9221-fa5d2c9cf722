import { LLM_MODEL_DEFAULT } from '@/constants';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const initialState = {
  user: {
    avatar: '',
    email: '',
    full_name: '',
    id: '',
    monthly_message_limit: 0,
    monthly_messages_used: 0,
    tier_id: '',
    tier_name: '',
    tier_note: '',
    tier_on_trial: false,
    tier_price_as_float: 0,
    tier_type: '',
    trial_used: false,
    show_fifty_percent_discount: true,
    permissions: [],
    credits_key: {
      credits_available: 0,
      credits_used: 0,
      percentage: 0,
      ai_voice_minutes_available: 0,
      ai_voice_minutes_used: 0
    }
  },
  auth: {
    access_token: undefined,
    user_id: undefined,
    first_name: undefined,
    last_name: undefined,
    date_created: undefined,
    login_count: 0
  },
  tier: {
    allowed_model: LLM_MODEL_DEFAULT.value,
    save_conversations: false,
    edit_knowledge_bases: false,
    upload_images: false,
    upload_multiple_files: true,
    upload_videos: false,
    voice_to_text: false,
    remove_watermark: false,
    number_of_knowledge_bases: 0
  },
}

const userStoreSlice = (set) => ({
  ...initialState,
  saveTiersDetail: (tier) => set({ tier }),
  saveAuthDetail: (auth) => set({ auth }),
  setUser: (user) => set({ user }),
  updateAccessToken: (access_token) => set((state) => ({ auth: { ...state.auth, access_token } })),
  setCredits: (creditsData) => set((state) => ({ 
    user: { 
      ...state.user, 
      credits_key: {
        ...state.user.credits_key,
        ...creditsData
      } 
    }
  })),
  removeTiersDetail: () =>
    set({
      tier: {
        allowed_model: LLM_MODEL_DEFAULT.value,
        save_conversations: false,
        edit_knowledge_bases: false,
        upload_images: false,
        upload_multiple_files: true,
        upload_videos: false,
        voice_to_text: false,
        remove_watermark: false,
        number_of_knowledge_bases: 0
      }
    }),
  removeAuthDetail: () =>
    set({
      auth: {
        access_token: undefined,
        user_id: undefined,
        first_name: undefined,
        last_name: undefined,
        date_created: undefined,
        login_count: 0
      }
    }),
  reset: () => set({ ...initialState })
});

const persistedUserStore = persist(userStoreSlice, {
  name: 'user'
});

export const useUserStore = create(persistedUserStore);
