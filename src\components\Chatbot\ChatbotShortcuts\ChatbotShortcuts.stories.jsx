import { dataChart } from '@/helpers/stories/generateDateChart';
import { fn } from '@storybook/test';

import ChatbotShortcuts from '.';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Chatbot/ChatbotShortcuts',
  component: ChatbotShortcuts,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'fullscreen'
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    status: {
      options: ['Active', 'Paused'],
      control: { type: 'radio' }
    }
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {
    status: 'Paused'
  },
  decorators: [
    (Story) => (
      <div className="h-screen">
        <Story />
      </div>
    )
  ]
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default = {
  args: {
    chatbotName: 'Chatbot name',
    stats: dataChart,
    flaggedMessages: 2
  }
};

export const SmallScreen = {
  parameters: {
    viewport: {
      defaultViewport: 'iphone14promax'
    }
  },
  args: Default.args
};
