import { DEFAULT_HEADERS } from './constants.service';
import http from './http';
import generateApiEndpoint from '@/helpers/generateApiEndpoint';

export const getUsersAdmin = ({email__ilike, limit, offset, order_by, ascending}) => {
  return http.get(generateApiEndpoint('user_migration_rules'), {
    headers: DEFAULT_HEADERS,
    params: {
      email__ilike,
      limit,
      offset,
      order_by,
      ascending
    }
  });
};


export const updateUserMigrationRule = ({id, old_design, new_design, migration_wave}) => {
  return http.patch(generateApiEndpoint(`user_migration_rules/${id}`), {
    old_design, 
    new_design, 
    migration_wave
  }, {
    headers: DEFAULT_HEADERS,
  });
};
