import { v4 as uuidv4 } from 'uuid';

/**
 * Transforms a message object into an array of bubble messages for display.
 *
 * @param {Object} message - The original message object.
 * @param {string} message.question - The user's question.
 * @param {string} message.answer - The assistant's answer.
 * @param {string} message.date_created - The date the message was created.
 *
 * @returns {Array} An array of formatted message objects for the chat bubble.
 */
const transformMessageToBubbleMessage = (message) => {
  // Check if answer contains specific phrases that should be displayed as info messages
  const isInfoMessage = message.answer && (
    message.answer.includes('has requested to speak with a live agent') ||
    message.answer.includes('has joined the conversation and is ready to assist you') ||
    message.answer.includes('Conversation has been marked as resolved by')
  );

  const messages = [];

  if (message.question) {
    messages.push({
      id: uuidv4(),
      role: 'user',
      type: 'normal',
      content: message.question,
      date_created: message.date_created,
      status: 'complete',
      was_answered: message.was_answered,
      images: message?.attachments
    });
  }

  if (message.answer) {
    messages.push({
      id: message.id,
      role: 'assistant',
      type: isInfoMessage ? 'info' : 'normal',
      content: message.answer,
      date_created: message.date_created,
      reaction: message.reaction,
      status: 'complete',
      was_answered: message.was_answered,
      images: message?.attachments
    });
  }

  return [
    ...messages,
  ];
};

export default transformMessageToBubbleMessage;
