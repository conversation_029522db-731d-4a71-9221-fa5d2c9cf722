import * as React from 'react';
const ColorIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.597 6.453 9.5 4.55l4.95 4.95-4.95 4.95L4.55 9.5l2.34-2.34 1.467 1.467a1.594 1.594 0 1 0 .707-.707zm-.707-.707L9.146 3.49a.5.5 0 0 1 .708 0l5.656 5.656a.5.5 0 0 1 0 .708l-5.303 5.303a1 1 0 0 1-1.414 0l-4.95-4.95a1 1 0 0 1 0-1.414l2.34-2.34-1.35-1.35a.5.5 0 1 1 .708-.707zm2.463 3.187.018-.018a.594.594 0 1 1-.018.018m6.116 3.255.312-.39zm-.313-.39-.002.001-.005.004-.013.01-.043.037a4.4 4.4 0 0 0-.568.606c-.3.392-.65.992-.65 1.7a1.594 1.594 0 1 0 3.188 0c0-.708-.35-1.308-.651-1.7a4.4 4.4 0 0 0-.61-.643l-.014-.01-.004-.004-.002-.001m0 0a.5.5 0 0 0-.626 0zm-.626 0 .313.39zm0 0 .313.39zm.313 1.087q.074.083.15.182c.246.319.444.704.444 1.09a.594.594 0 0 1-1.188 0c0-.386.197-.771.443-1.09q.077-.1.15-.182"
      fill="currentColor"
    />
  </svg>
);
export default ColorIcon;
