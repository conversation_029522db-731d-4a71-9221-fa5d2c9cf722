/* Box2FA Input styles */
.box-2fa-input {
  font-family: monospace !important;
  font-weight: bold !important;
}

/* Light mode styles */
.box-2fa-input {
  color: black !important;
  caret-color: black !important;
}

/* Dark mode styles - extremely specific and aggressive */
.dark .box-2fa-input {
  color: white !important;
  caret-color: white !important;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.5) !important;
}

/* Make cursor more visible in dark mode */
.dark .box-2fa-input::selection {
  background-color: rgba(255, 255, 255, 0.5) !important;
}

/* Ensure cursor is visible when input is focused */
.dark .box-2fa-input:focus {
  caret-color: white !important;
  caret-shape: block !important;
  border-color: rgba(255, 255, 255, 0.6) !important;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
}

/* Ensure the input is visible in dark mode */
.dark input.box-2fa-input {
  -webkit-text-fill-color: white !important;
  color: white !important;
  background-color: transparent !important;
}

/* Animation for the custom cursor is provided by Tailwind's animate-pulse class */
