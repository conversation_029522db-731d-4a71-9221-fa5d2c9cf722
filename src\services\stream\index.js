import { fetchEventSource } from '@microsoft/fetch-event-source';

const danteStream = async ({
  url,
  control,
  method,
  body,
  headers,
  accessToken,
  onOpen,
  onMessage,
  onClose,
  onError
}) => {
  return fetchEventSource(url, {
    method,
    body,
    headers: {
      Accept: 'text/event-stream',
      'Content-Type': 'application/json',
      'x-client': 'dante',
      Authorization: `Bearer ${accessToken}`,
      ...headers
    },
    onopen: onOpen,
    onmessage: onMessage,
    onclose: onClose,
    onerror: onError,
    openWhenHidden: true,
    signal: control.signal
  });
};

export default danteStream;
