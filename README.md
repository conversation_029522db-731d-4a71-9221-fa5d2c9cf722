# dante-frontend-app-v3

### Adding New Public Routes

Public routes in **Dante's main APP** enable access to specific URLs without requiring user authentication. These routes are ideal for scenarios where lightweight dependencies are essential, such as sharing content or embedding elements into external websites.

**Why Use Public Routes?**

Public routes are designed to optimize performance and usability by adhering to the following principles:

- **Load Efficiency:** Each URL loads only the essential files, libraries, and analytics, reducing unnecessary overhead.
- **Performance Optimization:** Minimal dependencies lead to faster loading times and improved responsiveness.
- **Enhanced SEO:** Public routes improve **Search Engine Optimization (SEO)** by ensuring crawlable, lightweight pages.
- **Better Page Speed Scores:** Leaner page designs contribute to higher Page Speed Insights scores, which is crucial when embedding elements into external websites.

**Examples of Public Routes**

Here are some examples of public routes and their potential use cases:

- `/share/[share_id]`
- `/embed/?kb_id=[kb_id]`
- `/embed/avatar/?avatar_id=[avatar_id]`

When adding a new public route, follow the steps below to properly configure the development and production environments.

#### Development Environment

1. **Create a new folder for the route**
   In the `src/entrypoints/` directory, create a new folder named after the route:

   ```bash
   mkdir src/entrypoints/new-route/
   ```

2. **Create the React entry file**
   Inside the new folder, create an `index.jsx` file that will initialize React, define the root element, and call the main `App` component:

   ```jsx
   // src/entrypoints/new-route/index.jsx
   import React from 'react';
   import ReactDOM from 'react-dom';
   import App from './App';

   ReactDOM.render(<App />, document.getElementById('root'));
   ```

3. **Create the route's main component**
   Add an `App.jsx` file in the same folder. This file will contain the initial scripts, such as the Router and Provider, if needed:

   ```jsx
   // src/entrypoints/new-route/App.jsx
   import React from 'react';
   import Router from '@/router/new-route';

   const App = () => (
     <Provider>
       <Router />
     </Provider>
   );

   export default App;
   ```

4. **Create the route file**
   If you use a Router, create a specific file to manage it:

   ```bash
   src/router/new-route/index.jsx
   ```

   ```jsx
   // src/router/new-route/index.jsx
   import React from 'react';
   import { BrowserRouter as Router, Route, Switch } from 'react-router-dom';

   const Router = () => (
     <Router>
       <Switch>
         <Route path="/" exact component={() => <div>Route Home</div>} />
       </Switch>
     </Router>
   );

   export default Router;
   ```

5. **Create the route's HTML file**
   Add a `new-route.html` file at the project's root:

   ```html
   <!-- new-route.html -->
   <!DOCTYPE html>
   <html lang="en">
     <head>
       <meta charset="UTF-8" />
       <title>New Route</title>
     </head>
     <body>
       <div id="root"></div>
       <script
         type="module"
         src="/src/entrypoints/new-route/index.jsx"
       ></script>
     </body>
   </html>
   ```

6. **Configure the entry point in `vite.config.js`**
   In the Vite configuration file, add the new HTML as an entry point:

   ```js
   import { resolve } from 'path';

   export default {
     // ...
     build: {
       rollupOptions: {
         input: {
           // ...
           'new-route': resolve(__dirname, 'new-route.html'),
         },
       },
     },
   };
   ```

7. **Configure the development server (`server.dev.js`)**
   Add the route to the server to serve the HTML file:

   ```js
   import { join } from 'path';
   import { promises as fs } from 'fs';

   let newRouteTemplate = await fs.readFile(
     join(__dirname, 'new-route.html'),
     'utf-8'
   );

   app.get('/new-route/*', (req, res) => {
     renderHtml(req, res, newRouteTemplate);
   });
   ```

#### Production Environment
