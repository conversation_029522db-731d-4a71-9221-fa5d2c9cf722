import generateApiEndpoint from '@/helpers/generateApiEndpoint';
import http from './http';
import { DEFAULT_HEADERS } from './constants.service';

export const playAudioTTS = () => {
  return http.post(
    generateApiEndpoint('tts/play'),
    {},
    {
      params: {},
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};
export const playSharedAudioTTS = (token) => {
  return http.post(
    generateApiEndpoint('tts/play-shared'),
    {},
    {
      params: {
        token: token
      },
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};