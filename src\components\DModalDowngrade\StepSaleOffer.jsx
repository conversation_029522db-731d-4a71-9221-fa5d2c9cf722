import { useUserStore } from '@/stores/user/userStore';
import { downgradeStepsEnum } from './stepDowngrade';
import { useEffect } from 'react';
import DButton from '../Global/DButton';
import { applyDiscountOffer } from '@/services/plans.service';
import useToast from '@/hooks/useToast';

const StepSaleOffer = ({
  handleNextStep,
  handleClose,
  setTitle,
}) => {
  const { user } = useUserStore();
  const { addSuccessToast } = useToast();

  const handleAcceptOffer = async () => {
   try{
    const response = await applyDiscountOffer(user?.id);
    if(response?.status === 200){
      addSuccessToast({message: '50% discount applied to your next payment.'});
      handleClose();
    }
   }catch(error){
    console.log(error);
   }
  };

  const handleReject = () => {
    handleNextStep(downgradeStepsEnum.TEAM);
  };

  const handleGoBack = () => {
    handleNextStep(downgradeStepsEnum.REASONS);
  };

  useEffect(() => {
    setTitle('');
  }, []);

  return (
    <div className="flex flex-col gap-size5 relative">
      {/* Header with title and close button */}
      <div className="absolute top-[-45px] left-0 right-0 -mt-14 bg-grey-5 rounded-t-size1 border border-grey-5 border-b-0 flex items-center py-size1 px-size3">
        <div className="flex items-center gap-size2">
          <div className="bg-purple-5 rounded-full p-[6px] flex items-center justify-center">
            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-purple-200">
              <span className="text-white font-bold text-sm">✓</span>
            </div>
          </div>
          <span className="font-medium text-sm h-[19px]">Special Offer</span>
        </div>
        <button
          onClick={handleClose}
          className="ml-auto text-grey-50 hover:text-grey-75 text-xl font-bold"
          aria-label="Close"
        >
          ×
        </button>
      </div>

      {/* Main content */}
      <div className="flex flex-col gap-size3">
        <div className="flex flex-col gap-size1">
          <h1 className="text-xl font-bold">One-time Offer</h1>
          <p className="text-grey-50 text-xs leading-4 font-medium">
            Keep your subscription at half the original price.
          </p>
          <p className="text-grey-50 text-xs leading-4 font-medium">
            Need help? <a href="https://calendly.com/jetelira/dante-ai-demo-app" className="text-purple-200">Speak to our team.</a>
          </p>
        </div>

        {/* Offer box */}
        <div className="bg-grey-2 py-size4 px-size5 rounded-size1 flex flex-col gap-size3">
          <p className="text-grey-50 font-medium text-xs">Claim your limited-time offer:</p>
          <h2 className="text-xl font-bold">50% off for 2 months</h2>
          {/* <DButton
            size="md"
            className='text-sm h-10 bg-purple-200 text-white !rounded-md'
            onClick={handleAcceptOffer}
            fullWidth
          >
            Accept This Offer
          </DButton> */}
        </div>

        {/* Bottom buttons */}
        <div className="flex gap-size3 pt-size2">
          <DButton
            variant="outlined"
            size="md"
            className='text-sm h-10'
            onClick={handleReject}
            fullWidth
          >
            Decline Offer
          </DButton>
          <DButton
            variant="puple"
            size="md"
            className='text-sm h-10 bg-purple-200 text-white'
            onClick={handleAcceptOffer}
            fullWidth
          >
            Accept Offer
          </DButton>
        </div>
      </div>
    </div>
  );
};

export default StepSaleOffer;
