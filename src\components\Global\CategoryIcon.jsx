const CategoryIcon = (props) => {
    return   <svg
        width={12}
        height={14}
        viewBox="0 0 12 14"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.26408 11.7027L9.60768 13.3745C9.77745 13.4593 9.95809 13.5001 10.1364 13.5C10.752 13.4998 11.318 13.0064 11.318 12.3174V1.68182C11.318 1.02912 10.7889 0.5 10.1362 0.5H1.86346C1.21076 0.5 0.681641 1.02912 0.681641 1.68182V12.3174C0.681641 13.0064 1.24758 13.4998 1.86322 13.5C2.04155 13.5001 2.22222 13.4593 2.39199 13.3745L5.73557 11.7027C5.90191 11.6195 6.09774 11.6195 6.26408 11.7027ZM10.1362 1.68182H1.86346V12.3174L5.20689 10.6457C5.20684 10.6457 5.20694 10.6457 5.20689 10.6457C5.70595 10.3962 6.29355 10.3961 6.7926 10.6456C6.79255 10.6456 6.79265 10.6457 6.7926 10.6456L10.1361 12.3174L10.1362 1.68182Z"
        fill="currentColor"
        />
  </svg>
}

export default CategoryIcon;