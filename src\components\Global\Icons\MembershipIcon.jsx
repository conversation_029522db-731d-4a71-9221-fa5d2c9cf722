const MembershipIcon = (props) => {
  return (
    <svg width="18" height="14" viewBox="0 0 18 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 1.5C6.72386 1.5 6.5 1.72386 6.5 2V7.06574L8.07212 6.01766C8.61096 5.65844 9.28547 5.56811 9.89984 5.7729C10.5265 5.98178 11.0182 6.47351 11.2271 7.10016L11.2478 7.16226C11.3931 7.59826 11.3992 8.06526 11.2706 8.5H16C16.2761 8.5 16.5 8.27614 16.5 8V4H8C7.72386 4 7.5 3.77614 7.5 3.5C7.5 3.22386 7.72386 3 8 3H16.5V2C16.5 1.72386 16.2761 1.5 16 1.5H7ZM10.1019 8.67817L10.2324 8.41715C10.3782 8.12549 10.4022 7.78784 10.2991 7.47849L10.2784 7.41639C10.1691 7.08835 9.91165 6.83093 9.58361 6.72158C9.26199 6.61438 8.90889 6.66166 8.62682 6.84971L4.77735 9.41603C4.54759 9.5692 4.23715 9.50711 4.08397 9.27735C3.9308 9.04759 3.99289 8.73715 4.22265 8.58397L5.5 7.73241V2.64039L4.6587 2.85071C4.39495 2.91665 4.15408 3.05303 3.96184 3.24527L1.35355 5.85355C1.15829 6.04882 0.841709 6.04882 0.646447 5.85355C0.451184 5.65829 0.451184 5.34171 0.646447 5.14645L3.25473 2.53816C3.57513 2.21776 3.97658 1.99047 4.41616 1.88057L5.55508 1.59584C5.73151 0.963728 6.3116 0.5 7 0.5H16C16.8284 0.5 17.5 1.17157 17.5 2V8C17.5 8.82843 16.8284 9.5 16 9.5H10.6754L5.67539 13.5H1C0.723858 13.5 0.5 13.2761 0.5 13C0.5 12.7239 0.723858 12.5 1 12.5H5.32461L10.1019 8.67817Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default MembershipIcon;
