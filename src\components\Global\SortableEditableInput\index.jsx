// SortableEditableInput.jsx

import DInputEditable from '../DInputEditable';
import DInput from '../DInput/DInput';
import DSwitch from '../DSwitch';
const SortableEditableInput = ({
  item,
  handleFieldChange,
  handleDelete,
  handleEdit,
  handleSwitchChange,
  dragHandleProps,
  handleInputChange,
  error,
  renderExtraContent
}) => {
  const identifier = item.id || item.frontend_id;
  return (
    <div className="flex flex-col gap-size1">
      <DInputEditable
        value={item.input_label}
        onChange={(e) => handleFieldChange(identifier, e.target.value)}
        onDelete={() => handleDelete(identifier)}
        onEdit={() => handleEdit(identifier)}
        is_draggable
        onDragListeners={dragHandleProps}
        is_deletable={item.is_deletable}
        is_editable={item.is_editable}
        placeholder={item.placeholder}
        className="h-[56px]"
        required={item.required}
        disclaimer={item.disclaimer}
        has_switch={item.has_switch}
        switchValue={item.switchValue}
        onSwitchChange={(value) => handleSwitchChange(identifier, value)}
        renderExtraContent={renderExtraContent ? () => renderExtraContent(item) : undefined}
      />
      {item.hasInput && <div className="ml-8">
        <DInput
          value={item.input_value}
          onChange={(e) => handleInputChange(identifier, e.target.value)}
          error={error[`linkItem${identifier}`] || ''}
          placeholder={item.placeholder_input}
        />
      </div>}
      {item?.additionalSwitch && item?.additionalSwitch.show && <div className="ml-9 px-size1 flex items-center justify-between">
        <div className="flex items-center gap-size1">
          <p className={`text-sm font-regular tracking-tight ${item.additionalSwitch.beta ? 'text-grey-50' : ''}`}>{item.additionalSwitch.label}</p>
          {item.additionalSwitch.beta && <div className="bg-black text-white px-2 py-1 rounded-full text-[10px]">Beta</div>}
        </div>
        <DSwitch
          value={item.additionalSwitch.active}
          onChange={(value) => item.additionalSwitch.onChange(value)}
        />
      </div>}
    </div>
  );
};

export default SortableEditableInput;
