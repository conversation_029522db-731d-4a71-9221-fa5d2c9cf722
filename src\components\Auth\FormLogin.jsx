import { useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';

import loginUseCase from '@/application/auth/login';
import loginGoogleUseCase from '@/application/auth/loginGoogle';
import DButton from '@/components/Global/DButton';
import DInput from '@/components/Global/DInput/DInput';
import DInputBlock from '@/components/Global/DInput/DInputBlock';
import GoogleIcon from '@/components/Global/Icons/GoogleIcon';
import ReviewTrustPilot from '@/components/Onboarding/ReviewTrustPilot';
import LayoutPublic from '@/layouts/LayoutPublic';
import { useGoogleLogin } from '@react-oauth/google';

const LoginForm = ({
  error,
  handleSubmit,
  handleChange,
  handleGoogleLogIn,
  pending,
}) => {
  return (
    <form
      className="flex flex-col gap-size5 items-center w-full"
      onSubmit={handleSubmit}
    >
      <div className="w-full">
        <h1 className="text-2xl md:text-3xl">Welcome back</h1>
        <h2 className="text-2xl md:text-3xl text-grey-50">
          Log in to Dante AI
        </h2>
      </div>
      <div className="flex flex-col gap-size3 w-full">
        <DInputBlock label="Email" name="email">
          <DInput
            name="email"
            type="email"
            autoComplete="email"
            placeholder="Enter your email"
            onChange={handleChange}
          />
        </DInputBlock>
        <DInputBlock label="Password" name="password">
          <DInput
            name="password"
            type="password"
            placeholder="Enter your password"
            autoComplete="current-password"
            onChange={handleChange}
          />
        </DInputBlock>
        <Link to="/forgot-password" className="text-black text-xs w-max">
          Forgot password?
        </Link>
        <DButton
          type="submit"
          variant="dark"
          size="lg"
          fullWidth
          className="mt-size5"
          loading={pending}
          name="login-button"
        >
          Log in with email
        </DButton>
        {error && <p className="text-error">{error}</p>}
      </div>
      <p className="text-grey-50">or</p>
      <DButton
        variant="light"
        size="sm"
        className="w-max"
        onClick={handleGoogleLogIn}
      >
        <GoogleIcon />
        Log in with Google
      </DButton>
    </form>
  );
};

export default LoginForm;
