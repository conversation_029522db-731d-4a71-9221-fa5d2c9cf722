import { useRef, useState, useOptimistic, useEffect, useCallback } from 'react';
import DInput from '../Global/DInput/DInput';
import DSelect from '../Global/DSelect';
import DColorInput from '../Global/DColorInput';
import DTextArea from '../Global/DInput/DTextArea';
import DButton from '../Global/DButton';
import DAlert from '../Global/DAlert';
import LinkIcon from '../Global/Icons/LinkIcon';
import CameraIcon from '../Global/Icons/CameraIcon';
import { DANTE_THEME_CHAT, DANTE_THEME_COLOR_CHAT } from '@/constants';
import DTooltip from '../Global/DTooltip';
import InfoIcon from '../Global/Icons/InfoIcon';
import DSwitch from '../Global/DSwitch';
import { useUserStore } from '@/stores/user/userStore';
import { activateNewDesign } from '@/services/chatbot.service';
import DConfirmationModal from '../Global/DConfirmationModal';
import useToast from '@/hooks/useToast';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import cssValidator from 'w3c-css-validator';
import DeleteIcon from '../Global/Icons/DeleteIcon';
import { throttle } from 'lodash';
import featureCheck from '@/helpers/tier/featureCheck';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';

const Branding = ({
  customizationData,
  updateCustomizationData,
  updateCustomizationDataBatch,
  isSaveLoading,
}) => {
  const chatbotLogoRef = useRef(null);
  const user = useUserStore((state) => state.user);
  const setUser = useUserStore((state) => state.setUser);
  const { addSuccessToast } = useToast();
  const teamSelected = useTeamManagementStore((state) => state.selectedTeam?.id);
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );

  const [chatbotLogo, setChatbotLogo] = useState(null);
  const [chatbotIcon, setChatbotIcon] = useState(null);
  const [customCSSError, setCustomCSSError] = useState(null);
  const [openActivateNewDesign, setOpenActivateNewDesign] = useState(false);
  const [activateNewDesignLoading, setActivateNewDesignLoading] =
    useState(false);
  //use state for colors so they don't change on save
  const [brandColor, setBrandColor] = useState(customizationData?.brand_color);
  const [elementColor, setElementColor] = useState(
    customizationData?.element_color
  );
  const [surfaceColor, setSurfaceColor] = useState(
    customizationData?.surface_color
  );
  const [alertColor, setAlertColor] = useState(customizationData?.alert_color);
  const [positiveColor, setPositiveColor] = useState(
    customizationData?.positive_color
  );
  const [negativeColor, setNegativeColor] = useState(
    customizationData?.negative_color
  );

  const [customCSS, setCustomCSS] = useState(customizationData?.custom_css);

  const allFontFamilies = [
    {
      label: 'Default',
      value: 'Default',
    },
    {
      label: 'DM Sans',
      value: 'DM Sans',
    },
    {
      label: 'Helvetica',
      value: 'Helvetica',
    },
    {
      label: 'Georgia',
      value: 'Georgia',
    },
    {
      label: 'Tahoma',
      value: 'Tahoma',
    },
    {
      label: 'Courier',
      value: 'Courier',
    },
    {
      label: 'Roboto',
      value: 'Roboto',
    },
    {
      label: 'Inter',
      value: 'Inter',
    },
    {
      label: 'Montserrat',
      value: 'Montserrat',
    },
    {
      label: 'Lato',
      value: 'Lato',
    },
    {
      label: 'Oswald',
      value: 'Oswald',
    },
    {
      label: 'Bree Serif',
      value: 'Bree Serif',
    },
    {
      label: 'Arvo',
      value: 'Arvo',
    },
    {
      label: 'RobotoSlab',
      value: 'RobotoSlab',
    },
    {
      label: 'Merriweather',
      value: 'Merriweather',
    },
  ];

  const fontSizes = [
    {
      label: '10',
      value: 10,
    },
    {
      label: '11',
      value: 11,
    },
    {
      label: '12',
      value: 12,
    },
    {
      label: '13',
      value: 13,
    },
    {
      label: '14',
      value: 14,
    },
    {
      label: '15',
      value: 15,
    },
    {
      label: '16',
      value: 16,
    },
    {
      label: '18',
      value: 18,
    },
    {
      label: '20',
      value: 20,
    },
    {
      label: '24',
      value: 24,
    },
  ];

  const chatbotIconRef = useRef(null);

  const handleImageChange = (e, type) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (type === 'logo') {
          setChatbotLogo(reader.result);
          updateCustomizationData('embed_logo_url', file);
        } else if (type === 'icon') {
          setChatbotIcon(reader.result);
          updateCustomizationData('chatbot_icon', file);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const throttledHandleCustomCSSChange = useCallback(
    throttle(async (value) => {
      try {
        const result = await cssValidator.validateText(value);

        let errorMsg = '';

        if (result.valid) {
          updateCustomizationData('custom_css', value);
          setCustomCSSError(null);
        } else {
          result.errors.forEach((error) => {
            errorMsg += error.message + '. ';
          });
          setCustomCSSError(errorMsg);
        }
        console.log(result);
      } catch (error) {
        setCustomCSSError('Invalid CSS');
      }
    }, 1000),
    [customizationData]
  );

  const handleCustomCSSChange = (value) => {
    if (!teamSelected && !featureCheck('custom_css')) {
      return;
    }

    if (value === '') {
      setCustomCSS('');
      updateCustomizationData('custom_css', '');
      return;
    }
    setCustomCSS(value);
    throttledHandleCustomCSSChange(value);
  };

  const handleResetChanges = () => {
    // Reset color fields
    Object.keys(DANTE_THEME_COLOR_CHAT).forEach((key) => {
      updateCustomizationData(key, DANTE_THEME_COLOR_CHAT[key]);
    });

    // Reset font name
    updateCustomizationData('font_name', DANTE_THEME_CHAT.font_name);

    // Reset font size
    updateCustomizationData('font_size', DANTE_THEME_CHAT.font_size);

    // Reset brand website
    updateCustomizationData('brand_website', 'https://');

    // Reset header logo
    updateCustomizationData('embed_logo_url', '');
    setChatbotLogo(null);

    // Reset chatbubble icon
    updateCustomizationData('chatbot_icon', '');
    setChatbotIcon(null);

    // Reset custom CSS
    setCustomCSS('');
    setCustomCSSError(null);
    updateCustomizationData('custom_css', '');
  };

  const handleActivateNewDesign = async () => {
    try {
      setActivateNewDesignLoading(true);
      const response = await activateNewDesign(customizationData?.kb_id);
      if (response.status === 200) {
        setSelectedChatbot({
          ...selectedChatbot,
          knowledge_base: {
            ...selectedChatbot.knowledge_base,
            new_design_activated: true,
          },
        });
        setBrandColor(response.data.brand_color);
        setSurfaceColor(response.data.surface_color);
        setElementColor(response.data.element_color);
        updateCustomizationData('brand_color', response.data.brand_color);
        updateCustomizationData('surface_color', response.data.surface_color);
        updateCustomizationData('element_color', response.data.element_color);
        addSuccessToast({ message: 'New design activated successfully' });
        setOpenActivateNewDesign(false);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setActivateNewDesignLoading(false);
    }
  };

  const handleChange = (colorType, colorHex) => {
    switch (colorType) {
      case 'brand_color':
        setBrandColor(colorHex);
        break;
      case 'element_color':
        setElementColor(colorHex);
        break;
      case 'surface_color':
        setSurfaceColor(colorHex);
        break;
      case 'alert_color':
        setAlertColor(colorHex);
        break;
      case 'positive_color':
        setPositiveColor(colorHex);
        break;
      case 'negative_color':
        setNegativeColor(colorHex);
        break;
      default:
        break;
    }
    // Call the backend update function
    updateCustomizationData(colorType, colorHex);
  };

  useEffect(() => {
    setBrandColor(customizationData?.brand_color);
    setElementColor(customizationData?.element_color);
    setSurfaceColor(customizationData?.surface_color);
    setAlertColor(customizationData?.alert_color);
    setPositiveColor(customizationData?.positive_color);
    setNegativeColor(customizationData?.negative_color);
    setCustomCSS(customizationData?.custom_css);
  }, [customizationData]);

  useEffect(() => {
    if (
      customizationData?.embed_logo_url &&
      customizationData?.embed_logo_url instanceof File &&
      !isSaveLoading
    ) {
      setChatbotLogo(URL.createObjectURL(customizationData?.embed_logo_url));
    } else if (
      customizationData?.embed_logo_url &&
      typeof customizationData?.embed_logo_url === 'string' &&
      !isSaveLoading
    ) {
      setChatbotLogo(customizationData?.embed_logo_url);
    }
    if (
      customizationData?.chatbot_icon &&
      customizationData?.chatbot_icon instanceof File &&
      !isSaveLoading
    ) {
      setChatbotIcon(URL.createObjectURL(customizationData?.chatbot_icon));
    } else if (
      customizationData?.chatbot_icon &&
      typeof customizationData?.chatbot_icon === 'string' &&
      !isSaveLoading
    ) {
      setChatbotIcon(customizationData?.chatbot_icon);
    }
  }, [customizationData]);

  return (
    <>
      <div className="flex flex-col gap-size1 w-full">
        {!selectedChatbot?.knowledge_base?.new_design_activated && (
            <div className="flex flex-col gap-size1 mb-size2">
              <div className="flex items-center gap-size0">
                <DSwitch
                  label="Activate new design"
                  onChange={() => setOpenActivateNewDesign(true)}
                  id="activate-new-design"
                />
                <DTooltip content="By enabling the new design, your AI Chatbot will adopt a refreshed appearance and gain access to exciting new features. If you’d prefer to stick with the old design, you can continue using it until <b>31 December</b>. To keep the old design, make sure not to toggle this option on.">
                  <InfoIcon className="text-grey-50 size-3 ml-1" />
                </DTooltip>
              </div>
              <p className="text-xs tracking-tight text-grey-50">
                Activate the new deisgn to upgrade your AI Chatbot’s look. The
                changes shown below will only take effect once your activate
                this setting.
              </p>
            </div>
          )}
        <div className="flex items-center gap-size0">
          <p className="text-base font-medium tracking-tight">Brand website</p>
          <DTooltip
            content="This link will be used as the logo URL on shared AI Chatbots"
            position="right"
          >
            <InfoIcon className="text-grey-50 size-3" />
          </DTooltip>
        </div>
        <DInput
          iconPlacement="pre"
          icon={<LinkIcon />}
          placeholder="Type in brand website"
          value={customizationData?.brand_website || 'https://'}
          onChange={(e) => {
            updateCustomizationData('brand_website', e.target.value);
          }}
          id="brand_website"
        />
      </div>
      <div className="flex gap-size5 w-full">
        <div className="flex flex-col gap-size1 w-full">
          <p className="text-base font-medium tracking-tight">Font family</p>
          <DSelect
            value={customizationData?.font_name || DANTE_THEME_CHAT.font_name}
            onChange={(value) => {
              updateCustomizationData('font_name', value);
            }}
            options={allFontFamilies}
            id="font_name"
          />
        </div>
        <div className="flex flex-col gap-size1 w-full">
          <p className="text-base font-medium tracking-tight">Font size</p>
          <DSelect
            value={customizationData?.font_size || DANTE_THEME_CHAT.font_size}
            onChange={(value) => {
              updateCustomizationData('font_size', value);
            }}
            options={fontSizes}
            id="font_size"
          />
        </div>
      </div>
      <div className="w-full h-[1px] bg-grey-5"></div>
      <div className="flex gap-size5 w-full">
        <div className="flex flex-col gap-size1 w-full">
          <div className="flex items-center gap-size0">
            <p className="text-base font-medium tracking-tight">Brand color</p>
            <DTooltip content="This color represents your AIchatbot's brand.">
              <InfoIcon className="text-grey-50 size-3" />
            </DTooltip>
          </div>
          <DColorInput
            value={brandColor}
            onChange={(color) => handleChange('brand_color', color.hex)}
            id="brand_color"
          />
        </div>
        <div className="flex flex-col gap-size1 w-full">
          <div className="flex items-center gap-size0">
            <p className="text-base font-medium tracking-tight">Alert color</p>
            <DTooltip content="This color represents the AI Chatbot alerts.">
              <InfoIcon className="text-grey-50 size-3" />
            </DTooltip>
          </div>
          <DColorInput
            value={alertColor}
            onChange={(color) => handleChange('alert_color', color.hex)}
            id="alert_color"
          />
        </div>
      </div>
      <div className="flex gap-size5 w-full">
        <div className="flex flex-col gap-size1 w-full">
          <div className="flex items-center gap-size0">
            <p className="text-base font-medium tracking-tight">
              Surface color
            </p>
            <DTooltip content="This color represents the AI Chatbot background.">
              <InfoIcon className="text-grey-50 size-3" />
            </DTooltip>
          </div>
          <DColorInput
            value={surfaceColor}
            onChange={(color) => handleChange('surface_color', color.hex)}
            id="surface_color"
          />
        </div>
        <div className="flex flex-col gap-size1 w-full">
          <div className="flex items-center gap-size0">
            <p className="text-base font-medium tracking-tight">
              Positive color
            </p>
            <DTooltip content="This color represents the AI Chatbot positive responses.">
              <InfoIcon className="text-grey-50 size-3" />
            </DTooltip>
          </div>
          <DColorInput
            value={positiveColor}
            onChange={(color) => handleChange('positive_color', color.hex)}
            id="positive_color"
          />
        </div>
      </div>
      <div className="flex gap-size5 w-full">
        <div className="flex flex-col gap-size1 w-full">
          <div className="flex items-center gap-size0">
            <p className="text-base font-medium tracking-tight">Font color</p>
            <DTooltip content="This color represents the chatbot text.">
              <InfoIcon className="text-grey-50 size-3" />
            </DTooltip>
          </div>

          <DColorInput
            value={elementColor}
            onChange={(color) => handleChange('element_color', color.hex)}
            id="element_color"
          />
        </div>
        <div className="flex flex-col gap-size1 w-full">
          <div className="flex items-center gap-size0">
            <p className="text-base font-medium tracking-tight">
              Negative color
            </p>
            <DTooltip content="This color represents the AI Chatbot negative responses.">
              <InfoIcon className="text-grey-50 size-3" />
            </DTooltip>
          </div>
          <DColorInput
            value={negativeColor}
            onChange={(color) => handleChange('negative_color', color.hex)}
            id="negative_color"
          />
        </div>
      </div>

      <div className="w-full h-[1px] bg-grey-5"></div>
      <div className="flex gap-size5 w-full">
        <div className="flex flex-col gap-size1 w-full">
          <p className="text-base font-medium tracking-tight">Header logo</p>
          <div
            className="flex items-center justify-center gap-size1 border-dashed border border-black-10 h-20 w-full rounded-size2 p-size1 cursor-pointer relative"
            onClick={() => {
              chatbotLogoRef.current.click();
            }}
          >
            {customizationData?.embed_logo_url || chatbotLogo ? (
              <img
                src={
                  typeof customizationData?.embed_logo_url === 'string'
                    ? customizationData?.embed_logo_url
                    : chatbotLogo
                }
                alt="Chatbot Logo Preview"
                className="h-full w-full object-contain rounded-size2"
              />
            ) : (
              <CameraIcon />
            )}
            <DButton
              className="!size-6 !absolute right-[-5px] bottom-[-5px] !z-10 text-grey-50 bg-white !p-[2px] !rounded-full"
              onClick={(e) => {
                e.stopPropagation();
                updateCustomizationData('embed_logo_url', '');
                setChatbotLogo(null);
              }}
            >
              <DeleteIcon />
            </DButton>
          </div>
          <input
            type="file"
            className="hidden"
            onChange={(e) => handleImageChange(e, 'logo')}
            ref={chatbotLogoRef}
          />
          <span className="text-xs tracking-tight text-grey-20">
            *Supported file types: PNG, JPEG, JPG. Max width: 150px, max height:
            50px
          </span>
        </div>
        <div className="flex flex-col gap-size1 w-full">
          <p className="text-base font-medium tracking-tight">
            Chatbubble icon
          </p>
          <div
            className="flex items-center justify-center gap-size1 border border-dashed border-black-10 h-20 w-full rounded-size2 p-size1 cursor-pointer relative"
            onClick={() => {
              chatbotIconRef.current.click();
            }}
          >
            {chatbotIcon ? (
              <img
                src={chatbotIcon}
                alt="Chatbot Icon Preview"
                className="h-full w-full object-contain rounded-size2"
              />
            ) : (
              <CameraIcon />
            )}
            <DButton
              className="!size-6 !absolute right-[-5px] bottom-[-5px] !z-10 text-grey-50 bg-white !p-[2px] !rounded-full"
              onClick={(e) => {
                e.stopPropagation();
                updateCustomizationData('chatbot_icon', '');
                setChatbotIcon(null);
              }}
            >
              <DeleteIcon />
            </DButton>
          </div>
          <input
            type="file"
            className="hidden"
            onChange={(e) => {
              handleImageChange(e, 'icon');
            }}
            ref={chatbotIconRef}
          />
        </div>
      </div>
      <div className="flex flex-col gap-size1 w-full">
        <DTextArea
          label="Custom CSS"
          value={customCSS}
          onChange={(e) => handleCustomCSSChange(e.target.value)}
          error={customCSSError}
          minRows={10}
          resize
          id="custom-css"
          autoResize
        />
      </div>
      <div className="flex gap-size2 items-center pb-size3">
        <DButton
          variant="dark"
          onClick={handleResetChanges}
          className="!min-w-36"
          data-testid="reset-changes-button"
        >
          Reset Changes
        </DButton>
        <DAlert state="info" className="!w-full flex items-center">
          <p className="text-sm tracking-tight">
          How to get started with Dante AI: <a href="https://www.dante-ai.com/guides/chatbot-personality" target="_blank" rel="noopener noreferrer" className="text-black font-bold">Visit our Learning Hub</a>
          </p>
        </DAlert>
      </div>
      <DConfirmationModal
        open={openActivateNewDesign}
        onClose={() => setOpenActivateNewDesign(false)}
        onConfirm={handleActivateNewDesign}
        title="Activate New Design"
        description="Are you sure you want to activate the new design? This action cannot be undone."
        confirmText="Activate"
        cancelText="Cancel"
        loading={activateNewDesignLoading}
      />
    </>
  );
};

export default Branding;
