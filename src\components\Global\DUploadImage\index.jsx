import { useEffect, useRef, useState } from 'react';
import clsx from 'clsx';
import CameraIcon from '../Icons/CameraIcon';

const tolerance = 0.01;

const DUploadImage = ({
  label,
  handleImageChange,
  imageUrl,
  imageFile,
  error,
  supportedFileTypes = '*Supported file types: PNG, JPEG, JPG. Max width: 150px, max height: 50px',
  ratioWith = 1,
  ratioHeight = 1,
  maxWidth = 150,
  maxHeight = 50,
  maxSize = 150 * 1024,
  name,
  setImageError,
  required,
  ...props
}) => {
  const imageRef = useRef(null);
  const ratio = ratioWith / ratioHeight;

  const [errorMessage, setErrorMessage] = useState([]);

  useEffect(() => {
    const checkImageSize = async () => {
      const newErrors = [];
      let hasErrors = false;
      if (imageFile instanceof File) {
        if (imageFile.size > maxSize) {
          newErrors.push(`Max file size: ${maxSize / 1024}kb`);
          hasErrors = true;
        } else {
          const isImageValid = await new Promise((resolve) => {
            const image = new Image();
            image.src = URL.createObjectURL(imageFile);
            image.onload = () => {
              const imageRatio = image.width / image.height;
              const compare = Math.abs(imageRatio - ratio) <= tolerance;
              if (compare) {
                resolve(true);
              } else {
                resolve(false);
              }
            };
            image.onerror = () => resolve(false);
          });

          if (!isImageValid) {
            newErrors.push(
              `Image not in the correct ratio: ${ratioWith}:${ratioHeight}`
            );
            hasErrors = true;
          }
        }
      }
      setErrorMessage(newErrors);

      setImageError(name, newErrors.join(', '));

      return !hasErrors;
    };
    checkImageSize();
  }, [imageFile]);

  return (
    <div className="flex flex-col gap-size1 w-full">
      {label && (
        <p className="text-base font-medium tracking-tight">
          {label} {required && <span className="text-red-500">*</span>}
        </p>
      )}
      <div
        className={clsx(
          'flex items-center justify-center gap-size1 border-dashed border h-20 w-full rounded-size2 p-size1 cursor-pointer',
          {
            'border-black-10': !error,
            'border-red-500': error,
          }
        )}
        onClick={() => imageRef.current?.click()}
      >
        {imageUrl || imageFile ? (
          <img
            src={typeof imageUrl === 'string' ? imageUrl : imageFile}
            alt="Image Preview"
            className="h-full w-full object-contain rounded-size2"
          />
        ) : (
          <CameraIcon />
        )}
      </div>
      <input
        type="file"
        className="hidden"
        onChange={handleImageChange}
        ref={imageRef}
        accept="image/png, image/jpeg, image/jpg"
      />
      <span className={clsx('text-xs tracking-tight text-grey-20')}>
        Format: PNG, JPEG, JPG • Image ratio: {ratioWith}:{ratioHeight} • Size:{' '}
        {maxSize / 1024}kb max
      </span>
      <span className={clsx('tracking-tight text-error min-h-4')}>{error}</span>
    </div>
  );
};

export default DUploadImage;
