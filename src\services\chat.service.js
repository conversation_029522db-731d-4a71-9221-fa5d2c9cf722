import DOMPurify from 'dompurify';
import http from './http';
import danteStream from './stream';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import generateApiEndpoint from '@/helpers/generateApiEndpoint';

const sendMessage = async ({
  origin,
  conversation,
  cookie,
  accessToken,
  onOpen,
  onMessage,
  onClose,
  onError,
  control
}) => {
  const { question, llmModel, userID, message, id: conversationId } = conversation;
  const apiURL = new URL(`${import.meta.env.VITE_APP_BASE_API}`);
  const teamSelected = useTeamManagementStore.getState().selectedTeam?.id;

  apiURL.pathname = `model/${origin === 'inside' ? 'query-stream' : 'query-shared-stream'}`;

  apiURL.searchParams.set('question', DOMPurify.sanitize(question));
  apiURL.searchParams.set('model_type', llmModel);
  apiURL.searchParams.set('conversation_id', conversationId);
  if(teamSelected) {
    apiURL.searchParams.set('team_id', teamSelected);
  }

  if (userID) {
    apiURL.searchParams.set('user_generated_id', userID);
  }

  if (origin === 'outside') {
    apiURL.searchParams.set('token', accessToken);
  }

  return danteStream({
    url: apiURL.toString(),
    method: 'POST',
    body: JSON.stringify({
      message
    }),
    headers: {
      ...(origin === 'outside' ? { Cookies: cookie } : {})
    },
    accessToken,
    onOpen,
    onMessage,
    onClose,
    onError,
    control
  });
};

export const sendMessageInApp = async ({
  conversation,
  cookie,
  accessToken,
  onOpen,
  onMessage,
  onClose,
  onError,
  control
}) => {
  const origin = 'inside';

  return sendMessage({
    origin,
    conversation,
    cookie,
    accessToken,
    onOpen,
    onMessage,
    onClose,
    onError,
    control
  });
};

export const sendMessageEmbed = async ({
  conversation,
  cookie,
  accessToken,
  onOpen,
  onMessage,
  onClose,
  onError,
  control
}) => {
  const origin = 'outside';
  return sendMessage({
    origin,
    conversation,
    cookie,
    accessToken,
    onOpen,
    onMessage,
    onClose,
    onError,
    control
  });
};

export const createSharedFormData = (conv_id, token, collection_fields) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'conversations/shared/' + conv_id + '/form_data',
    collection_fields,
    {
      params: {
        conversation_id: conv_id,
        shared_kb_token: token
      },
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const requestTakeover = (conversation_id, token, body) => {
  return http.post(
    generateApiEndpoint(`human-handover/conversations/shared/${conversation_id}/flag`),
    body,
    {
      params: {
        token: token
      },
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const pollTakeover = (agent_takeover_request_id) => {
  return http.get(
    import.meta.env.VITE_APP_BASE_API +
      'agent/agent-takeover-request/' +
      agent_takeover_request_id +
      '/poll',
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const showDynamicButtons = (question, conversation_id, token, options) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'model/show-data-collection',
    {
      question,
      conversation_id,
      token,
      ...options
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

export const sendEmailTranscript = (conversation_id, token, email, timezone) => {
  return http.post(
    import.meta.env.VITE_APP_BASE_API + 'conversations/shared/' + conversation_id + '/transcript',
    {},
    {
      params: {
        token: token,
        email: email,
        timezone: timezone
      },
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};