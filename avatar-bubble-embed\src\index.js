import './bubble-embed.css';

(async function () {
  async function getChromeVersion() {
    // Retrieve high-entropy values from the browser's userAgentData for detailed browser info
    const data = await navigator?.userAgentData?.getHighEntropyValues(['fullVersionList']);

    // Find Chrome version in the full version list
    const browser = data?.fullVersionList.find((d) => d.brand.toLowerCase().includes('chrome'));

    // Return false if Chrome version is not found
    if (!browser) return false;

    // Extract major version and subversions from the version string
    const [majorVersion, ...subVersionParts] = browser.version.split('.');
    const subVersion = subVersionParts.join('.');

    // Return parsed major version and subversion
    return { majorVersion: parseInt(majorVersion, 10), subVersion };
  }

  function versionCompare(v1, v2) {
    // Split version strings into arrays of numbers for comparison
    const v1Parts = v1.split('.').map(Number);
    const v2Parts = v2.split('.').map(Number);

    // Compare corresponding parts of both versions
    for (let i = 0; i < v1Parts.length; i++) {
      if (v1Parts[i] < v2Parts[i]) return -1; // v1 is smaller
      if (v1Parts[i] > v2Parts[i]) return 1; // v1 is larger
    }

    // If all parts are equal, return 0
    return 0;
  }

  const checkVersion = async () => {
    const chromeVersion = await getChromeVersion();
    const disallowedVersion = '128.0.6613.113';

    if (chromeVersion) {
      // Check if major version is 128
      if (chromeVersion.majorVersion === 128) {
        // Compare subversion against the disallowed version
        if (
          versionCompare(
            `${chromeVersion.majorVersion}.${chromeVersion.subVersion}`,
            disallowedVersion
          ) < 0
        ) {
          return false; // Disable feature for versions below disallowed version
        } else {
          return true; // Enable feature for versions above or equal to disallowed version
        }
      } else {
        return true; // Enable feature for other major versions
      }
    } else {
      return true; // Enable feature if Chrome version is not found
    }
  };

  // Determine whether to show the feature based on the Chrome version check
  const shouldShow = await checkVersion();

  const mountEmbed = () => {
    let embedOpen = false;
    let styleBaseDivChat = 'position: fixed;width:456px;height: 85vh;max-height:800px;bottom: 25px;right: 25px;z-index: 999999;opacity: 0;transition: all 300ms ease-in-out;pointer-events: none;border: none;border-radius: 16px;box-shadow: 0px 10px 16px rgba(0, 0, 0, 0.25);display: flex;align-items: center;justify-content: center;background: #f8f8f8;';
    let chatIframeStyle =
      'position:absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; border-radius: 16px;';
    let styleBaseTooltip = 'position:fixed;width:396px;height:260px; bottom:150px; right: 30px;z-index:999990; border:none; pointer-events:auto;';
    let styleDivTooltip = 'position:fixed;width:310px;height:260px; bottom:150px; right: 30px;z-index:999990; cursor: pointer;';
    let styleImgDefault =
      'position:fixed;width:120px;height:120px; bottom:32px; right: 32px;z-index:99999;background:#fff;border-radius:50%; transition: transform 300ms ease-in-out, box-shadow 1500ms ease-in-out; cursor:pointer; box-shadow:0px 0px 8px 3px rgba(0,0,0, 0.2); ';
    let styleCloseChatBubble =
      'position:absolute;width:24px;height:24px; top:20px; right: 16px; display: flex; align-items:center; justify-content: center; z-index:9999999; cursor:pointer; opacity:0; pointer-events:none; ';

    const vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);
    // custom work to cleanup a white-label app
    // const cleanedURL = window.danteEmbed.replace('chat.helppi-ai.fi', 'app.dante-ai.com');
    // const embedUrl = new URL(cleanedURL);
    // const openByDefault = embedUrl.searchParams.get('bubbleopen');
    let cookiesAllowed = false;
    let cookiesEventData = false;
    let hasSentPrompt = false;

    if (vw < 600) {
      styleBaseDivChat = 'position:fixed;width:100%; max-width: 100%;height:100%;max-height:100%;bottom:0;top:0; right: 0;bottom:0;z-index:999999; opacity:0; transition: opacity 300ms ease-in-out; pointer-events:none; border-radius: 16px; box-shadow: 0px 10px 16px rgba(0, 0, 0, 0.25);';
      styleImgDefault = styleImgDefault += 'bottom:20px; right:20px;';
      styleBaseTooltip = 'position:fixed;width:100%;height:auto; bottom:110px; right: 30px;z-index:999990; border:none; pointer-events:none;';
      styleDivTooltip = 'position:fixed;width: calc(100% - 90px);height:auto; bottom:10px; right: 80px;height:200px; z-index:999990;';

      localStorage.setItem('embed-chatbot-responsive', true);
    } else {
      localStorage.setItem('embed-chatbot-responsive', false);
    }
    const styleImgHover = styleImgDefault + 'transform:scale(1.10);';

    const styleImgPulse = styleImgDefault + 'box-shadow:0px 0px 16px 3px rgba(0,0,0, 0.8);';

    const styleOpen = styleBaseDivChat + 'opacity:1; pointer-events:auto;overflow:hidden';
    const toggleEmbed = (forceOpen) => {
      if (forceOpen) {
        embedOpen = true;
      } else {
        embedOpen = !embedOpen;
      }
      if (cookiesAllowed) {
        localStorage.setItem('embed-chatbot-open', embedOpen);
      }
      elemIframeTooltip.style.cssText = 'opacity:0;';

      divTooltipIframe.style.cssText = styleDivTooltip + 'pointer-events: none;';
      setTimeout(() => {
        divTooltipIframe.style.cssText = 'display:none;';
      }, 500);
      if (embedOpen) {
        // Asynchronously loading the iframe to prevent unnecessary loading
        // Check if the chatbot iframe is not present, and append it if not
        if (!document.querySelector('#dante_chatbot_iframe')) {
          divChat.appendChild(elemIframe);
        }

        divChat.style.cssText = styleOpen;

        // closeChatBubble.style.cssText = styleCloseChatBubble + 'opacity:1; pointer-events:auto;';
        if (vw < 480) {
          document.body.style['overflow'] = 'hidden';
        }
      } else {
        divChat.style.cssText = styleBaseDivChat;
        // closeChatBubble.style.cssText = styleCloseChatBubble + 'display:none; opacity:0;';
        setTimeout(() => {
          divChat.style.cssText = 'display:none; opacity:0;';
        }, 500);
        if (vw < 480) {
          document.body.style['overflow'] = 'unset';
        }
      }
    };

    const imgHover = () => {
      // Apply hover style while preserving the animation class
      elemImg.style.cssText = styleImgHover;
    };
    const imgDefault = () => {
      // Apply default style while preserving the animation class
      elemImg.style.cssText = styleImgDefault;
    };

    const divTooltipIframe = document.createElement('div');
    const divChat = document.createElement('div');
    const elemIframeTooltip = document.createElement('iframe');
    const elemIframeThirdParty = document.createElement('iframe');

    // elemIframeTooltip.src = window.danteEmbed
    //   .replace('chat.helppi-ai.fi', 'app.dante-ai.com')
    //   .replace('/embed/avatar/?', '/embed/tooltips/?');
    // elemIframeTooltip.title = 'Dante AI Prompts';
    // elemIframeTooltip.style.cssText = styleBaseTooltip;
    // divTooltipIframe.style.cssText = styleDivTooltip;
    // divTooltipIframe.appendChild(elemIframeTooltip);
    elemIframeThirdParty.src = `${embedUrl.origin}/thirdparty.html`;
    elemIframeThirdParty.style = 'display:none; opacity:0;';

    // elemIframeTooltip.addEventListener('click', () => {
    //   toggleEmbed();
    // });

    const elemImg = document.createElement('img');
    elemImg.src =
      'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
    elemImg.alt = 'Dante Chatbot Icon';

    // Add the animation class to the image element when it's created
    elemImg.classList.add('animate-fadeIn');

    elemImg.style.cssText = styleImgDefault;
    elemImg.addEventListener('click', () => {
      toggleEmbed();
    });
    elemImg.addEventListener('mouseover', imgHover);
    elemImg.addEventListener('mouseout', imgDefault);
    elemImg.classList.add('dante-skeleton');
    elemImg.classList.add('dante-embed-icon');
    const elemIframe = document.createElement('iframe');
    elemIframe.id = 'dante_chatbot_iframe';
    elemIframe.src = cleanedURL;
    elemIframe.title = 'Dante AI Avatar';
    elemIframe.allow = 'clipboard-write *; microphone *';
    elemIframe.style.cssText = chatIframeStyle;
    divChat.style.cssText = styleBaseDivChat;

    fetch(
      `https://api.dante-ai.com/knowledge-bases/customization/shared?kb_id=${embedUrl.searchParams.get(
        'kb_id'
      )}&token=${embedUrl.searchParams.get('token')}`
    )
      .then((response) => response.json())
      .then((data) => {
        if (data?.show_tooltips) {
          if (shouldShow) {
            document.body.appendChild(divTooltipIframe);
          }
        }

        document.body.appendChild(elemIframeThirdParty);
        const embedIcon = document.querySelectorAll('img.dante-embed-icon')[0];

        if (data.chatbot_profile_pic !== undefined && data.chatbot_profile_pic !== null) {
          // Set the image source
          embedIcon.src = data.chatbot_profile_pic;
          embedIcon.classList.remove('dante-skeleton');
        } else {
          // Set the default image source
          embedIcon.src = 'https://chat.dante-ai.com/btn-embed.png';
          embedIcon.classList.remove('dante-skeleton');
        }
      })
      .catch(() => {
        // Handle error silently
      });

    const pulseIn = () => {
      // Apply pulse style while preserving the animation class
      elemImg.style.cssText = styleImgPulse;
      setTimeout(() => pulseOut(), 1500);
    };

    const pulseOut = () => {
      // Apply default style while preserving the animation class
      elemImg.style.cssText = styleImgDefault;
      setTimeout(() => pulseIn(), 1500);
    };

    // divChat.appendChild(closeChatBubble);
    if (shouldShow) {
      document.body.appendChild(elemImg);
      document.body.appendChild(divChat);
    }

    if (openByDefault === 'true') {
      setTimeout(() => toggleEmbed(), 1000);
    }

    const handleThirdPartyEvent = (event) => {
      if (event.data.eventData === 'allow_third_party_cookie') {
        cookiesAllowed = true;
        if (localStorage.getItem('embed-chatbot-open') === 'true') {
          const lastInteraction = new Date(localStorage.getItem('embed-chatbot-last-interaction'));
          const interactionTime = lastInteraction.getTime() + 5 * 60000;
          const currentTime = new Date().getTime();

          if (interactionTime > currentTime) {
            setTimeout(() => toggleEmbed(true), 1000);
          }
        }
        cookiesEventData = event.data;
      }
    };

    let promptClickEvent = {};

    const handleMessages = (event) => {
      switch (event.data.eventType) {
        case 'closeAvatar':
          toggleEmbed();
          break;
        case 'tooltipCloseClick':
          divTooltipIframe.style.cssText = 'display:none;';
          break;
        case 'siteReady':
          const chatIframe = document.querySelector('#dante_chatbot_iframe');
          chatIframe.contentWindow.postMessage(cookiesEventData, '*');

          if (Object.keys(promptClickEvent).length > 0 && !hasSentPrompt) {
            hasSentPrompt = true;
            chatIframe.contentWindow.postMessage(promptClickEvent, '*');
            promptClickEvent = {};
          }
          break;
        case 'promptClick':
          toggleEmbed();
          promptClickEvent = {
            eventType: 'promptClick',
            eventData: event.data.eventData
          };

          break;
        case 'closeChatBubble':
          let val = event.data.eventData;
          let chatHeight;

          if (val) {
            chatHeight = '450px';
          } else {
            chatHeight = vw > 600 ? '612px' : 'calc(100% - 20px)';
          }

          divChat.style.cssText = `${styleBaseDivChat}height:${chatHeight}; opacity:1;pointer-events:auto; `;
          break;
        case 'tooltipClick':
          toggleEmbed();
          break;
        case 'bubbleInteractionDate':
          if (cookiesAllowed) {
            localStorage.setItem('embed-chatbot-last-interaction', event.data.eventData);
          }
          break;
        case 'thirdparty':
          handleThirdPartyEvent(event);
          break;
        case 'tooltipContainerHeight':
          const tooltipIframe = document.querySelector('#dante_tooltip_iframe');
          if (tooltipIframe) {
            tooltipIframe.style.height = event.data.eventData + 'px';
          }
          break;
        default:
          break;
      }
    };

    pulseIn();
    window.addEventListener('message', handleMessages);
  };

  const start = (t) => {
    return (
      document.attachEvent ? 'complete' === document.readyState : 'loading' !== document.readyState
    )
      ? t()
      : document.addEventListener('DOMContentLoaded', t);
  };

  start(mountEmbed);
})();
