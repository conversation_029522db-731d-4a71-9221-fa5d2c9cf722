import { createServer as createViteServer } from 'vite';
import express from 'express';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';


const __dirname = dirname(fileURLToPath(import.meta.url));

async function createServer() {
  const app = express();

  // Initialize Vite in middleware mode
  const vite = await createViteServer({
    server: {
      middlewareMode: true,
      host: true,
    },
    appType: 'custom',
  });

  app.use(vite.middlewares);

  // Preload HTML files
  let shareTemplate, indexTemplate, embedTemplate;
  try {
    shareTemplate = await fs.readFile(join(__dirname, 'share.html'), 'utf-8');
    indexTemplate = await fs.readFile(join(__dirname, 'index.html'), 'utf-8');
    embedTemplate = await fs.readFile(join(__dirname, 'embed.html'), 'utf-8');
  } catch (err) {
    console.error('Error reading HTML templates:', err);
    process.exit(1); // Exit if templates are not available
  }

  // Utility function for rendering HTML with Vite
  async function renderHtml(req, res, template) {
    try {
      const html = await vite.transformIndexHtml(req.url, template);
      res.status(200).send(html);
    } catch (err) {
      console.error('Error transforming HTML:', err);
      res.status(500).send('Internal Server Error');
    }
  }

  // Route for /share/*
  app.get('/share/*', (req, res) => {
    renderHtml(req, res, shareTemplate);
  });

  // Route for /embed/*
  app.get('/embed/*', (req, res) => {
    renderHtml(req, res, embedTemplate);
  });

  // Catch-all route
  app.get('*', (req, res) => {
    renderHtml(req, res, indexTemplate);
  });

  // Start the server
  const port = 3000;
  app.listen(port, '0.0.0.0', () => {
    console.log(`Server running at http://localhost:${port}`);
  });

}

createServer().catch((err) => {
  console.error('Error starting server:', err);
  process.exit(1);
});
