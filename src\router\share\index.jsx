import DLoading from '@/components/DLoading';
import ShareConversation from '@/pages/ShareConversation';
import { lazy, Suspense } from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
const ErrorPage = lazy(() => import('@/pages/Error'));

/** @type {import('react-router-dom').RouteObject[]} */
const sharedRoutes = [
  {
    path: '/',
    errorElement: (
      <>
        <ErrorPage />
      </>
    ),
    element: (
      <>
        <ShareConversation />
      </>
    ),
  },
];

const routes = [...sharedRoutes];

const router = createBrowserRouter(routes, { basename: '/share' });

const Routers = () => {
  return (
    <Suspense fallback={<DLoading show={true} />}>
      <RouterProvider router={router} />
    </Suspense>
  );
};
export default Routers;
