.step_loader {
  width: 18px;
  aspect-ratio: 1;
  display: grid;
  border-radius: 50%;
  background: linear-gradient(0deg, #8275f7 30%, #0000 0 70%, #8275f7 0) 50%/8% 100%,
    linear-gradient(90deg, #8275f7 30%, #0000 0 70%, #8275f7 0) 50%/100% 8%;
  background-repeat: no-repeat;
  animation: l23 1s infinite steps(6);
}
.step_loader::before,
.step_loader::after {
  content: '';
  grid-area: 1/1;
  border-radius: 50%;
  background: inherit;
  opacity: 0.915;
  transform: rotate(30deg);
}
.step_loader::after {
  opacity: 0.83;
  transform: rotate(60deg);
}
@keyframes l23 {
  100% {
    transform: rotate(1turn);
  }
}
