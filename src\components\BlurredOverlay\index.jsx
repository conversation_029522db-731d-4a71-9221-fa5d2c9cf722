import React from 'react';

const BlurredOverlay = ({ show, children, className, height }) => {
  const handleInteraction = (e) => {
    if (show) {
      e.stopPropagation();
      e.preventDefault();
    }
  };

  return (
    <div className={`blurred-overlay relative w-full flex flex-col  ${height ? height : ''}`} onClick={handleInteraction}>
      <div className={`${show ? 'blur-sm' : ''} ${className ? className : ''}`}>{children}</div>
      {show && (
        <div
          className="absolute inset-0 bg-transparent z-10"
          onClick={handleInteraction}
          onMouseDown={handleInteraction}
          onTouchStart={handleInteraction}
        />
      )}
    </div>
  );
};

export default BlurredOverlay;
